[2025-06-03 10:58:38] This is codeql execute queries -J-Xmx1374M --verbosity=progress --logdir=E:\advance_javascript\codeQL\7\python-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\7\python-db\results -- E:\advance_javascript\codeQL\7\python-db\db-python queries/python-problm-1.ql
[2025-06-03 10:58:38] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --format=json -- queries/python-problm-1.ql
[2025-06-03 10:58:38] [PROGRESS] resolve queries> Recording pack reference (anonymous QL pack at E:\advance_javascript\codeQL\7\queries\python-problm-1.ql) at E:\advance_javascript\codeQL\7\queries.
[2025-06-03 10:58:38] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\queries\\python-problm-1.ql"
                      ]
[2025-06-03 10:58:38] Refusing fancy output: The terminal is not an xterm: 
[2025-06-03 10:58:38] Creating executor with 1 threads.
[2025-06-03 10:58:39] Calling plumbing command: codeql resolve extensions --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations queries/python-problm-1.ql
[2025-06-03 10:58:39] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --allow-library-packs --format startingpacks -- queries/python-problm-1.ql
[2025-06-03 10:58:39] [PROGRESS] resolve queries> Recording pack reference (anonymous QL pack at E:\advance_javascript\codeQL\7\queries\python-problm-1.ql) at E:\advance_javascript\codeQL\7\queries.
[2025-06-03 10:58:39] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\queries"
                      ]
[2025-06-03 10:58:39] Calling plumbing command: codeql resolve extensions-by-pack --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\7\queries
[2025-06-03 10:58:39] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] <anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}
[2025-06-03 10:58:39] [SPAMMY] resolve extensions-by-pack> [DERIVATION] <anonymous pack>: 0.0.0 {<anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}}
[2025-06-03 10:58:39] [SPAMMY] resolve extensions-by-pack> [DECISION 1] <anonymous pack>: 0.0.0
[2025-06-03 10:58:39] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : { },
                        "threatModels" : { },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 10:58:39] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : { },
                        "threatModels" : { },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 10:58:39] Calling plumbing command: codeql resolve library-path --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --query=E:\advance_javascript\codeQL\7\queries\python-problm-1.ql --format=json
[2025-06-03 10:58:39] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:58:39] [DETAILS] resolve library-path> Found no pack; trying after symlink resolution with E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:58:39] [DETAILS] resolve library-path> Found enclosing pack 'queries' at E:\advance_javascript\codeQL\7\queries.
[2025-06-03 10:58:39] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-03 10:58:39] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:58:39] [SPAMMY] resolve library-path> [INCOMPATIBILITY] <anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}
[2025-06-03 10:58:39] [SPAMMY] resolve library-path> [DERIVATION] <anonymous pack>: 0.0.0 {<anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}}
[2025-06-03 10:58:39] [SPAMMY] resolve library-path> [DECISION 1] <anonymous pack>: 0.0.0
[2025-06-03 10:58:39] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\7\queries resolved OK.
[2025-06-03 10:58:39] [DETAILS] resolve library-path> Found no dbscheme through dependencies.
[2025-06-03 10:58:39] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\7\\queries"
                        ],
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "queries\\python-problm-1.ql",
                        "possibleAdvice" : "There should probably be a qlpack.yml file declaring dependencies in E:\\advance_javascript\\codeQL\\7\\queries or an enclosing directory.",
                        "qlPackName" : "queries"
                      }
[2025-06-03 10:58:39] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:58:39] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:58:40] Resolved file set for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql hashes to 77669a17ba1632df3d5a4ef473f55b8c.
[2025-06-03 10:58:40] [DETAILS] execute queries> Checking QL for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:58:40] Stale frontend caches are invalidated based on import graph reachability.
[2025-06-03 10:58:40] ExternalModuleBindingPass ...
[2025-06-03 10:58:40] ExternalModuleBindingPass time: 00:00.001
[2025-06-03 10:58:40] CollectInstantiationsPass ...
[2025-06-03 10:58:40] CollectInstantiationsPass time: 00:00.010
[2025-06-03 10:58:40] Ql checks ...
[2025-06-03 10:58:40] Ql checks time: 00:00.064
[2025-06-03 10:58:40] [ERROR] execute queries> ERROR: could not resolve module python (E:\advance_javascript\codeQL\7\queries\python-problm-1.ql:9,8-14)
[2025-06-03 10:58:40] [ERROR] execute queries> ERROR: could not resolve type CallExpr (E:\advance_javascript\codeQL\7\queries\python-problm-1.ql:11,6-14)
[2025-06-03 10:58:40] [ERROR] execute queries> ERROR: could not resolve type Name (E:\advance_javascript\codeQL\7\queries\python-problm-1.ql:13,19-23)
[2025-06-03 10:58:40] Sequence stamp origin is -6042295201083183363
[2025-06-03 10:58:40] Pausing evaluation to close the cache at sequence stamp o+0
[2025-06-03 10:58:40] The disk cache is freshly trimmed; leave it be.
[2025-06-03 10:58:40] Unpausing evaluation
[2025-06-03 10:58:40] Exiting with code 2
