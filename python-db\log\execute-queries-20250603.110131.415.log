[2025-06-03 11:01:31] This is codeql execute queries -J-Xmx1374M --verbosity=progress --logdir=E:\advance_javascript\codeQL\7\python-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\7\python-db\results -- E:\advance_javascript\codeQL\7\python-db\db-python queries/python-problm-1.ql
[2025-06-03 11:01:31] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --format=json -- queries/python-problm-1.ql
[2025-06-03 11:01:31] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 11:01:31] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\queries\\python-problm-1.ql"
                      ]
[2025-06-03 11:01:31] Refusing fancy output: The terminal is not an xterm: 
[2025-06-03 11:01:31] Creating executor with 1 threads.
[2025-06-03 11:01:32] Calling plumbing command: codeql resolve extensions --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations queries/python-problm-1.ql
[2025-06-03 11:01:32] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --allow-library-packs --format startingpacks -- queries/python-problm-1.ql
[2025-06-03 11:01:32] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 11:01:32] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7"
                      ]
[2025-06-03 11:01:32] Calling plumbing command: codeql resolve extensions-by-pack --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\7
[2025-06-03 11:01:32] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}
[2025-06-03 11:01:32] [SPAMMY] resolve extensions-by-pack> [DERIVATION] python-security-queries: 1.0.0 {python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 1] python-security-queries: 1.0.0
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/python-all: * [*] {python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/python-all: 4.0.7
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 11:01:33] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 11:01:33] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\ext\default-threat-models-fixup.model.yml.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 11:01:33] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Asyncpg.model.yml.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 5 tuples.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 6 tuples.
[2025-06-03 11:01:33] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Stdlib.model.yml.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 12 tuples.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 1 tuples.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 66 tuples.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 11:01:33] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\empty.model.yml.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 0 tuples.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 0 tuples.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 0 tuples.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 11:01:33] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 11:01:34] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\subclass-capture\ALL.model.yml.
[2025-06-03 11:01:34] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 58275 tuples.
[2025-06-03 11:01:34] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-03 11:01:34] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 11:01:34] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-03 11:01:34] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-03 11:01:34] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-03 11:01:34] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-03 11:01:34] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-03 11:01:34] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=7+1+2+1+2",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=20+4+1*2+2+1",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4+2+1+2+1*2+4+2",
                                "columnNumbers" : "A=9*12"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=29",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=37+1+2+4+2*2+4+2*3+1+2+1+2+1+2+4+2+4+2*2+3+2*2+3+1+2*4+4+1+4+1+4+1*5+2*4+4+1+2*11+3+2+3+4+1+2*2+1+2",
                                "columnNumbers" : "A=9*66"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+3*58274",
                                "columnNumbers" : "A=5*58275"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 11:01:34] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=7+1+2+1+2",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=20+4+1*2+2+1",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4+2+1+2+1*2+4+2",
                                "columnNumbers" : "A=9*12"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=29",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=37+1+2+4+2*2+4+2*3+1+2+1+2+1+2+4+2+4+2*2+3+2*2+3+1+2*4+4+1+4+1+4+1*5+2*4+4+1+2*11+3+2+3+4+1+2*2+1+2",
                                "columnNumbers" : "A=9*66"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+3*58274",
                                "columnNumbers" : "A=5*58275"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 11:01:35] Calling plumbing command: codeql resolve library-path --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --query=E:\advance_javascript\codeQL\7\queries\python-problm-1.ql --format=json
[2025-06-03 11:01:35] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 11:01:35] [DETAILS] resolve library-path> Found enclosing pack 'python-security-queries' at E:\advance_javascript\codeQL\7.
[2025-06-03 11:01:35] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-03 11:01:35] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\7\qlpack.yml.
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] python-security-queries: 1.0.0 {python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 1] python-security-queries: 1.0.0
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] codeql/python-all: * [*] {python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 2] codeql/python-all: 4.0.7
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] codeql/yaml: * [*] {codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] codeql/xml: * [*] {codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] codeql/util: * [*] {codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] codeql/tutorial: * [*] {codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] codeql/threat-models: * [*] {codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] codeql/regex: * [*] {codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] codeql/mad: * [*] {codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] codeql/dataflow: * [*] {codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] codeql/typetracking: * [*] {codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DERIVATION] codeql/ssa: * [*] {codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 11:01:35] [SPAMMY] resolve library-path> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 11:01:35] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\7 resolved OK.
[2025-06-03 11:01:35] [DETAILS] resolve library-path> Found dbscheme through QL packs: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmlecode.python.dbscheme.
[2025-06-03 11:01:35] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\dataflow\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\mad\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\regex\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\ssa\\1.1.2",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\tutorial\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\typetracking\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\xml\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\yaml\\1.0.23"
                        ],
                        "dbscheme" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmlecode.python.dbscheme",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "python-security-queries\\queries\\python-problm-1.ql",
                        "qlPackName" : "python-security-queries"
                      }
[2025-06-03 11:01:35] [PROGRESS] execute queries> [1/1] No need to rerun E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 11:01:35] [PROGRESS] execute queries> Shutting down query evaluator.
[2025-06-03 11:01:35] Sequence stamp origin is -6042294458907225387
[2025-06-03 11:01:35] Pausing evaluation to close the cache at sequence stamp o+0
[2025-06-03 11:01:35] The disk cache is freshly trimmed; leave it be.
[2025-06-03 11:01:35] Unpausing evaluation
[2025-06-03 11:01:35] Exiting with code 0
