/**
 * @name Custom Hacker Paradise Detection (Refined)
 * @description Detects the specific vulnerability pattern "I am hacker's paradise" while avoiding false positives
 * @kind problem
 * @problem.severity error
 * @id js/custom-hacker-paradise-refined
 */

import javascript

from StringLiteral str, string varName
where
  str.getValue() = "I am hacker's paradise" and
  // Get the variable name if this string is assigned to a variable
  (
    exists(AssignExpr assign | 
      assign.getRhs() = str and
      assign.getLhs().(VarRef).getName() = varName
    ) or
    exists(VariableDeclarator decl |
      decl.getInit() = str and
      decl.getBindingPattern().(VarRef).getName() = varName
    )
  ) and
  // Exclude false positives - legitimate variable names
  not varName.toLowerCase().matches("%help%") and
  not varName.toLowerCase().matches("%prompt%") and
  not varName.toLowerCase().matches("%message%") and
  not varName.toLowerCase().matches("%text%") and
  not varName.toLowerCase().matches("%documentation%") and
  not varName.toLowerCase().matches("%example%")
select str, "Confirmed security vulnerability: Variable '" + varName + "' contains hacker backdoor pattern."
