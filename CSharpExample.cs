using System;
using System.Data.SqlClient;
using System.Web;
using System.Security.Cryptography;
using System.Text;

namespace SecurityVulnerabilities
{
    public class VulnerableCode
    {
        // Hardcoded credentials - security vulnerability
        private static string DatabasePassword = "MySecretPassword123";
        private static string ApiKey = "sk-1234567890abcdef";
        private static string ConnectionString = "Server=localhost;Database=MyDB;User Id=admin;Password=hardcoded123;";
        
        // SQL Injection vulnerability
        public void GetUserData(string userId)
        {
            string query = "SELECT * FROM Users WHERE UserId = '" + userId + "'"; // Vulnerable to SQL injection
            using (SqlConnection connection = new SqlConnection(ConnectionString))
            {
                SqlCommand command = new SqlCommand(query, connection);
                connection.Open();
                var result = command.ExecuteScalar(); // Executing unsafe query
            }
        }
        
        // Another SQL injection example
        public void LoginUser(string username, string password)
        {
            string sql = $"SELECT * FROM Users WHERE Username = '{username}' AND Password = '{password}'";
            // This is vulnerable to SQL injection
        }
        
        // Unsafe eval-like functionality
        public void ProcessUserInput(string userCode)
        {
            // This would be dangerous if it existed in C#
            // eval(userCode); // Not available in C# but similar dangerous patterns exist
            
            // Dangerous: Dynamic code compilation
            var code = "return " + userCode + ";"; // User input directly in code
        }
        
        // Weak cryptography
        public string WeakHash(string input)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.ASCII.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes); // MD5 is cryptographically weak
                return Convert.ToHexString(hashBytes);
            }
        }
        
        // More hardcoded secrets
        private const string JWT_SECRET = "my-jwt-secret-key";
        private const string ENCRYPTION_KEY = "32-char-encryption-key-value!!";
        private readonly string _apiToken = "ghp_xxxxxxxxxxxxxxxxxxxx";
        
        // Safe examples (should not be flagged)
        private string passwordPrompt = "Enter your password:";
        private string userInput = "";
        
        public void SafeQuery(string userId)
        {
            string query = "SELECT * FROM Users WHERE UserId = @userId"; // Parameterized query - safe
            using (SqlConnection connection = new SqlConnection(ConnectionString))
            {
                SqlCommand command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@userId", userId); // Safe parameterized query
                connection.Open();
                var result = command.ExecuteScalar();
            }
        }
    }
}
