[2025-06-03 10:42:18] This is codeql database analyze python-db queries/python-problm-1.ql --format=csv --output=results.csv --search-path=%CODEQL_HOME%
[2025-06-03 10:42:18] Log file was started late.
[2025-06-03 10:42:18] [PROGRESS] database analyze> Running queries.
[2025-06-03 10:42:18] Running plumbing command: codeql database run-queries --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=E:\advance_javascript\codeQL\7\%CODEQL_HOME% --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --no-rerun -- E:\advance_javascript\codeQL\7\python-db queries/python-problm-1.ql
[2025-06-03 10:42:18] Calling plumbing command: codeql resolve ram --dataset=E:\advance_javascript\codeQL\7\python-db\db-python --format=json
[2025-06-03 10:42:18] [PROGRESS] resolve ram> Stringpool size measured as 2154906
[2025-06-03 10:42:18] Plumbing command codeql resolve ram completed:
                      [
                        "-J-Xmx1374M"
                      ]
[2025-06-03 10:42:18] Spawning plumbing command: execute queries -J-Xmx1374M --verbosity=progress --logdir=E:\advance_javascript\codeQL\7\python-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=E:\advance_javascript\codeQL\7\%CODEQL_HOME% --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\7\python-db\results -- E:\advance_javascript\codeQL\7\python-db\db-python queries/python-problm-1.ql
[2025-06-03 10:42:20] [ERROR] Spawned process exited abnormally (code 2; tried to run: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\java\bin\java.exe, -Xmx1374M, "-Djava.library.path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\java\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\MongoDB\Server\7.0\bin;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\Git\cmd;C:\Program Files\MongoDB\Tools\100\bin;C:\ngrok;C:\Program Files\Docker\Docker\resources\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;c:\users\<USER>\.local\bin;c:\users\<USER>\appdata\roaming\python\python313\scripts;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;.", -cp, C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\codeql.jar, com.semmle.cli2.CodeQL, env_argv, execute, queries, -J-Xmx1374M, _, _, _, _, _, _, _, --no-rerun, _, --, _, queries/python-problm-1.ql])
[2025-06-03 10:42:20] Plumbing command codeql execute queries terminated with status 2.
[2025-06-03 10:42:20] Plumbing command codeql database run-queries completed with status 2.
[2025-06-03 10:42:20] Exiting with code 2
