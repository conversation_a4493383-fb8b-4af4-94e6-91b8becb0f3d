[2025-06-03 18:22:13] This is codeql execute queries -J-Xmx1375M --verbosity=progress --logdir=E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\results -- E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\db-javascript queries\hardcoded-credentials.ql
[2025-06-03 18:22:13] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --format=json -- queries\hardcoded-credentials.ql
[2025-06-03 18:22:13] [PROGRESS] resolve queries> Recording pack reference nodejs-security-queries at E:\advance_javascript\codeQL\7\multi_purpose_project\queries.
[2025-06-03 18:22:13] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries\\hardcoded-credentials.ql"
                      ]
[2025-06-03 18:22:13] Refusing fancy output: The terminal is not an xterm: 
[2025-06-03 18:22:13] Creating executor with 1 threads.
[2025-06-03 18:22:14] Calling plumbing command: codeql resolve extensions --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --include-extension-row-locations queries\hardcoded-credentials.ql
[2025-06-03 18:22:14] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --allow-library-packs --format startingpacks -- queries\hardcoded-credentials.ql
[2025-06-03 18:22:14] [PROGRESS] resolve queries> Recording pack reference nodejs-security-queries at E:\advance_javascript\codeQL\7\multi_purpose_project\queries.
[2025-06-03 18:22:14] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries"
                      ]
[2025-06-03 18:22:14] Calling plumbing command: codeql resolve extensions-by-pack --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\7\multi_purpose_project\queries
[2025-06-03 18:22:14] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] nodejs-security-queries: not 1.0.0 {root: nodejs-security-queries@1.0.0}
[2025-06-03 18:22:14] [SPAMMY] resolve extensions-by-pack> [DERIVATION] nodejs-security-queries: 1.0.0 {nodejs-security-queries: not 1.0.0 {root: nodejs-security-queries@1.0.0}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] nodejs-security-queries: * [*], codeql/javascript-all: not * [*] {dependency: nodejs-security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 1] nodejs-security-queries: 1.0.0
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/javascript-all: * [*] {nodejs-security-queries: * [*], codeql/javascript-all: not * [*] {dependency: nodejs-security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 18:22:16] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\apollo-server.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\aws-sdk.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\axios.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\default-threat-models-fixup.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\hana-db-client.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 2 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 6 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\make-dir.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\markdown-table.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 1 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\mkdirp.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\open.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\react-relay-threat.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 10 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\rimraf.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\shelljs.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\tanstack.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 6 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\underscore.string.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 5 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 20 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NoSQL.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NodeJSLib.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\SQL.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\data\internal\empty.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 0 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 0 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 0 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:neutralModel: 0 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 0 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 0 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\helmet\Helmet.Required.Setting.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:requiredHelmetSecuritySetting: 2 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\minimongo\model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 75 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mongodb\model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 26 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 611 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 32 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 102 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mssql\model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 34 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mysql\model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 57 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\pg\model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 65 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 23 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sequelize\model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 7 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 248 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 2 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\spanner\model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 174 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 5 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sqlite3\model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 15 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\IntegrityCheckingRequired\integrity_checking_required.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isCdnDomainWithCheckingRequired: 3 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\compromised\compromised_domains.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 1 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\untrusted\untrusted_domains.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 6 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-03 18:22:16] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-03 18:22:16] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-03 18:22:16] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 18:22:16] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 18:22:16] Calling plumbing command: codeql resolve library-path --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --query=E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql --format=json
[2025-06-03 18:22:16] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql.
[2025-06-03 18:22:16] [DETAILS] resolve library-path> Found enclosing pack 'nodejs-security-queries' at E:\advance_javascript\codeQL\7\multi_purpose_project\queries.
[2025-06-03 18:22:16] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-03 18:22:16] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\7\multi_purpose_project\queries\qlpack.yml.
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] nodejs-security-queries: not 1.0.0 {root: nodejs-security-queries@1.0.0}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] nodejs-security-queries: 1.0.0 {nodejs-security-queries: not 1.0.0 {root: nodejs-security-queries@1.0.0}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] nodejs-security-queries: * [*], codeql/javascript-all: not * [*] {dependency: nodejs-security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 1] nodejs-security-queries: 1.0.0
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/javascript-all: * [*] {nodejs-security-queries: * [*], codeql/javascript-all: not * [*] {dependency: nodejs-security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 18:22:16] [SPAMMY] resolve library-path> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 18:22:16] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\7\multi_purpose_project\queries resolved OK.
[2025-06-03 18:22:16] [DETAILS] resolve library-path> Found dbscheme through QL packs: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmlecode.javascript.dbscheme.
[2025-06-03 18:22:16] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\dataflow\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\mad\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\regex\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\ssa\\1.1.2",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\tutorial\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\typetracking\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\xml\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\yaml\\1.0.23"
                        ],
                        "dbscheme" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmlecode.javascript.dbscheme",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "nodejs-security-queries\\hardcoded-credentials.ql",
                        "qlPackName" : "nodejs-security-queries"
                      }
[2025-06-03 18:22:16] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql.
[2025-06-03 18:22:16] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql.
[2025-06-03 18:22:17] Resolved file set for E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql hashes to 75a10e3ee81824593c407c5f05522d2d.
[2025-06-03 18:22:17] [DETAILS] execute queries> Compilation cache hit for E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql.
[2025-06-03 18:22:17] [SPAMMY] execute queries> No database upgrade/downgrade needed for E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql
[2025-06-03 18:22:17] [PROGRESS] execute queries> [1/1] Found in cache: E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql.
[2025-06-03 18:22:17] [PROGRESS] execute queries> Starting evaluation of nodejs-security-queries\hardcoded-credentials.ql.
[2025-06-03 18:22:17] Starting evaluation of E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql
[2025-06-03 18:22:17] (0s) Start query execution
[2025-06-03 18:22:17] (0s) Beginning execution of E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql
[2025-06-03 18:22:17] (0s)  >>> Created relation exprs/5@a1831agj with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:17] (0s) Inferred that exprs_10#join_rhs/2@27dbbd1m is empty, due to exprs/5@a1831agj.
[2025-06-03 18:22:17] (0s) Inferred that exprs_230#join_rhs/3@dfee24vq is empty, due to exprs/5@a1831agj.
[2025-06-03 18:22:17] (0s) Inferred that exprs_20#count_range/2@b5deeecs is empty, due to exprs/5@a1831agj.
[2025-06-03 18:22:17] (0s) Inferred that exprs_2#antijoin_rhs/1@6182e1q8 is empty, due to exprs/5@a1831agj.
[2025-06-03 18:22:17] (0s) Inferred that exprs_203#join_rhs/3@fc0e1186 is empty, due to exprs/5@a1831agj.
[2025-06-03 18:22:17] (0s) Inferred that exprs_032#join_rhs/3@30fda5lr is empty, due to exprs/5@a1831agj.
[2025-06-03 18:22:17] (0s) Inferred that Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@49fc67r8 is empty, due to exprs/5@a1831agj.
[2025-06-03 18:22:17] (0s) Inferred that exprs_12034#join_rhs/5@d7abdd0t is empty, due to exprs/5@a1831agj.
[2025-06-03 18:22:17] (0s) Inferred that _AST::AstNode.getParent/0#dispred#21e1cceb__Expr::ExprOrType#305a3d05_project#Expr::ExprOrType.getOw__#join_rhs/2@07b731kl is empty, due to exprs/5@a1831agj.
[2025-06-03 18:22:17] (0s) Inferred that exprs_0#antijoin_rhs/1@d222edj4 is empty, due to exprs/5@a1831agj.
[2025-06-03 18:22:17] (0s) Inferred that _exprs_10#join_rhs#antijoin_rhs/1@9fb58758 is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that _exprs_10#join_rhs#antijoin_rhs#1/1@73bbbbea is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that @varaccess/1@705690bs is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that @varref/1@5caaa75i is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that AST::AstNode.getNumChildExpr/0#dispred#a4cbe1e8#bf/2@0fb19cd8 is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Constants::SyntacticConstants::NullConstant#5b41c79d/1@63281f6a is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that _exprs_10#join_rhs#antijoin_rhs#2/1@c7a2d58t is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::Assignment#b84ed089/1@8db6e8ao is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::BinaryExpr#18c21d7d/1@49e1f1kg is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::DotExpr.getProperty/0#dispred#8bb313c1/2@c39c5f7j is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::ParExpr.getExpression/0#dispred#70638730/2@e596b40m is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::Property#e5a4273a/1@cd6f62vd is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that _exprs_10#join_rhs#antijoin_rhs#3/1@00044fe0 is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::UnaryExpr#56508658/1@a07fdb1d is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Templates::TemplateLiteral.getElement/1#dispred#9fd3da10#fbf#cpe#13/2@410b62m9 is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Constants::SyntacticConstants::PrimitiveLiteralConstant#303549d7/1@e4c7adrv is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that TypeScript::TypeAssertion#2fbd1ca1/1@c49b68tf is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that _exprs_10#join_rhs#antijoin_rhs#4/1@fd80deh5 is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that _exprs_10#join_rhs#antijoin_rhs#5/1@c9105fg2 is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Variables::BindingPattern#efe8ec12/1@97547da4 is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Constants::SyntacticConstants::UndefinedConstant#782564e3/1@614ed22h is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@87418wnp is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Constants::SyntacticConstants::SyntacticConstant#a9c29f7a/1@87418ynp is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@87418znp is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::getAnAddOperand/1#c448e049/2@935276c6 is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::getConstantString/1#359f8cc1/2@0a154d13 is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Constants::ConstantExpr#16c7d9a2/1@ae9906rq is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that xUnit::IndexExprIndexIsBracketedListOfExpressions#3ad78363/1@016ab9t0 is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::Assignment#b84ed089/1@16fa64dv is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::Literal#62045a1b/1@81201fg9 is empty, due to exprs_10#join_rhs/2@27dbbd1m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::Assignment.getRhs/0#dispred#e24981fc/2@5c1e54jf is empty, due to Expr::Assignment#b84ed089/1@8db6e8ao.
[2025-06-03 18:22:17] (0s) Inferred that Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@87418xnp is empty, due to exprs_032#join_rhs/3@30fda5lr.
[2025-06-03 18:22:17] (0s) Inferred that Expr::Assignment.getRhs/0#dispred#e24981fc/2@5545891e is empty, due to Expr::Assignment#b84ed089/1@16fa64dv.
[2025-06-03 18:22:17] (0s) Inferred that _exprs_20#count_range#join_rhs/2@51ef89vg is empty, due to exprs_20#count_range/2@b5deeecs.
[2025-06-03 18:22:17] (0s) Inferred that Expr::SeqExpr.getOperand/1#dispred#f959c4fa/3@03d48bc4 is empty, due to _exprs_10#join_rhs#antijoin_rhs#3/1@00044fe0.
[2025-06-03 18:22:17] (0s) Inferred that Constants::SyntacticConstants::UnaryConstant#7f985313/1@874180wp is empty, due to exprs_032#join_rhs/3@30fda5lr.
[2025-06-03 18:22:17] (0s) Inferred that Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11_10#join_rhs/2@7ebaf2da is empty, due to Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@49fc67r8.
[2025-06-03 18:22:17] (0s) Inferred that cached_Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@8b4514me is empty, due to Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@49fc67r8.
[2025-06-03 18:22:17] (0s) Inferred that Stmt::DeclStmt.getDecl/1#dispred#a1ae5d80/3@e7aedd0n is empty, due to exprs_12034#join_rhs/5@d7abdd0t.
[2025-06-03 18:22:17] (0s) Inferred that Variables::GlobalVarAccess#d9e3aaf5/1@7820c9rd is empty, due to @varaccess/1@705690bs.
[2025-06-03 18:22:17] (0s) Inferred that Variables::VarRef.getName/0#dispred#287a3fe0/2@0fab3191 is empty, due to @varref/1@5caaa75i.
[2025-06-03 18:22:17] (0s) Inferred that AST::AstNode.getNumChildExpr/0#dispred#a4cbe1e8#bf_10#join_rhs/2@904963u5 is empty, due to AST::AstNode.getNumChildExpr/0#dispred#a4cbe1e8#bf/2@0fb19cd8.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::SyntacticConstants::NullConstant#5b41c79d/1@b9abb1u7 is empty, due to Constants::SyntacticConstants::NullConstant#5b41c79d/1@63281f6a.
[2025-06-03 18:22:17] (0s) Inferred that Expr::DotExpr.getProperty/0#dispred#8bb313c1_10#join_rhs/2@37c998ai is empty, due to Expr::DotExpr.getProperty/0#dispred#8bb313c1/2@c39c5f7j.
[2025-06-03 18:22:17] (0s) Inferred that Expr::ParExpr.getExpression/0#dispred#70638730_10#join_rhs/2@f94b08m3 is empty, due to Expr::ParExpr.getExpression/0#dispred#70638730/2@e596b40m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::Property.getObjectExpr/0#dispred#fcf8662c/2@6070dd0b is empty, due to Expr::Property#e5a4273a/1@cd6f62vd.
[2025-06-03 18:22:17] (0s) Inferred that _AST::AstNode.getParent/0#dispred#21e1cceb_Expr::Property#e5a4273a__Expr::ExprOrType#305a3d05_projec__#join_rhs/2@619f36t1 is empty, due to Expr::Property#e5a4273a/1@cd6f62vd.
[2025-06-03 18:22:17] (0s) Inferred that Expr::SeqExpr.getNumOperands/0#dispred#86a3b8c5/2@cc5a6dto is empty, due to _exprs_10#join_rhs#antijoin_rhs#3/1@00044fe0.
[2025-06-03 18:22:17] (0s) Inferred that Templates::TemplateLiteral.getElement/1#dispred#9fd3da10#fbf#cpe#13_10#join_rhs/2@fc64f5ud is empty, due to Templates::TemplateLiteral.getElement/1#dispred#9fd3da10#fbf#cpe#13/2@410b62m9.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::SyntacticConstants::PrimitiveLiteralConstant#303549d7/1@5e66f2go is empty, due to Constants::SyntacticConstants::PrimitiveLiteralConstant#303549d7/1@e4c7adrv.
[2025-06-03 18:22:17] (0s) Inferred that Variables::Parameter#a0c1c8e9/1@c1fb94e7 is empty, due to Variables::BindingPattern#efe8ec12/1@97547da4.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::SyntacticConstants::UndefinedConstant#782564e3/1@d6f2510t is empty, due to Constants::SyntacticConstants::UndefinedConstant#782564e3/1@614ed22h.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@214e74k7 is empty, due to Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@87418wnp.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::SyntacticConstants::SyntacticConstant#a9c29f7a/1@2ba6ad9n is empty, due to Constants::SyntacticConstants::SyntacticConstant#a9c29f7a/1@87418ynp.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@41f1f0eb is empty, due to Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@87418znp.
[2025-06-03 18:22:17] (0s) Inferred that #Expr::getAnAddOperand/1#c448e049Plus/2@bb2097j3 is empty, due to Expr::getAnAddOperand/1#c448e049/2@935276c6.
[2025-06-03 18:22:17] (0s) Inferred that project#Expr::getAnAddOperand/1#c448e049/1@a4abe262 is empty, due to Expr::getAnAddOperand/1#c448e049/2@935276c6.
[2025-06-03 18:22:17] (0s) Inferred that Expr::getAnAddOperand/1#c448e049_10#higher_order_body/2@5797a6k7 is empty, due to Expr::getAnAddOperand/1#c448e049/2@935276c6.
[2025-06-03 18:22:17] (0s) Inferred that project#Expr::getConstantString/1#359f8cc1/1@69d17fpn is empty, due to Expr::getConstantString/1#359f8cc1/2@0a154d13.
[2025-06-03 18:22:17] (0s) Inferred that Expr::Expr.getStringValue/0#dispred#d7c72429/2@b6c11e34 is empty, due to Expr::getConstantString/1#359f8cc1/2@0a154d13.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::ConstantExpr#16c7d9a2/1@ed3d38s0 is empty, due to Constants::ConstantExpr#16c7d9a2/1@ae9906rq.
[2025-06-03 18:22:17] (0s) Inferred that Expr::Literal.getValue/0#dispred#c75e715e/2@806438ih is empty, due to Expr::Literal#62045a1b/1@81201fg9.
[2025-06-03 18:22:17] (0s) Inferred that Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs/2@1d8868bv is empty, due to Expr::Assignment.getRhs/0#dispred#e24981fc/2@5c1e54jf.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@6a405eg0 is empty, due to Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@87418xnp.
[2025-06-03 18:22:17] (0s) Inferred that m#Variables::BindingPattern.getName/0#dispred#38c8fb21#bf/1@28f864ju is empty, due to Expr::Assignment.getRhs/0#dispred#e24981fc/2@5545891e.
[2025-06-03 18:22:17] (0s) Inferred that project#Expr::SeqExpr.getOperand/1#dispred#f959c4fa/1@1d11ba32 is empty, due to Expr::SeqExpr.getOperand/1#dispred#f959c4fa/3@03d48bc4.
[2025-06-03 18:22:17] (0s) Inferred that Expr::SeqExpr.getOperand/1#dispred#f959c4fa_02#count_range/2@d58fd35m is empty, due to Expr::SeqExpr.getOperand/1#dispred#f959c4fa/3@03d48bc4.
[2025-06-03 18:22:17] (0s) Inferred that Expr::SeqExpr.getLastOperand/0#dispred#0179038c/2@fa2f171p is empty, due to Expr::SeqExpr.getNumOperands/0#dispred#86a3b8c5/2@cc5a6dto.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::SyntacticConstants::UnaryConstant#7f985313/1@6b76df2t is empty, due to Constants::SyntacticConstants::UnaryConstant#7f985313/1@874180wp.
[2025-06-03 18:22:17] (0s) Inferred that Stmt::DeclStmt.getDecl/1#dispred#a1ae5d80_120#join_rhs/3@1a34d0fj is empty, due to Stmt::DeclStmt.getDecl/1#dispred#a1ae5d80/3@e7aedd0n.
[2025-06-03 18:22:17] (0s) Inferred that Constants::SyntacticConstants::InfinityConstant#613f811d/1@074935lm is empty, due to Variables::GlobalVarAccess#d9e3aaf5/1@7820c9rd.
[2025-06-03 18:22:17] (0s) Inferred that Constants::SyntacticConstants::NaNConstant#91a69921/1@0cbe5418 is empty, due to Variables::GlobalVarAccess#d9e3aaf5/1@7820c9rd.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::DotExpr.getProperty/0#dispred#8bb313c1_10#join_rhs__Expr::ExprOrType#305a3d05_project#Expr::E__#join_rhs/2@8dcd69vq is empty, due to Expr::DotExpr.getProperty/0#dispred#8bb313c1_10#join_rhs/2@37c998ai.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::ParExpr.getExpression/0#dispred#70638730_10#join_rhs__Expr::ExprOrType#305a3d05_project#Expr:__#join_rhs/2@619dfbbb is empty, due to Expr::ParExpr.getExpression/0#dispred#70638730_10#join_rhs/2@f94b08m3.
[2025-06-03 18:22:17] (0s) Inferred that _#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeafs/1#729c3ae8__Expr::getAnAddOperand/__#shared/2@593e0af2 is empty, due to #Expr::getAnAddOperand/1#c448e049Plus/2@bb2097j3.
[2025-06-03 18:22:17] (0s) Inferred that _#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#3606cd2c_Locations::dbLocationInfo/6#a0__#shared/4@a12ed952 is empty, due to #Expr::getAnAddOperand/1#c448e049Plus/2@bb2097j3.
[2025-06-03 18:22:17] (0s) Inferred that boundedFastTC:Expr::getAnAddOperand/1#c448e049_10#higher_order_body:_project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_body/2@dc94704c is empty, due to Expr::getAnAddOperand/1#c448e049_10#higher_order_body/2@5797a6k7.
[2025-06-03 18:22:17] (0s) Inferred that _project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_body/1@4548cdas is empty, due to project#Expr::getConstantString/1#359f8cc1/1@69d17fpn.
[2025-06-03 18:22:17] (0s) Inferred that Constants::ConstantString#e55e40cc/1@6b166a7j is empty, due to Expr::Expr.getStringValue/0#dispred#d7c72429/2@b6c11e34.
[2025-06-03 18:22:17] (0s) Inferred that cached_Expr::Expr.getStringValue/0#dispred#d7c72429/2@a455d204 is empty, due to Expr::Expr.getStringValue/0#dispred#d7c72429/2@b6c11e34.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::Literal.getValue/0#dispred#c75e715e#antijoin_rhs/1@55a769b0 is empty, due to Expr::Literal.getValue/0#dispred#c75e715e/2@806438ih.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::Literal.getValue/0#dispred#c75e715e#antijoin_rhs#1/1@ca1789pf is empty, due to Expr::Literal.getValue/0#dispred#c75e715e/2@806438ih.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::Literal.getValue/0#dispred#c75e715e#antijoin_rhs#2/1@9e6ef1od is empty, due to Expr::Literal.getValue/0#dispred#c75e715e/2@806438ih.
[2025-06-03 18:22:17] (0s) Inferred that #select/2@ef4acao4 is empty, due to Expr::Literal.getValue/0#dispred#c75e715e/2@806438ih.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs__Expr::ExprOrType#305a3d05_project#Expr::Exp__#join_rhs/2@7f6c28s0 is empty, due to Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs/2@1d8868bv.
[2025-06-03 18:22:17] (0s) Inferred that m#Variables::VarRef#38254975#b/1@861b933h is empty, due to m#Variables::BindingPattern.getName/0#dispred#38c8fb21#bf/1@28f864ju.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::SeqExpr.getOperand/1#dispred#f959c4fa_02#count_range#join_rhs/2@7c9eb88l is empty, due to Expr::SeqExpr.getOperand/1#dispred#f959c4fa_02#count_range/2@d58fd35m.
[2025-06-03 18:22:17] (0s) Inferred that Expr::SeqExpr.getLastOperand/0#dispred#0179038c_10#join_rhs/2@f8db31c5 is empty, due to Expr::SeqExpr.getLastOperand/0#dispred#0179038c/2@fa2f171p.
[2025-06-03 18:22:17] (0s) Inferred that _Stmt::DeclStmt.getDecl/1#dispred#a1ae5d80_120#join_rhs__Expr::ExprOrType#305a3d05_project#Expr::Exp__#join_rhs/2@973438il is empty, due to Stmt::DeclStmt.getDecl/1#dispred#a1ae5d80_120#join_rhs/3@1a34d0fj.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::SyntacticConstants::InfinityConstant#613f811d/1@8a3941qn is empty, due to Constants::SyntacticConstants::InfinityConstant#613f811d/1@074935lm.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::SyntacticConstants::NaNConstant#91a69921/1@ffe46apv is empty, due to Constants::SyntacticConstants::NaNConstant#91a69921/1@0cbe5418.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeaf__#sum_range/3@04579836 is empty, due to _#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeafs/1#729c3ae8__Expr::getAnAddOperand/__#shared/2@593e0af2.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeaf__#sum_term/4@778679b5 is empty, due to _#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeafs/1#729c3ae8__Expr::getAnAddOperand/__#shared/2@593e0af2.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeaf__#antijoin_rhs/2@62b41d4j is empty, due to _#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeafs/1#729c3ae8__Expr::getAnAddOperand/__#shared/2@593e0af2.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#360__#concat_range/6@4ebea2lc is empty, due to _#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#3606cd2c_Locations::dbLocationInfo/6#a0__#shared/4@a12ed952.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#360__#concat_term/10@e5df30gj is empty, due to _#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#3606cd2c_Locations::dbLocationInfo/6#a0__#shared/4@a12ed952.
[2025-06-03 18:22:17] (0s) Inferred that __project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_b__#shared/1@d3c2d0lm is empty, due to boundedFastTC:Expr::getAnAddOperand/1#c448e049_10#higher_order_body:_project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_body/2@dc94704c.
[2025-06-03 18:22:17] (0s) Inferred that _#Expr::getAnAddOperand/1#c448e049Plus___project#Expr::getAnAddOperand/1#c448e049_project#Expr::getC__#antijoin_rhs/1@dccbf28s is empty, due to boundedFastTC:Expr::getAnAddOperand/1#c448e049_10#higher_order_body:_project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_body/2@dc94704c.
[2025-06-03 18:22:17] (0s) Inferred that cached_Constants::ConstantString#e55e40cc/1@43d594jb is empty, due to Constants::ConstantString#e55e40cc/1@6b166a7j.
[2025-06-03 18:22:17] (0s) Inferred that Variables::VarRef#38254975#b/1@639fdfd1 is empty, due to m#Variables::VarRef#38254975#b/1@861b933h.
[2025-06-03 18:22:17] (0s) Inferred that _#select_exprs_exprs_0#antijoin_rhs#shared/3@6d8a82fi is empty, due to #select/2@ef4acao4.
[2025-06-03 18:22:17] (0s) Inferred that __Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLea__#join_rhs/2@5bc876ld is empty, due to _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeaf__#sum_range/3@04579836.
[2025-06-03 18:22:17] (0s) Inferred that Expr::getConcatenatedString/1#d0ffb08b/2@6792082s is empty, due to _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#360__#concat_range/6@4ebea2lc.
[2025-06-03 18:22:17] (0s) Inferred that Expr::hasAllConstantLeafs/1#729c3ae8/1@eb5b3b4h is empty, due to __project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_b__#shared/1@d3c2d0lm.
[2025-06-03 18:22:17] (0s) Inferred that Variables::BindingPattern.getName/0#dispred#38c8fb21#bf/2@1051e2mq is empty, due to Variables::VarRef#38254975#b/1@639fdfd1.
[2025-06-03 18:22:17] (0s) Inferred that _Locations::getLocatableLocation/1#b180d129__#select_exprs_exprs_0#antijoin_rhs#shared#shared/4@09c299g5 is empty, due to _#select_exprs_exprs_0#antijoin_rhs#shared/3@6d8a82fi.
[2025-06-03 18:22:17] (0s) Inferred that #select#query/8@8c46c8ea is empty, due to _#select_exprs_exprs_0#antijoin_rhs#shared/3@6d8a82fi.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::getAnAddOperand/1#c448e049_10#higher_order_body_Expr::hasAllConstantLeafs/1#729c3ae8#antijoin_rhs/2@d14219me is empty, due to Expr::hasAllConstantLeafs/1#729c3ae8/1@eb5b3b4h.
[2025-06-03 18:22:17] (0s) Inferred that _Expr::hasAllConstantLeafs/1#729c3ae8__Expr::getAnAddOperand/1#c448e049_10#higher_order_body_Expr::h__#shared/1@d273e1rj is empty, due to Expr::hasAllConstantLeafs/1#729c3ae8/1@eb5b3b4h.
[2025-06-03 18:22:17] (0s) Inferred that Expr::SmallConcatRoot#3606cd2c/1@bcb9de59 is empty, due to Expr::hasAllConstantLeafs/1#729c3ae8/1@eb5b3b4h.
[2025-06-03 18:22:17] (0s) Inferred that _Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a__Locations::getLocatableLocation/1#b180d12__#antijoin_rhs/3@96a198h1 is empty, due to _Locations::getLocatableLocation/1#b180d129__#select_exprs_exprs_0#antijoin_rhs#shared#shared/4@09c299g5.
[2025-06-03 18:22:17] (0s)  >>> Created relation typeexprs/5@5ef91d9v with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:17] (0s) Inferred that typeexprs_0#antijoin_rhs/1@e1d4c0cm is empty, due to typeexprs/5@5ef91d9v.
[2025-06-03 18:22:17] (0s) Inferred that typeexprs_10#join_rhs/2@af1f3114 is empty, due to typeexprs/5@5ef91d9v.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate CachedStages::Stages::Ast::backref/0#7ac52a97/0@dab1424s
[2025-06-03 18:22:18] (0s)  >>> Created relation CachedStages::Stages::Ast::backref/0#7ac52a97/0@dab1424s with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate CachedStages::Stages::Ast::ref/0#8de2cf1f/0@72b8f244
[2025-06-03 18:22:18] (0s)  >>> Created relation CachedStages::Stages::Ast::ref/0#8de2cf1f/0@72b8f244 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 18:22:18] (0s) No need to promote strings for predicate CachedStages::Stages::Ast::ref/0#8de2cf1f  as it does not contain computed strings.
[2025-06-03 18:22:18] (0s)  >>> Created relation cached_CachedStages::Stages::Ast::ref/0#8de2cf1f/0@c876115v with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 18:22:18] (0s)  >>> Created relation files/2@ec93749g with 1 rows and digest 52c0ad7mh3tofdclbbqkmifrfn8.
[2025-06-03 18:22:18] (0s)  >>> Created relation locations_default/6@2b5cc3pr with 12 rows and digest 68c79cf8it6tvavp4d8d9f8vlre.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate Locations::TDbLocation#dom#677b8f15/1@d1d6f28v
[2025-06-03 18:22:18] (0s)  >>> Created relation Locations::TDbLocation#dom#677b8f15/1@d1d6f28v with 12 rows and digest ad4b67frt8krvgc14m5evcpgaa8.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate num#Locations::TDbLocation#3d63300d/2@f9f789sa
[2025-06-03 18:22:18] Evaluating HOP construct<Locations#78f633f3::TLocation,0> with inputs:
                        12 tuples in Locations::TDbLocation#dom#677b8f15/1@d1d6f28v
[2025-06-03 18:22:18] (0s)  >>> Created relation num#Locations::TDbLocation#3d63300d/2@f9f789sa with 12 rows and digest d5989el075m17d1icjfskqt0691.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate Locations::dbLocationInfo/6#a08cdee9/6@9991d8mh
[2025-06-03 18:22:18] (0s)  >>> Created relation Locations::dbLocationInfo/6#a08cdee9/6@9991d8mh with 12 rows and digest 7d37889q87a4u5cntkfv7keicr7.
[2025-06-03 18:22:18] (0s) No need to promote strings for predicate Locations::dbLocationInfo/6#a08cdee9  as it does not contain computed strings.
[2025-06-03 18:22:18] (0s)  >>> Created relation cached_Locations::dbLocationInfo/6#a08cdee9/6@a72875je with 12 rows and digest 7d37889q87a4u5cntkfv7keicr7.
[2025-06-03 18:22:18] (0s)  >>> Created relation yaml_locations/2@b504eb06 with 11 rows and digest a858b410gfipcbkcrblo7hn966b.
[2025-06-03 18:22:18] (0s)  >>> Created relation json_locations/2@5eb54fmu with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s)  >>> Created relation xmllocations/2@45e0083e with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s)  >>> Created relation hasLocation/2@6de756q0 with 1 rows and digest d363a7nfjdeo998ps4qvokrqed2.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate Locations::getLocatableLocation/1#b180d129/2@d423140t
[2025-06-03 18:22:18] (0s)  >>> Created relation Locations::getLocatableLocation/1#b180d129/2@d423140t with 12 rows and digest 754f5f65k070bi0e9bf5g3q35bc.
[2025-06-03 18:22:18] (0s) No need to promote strings for predicate Locations::getLocatableLocation/1#b180d129  as it does not contain computed strings.
[2025-06-03 18:22:18] (0s)  >>> Created relation cached_Locations::getLocatableLocation/1#b180d129/2@d8dc0a2h with 12 rows and digest 754f5f65k070bi0e9bf5g3q35bc.
[2025-06-03 18:22:18] (0s)  >>> Created relation guard_node/3@b8d0317q with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s)  >>> Created relation properties/5@1908b117 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s) Inferred that properties_10#join_rhs/2@5d3367rj is empty, due to properties/5@1908b117.
[2025-06-03 18:22:18] (0s) Inferred that Classes::MemberDeclaration#5cd163da/1@d51f56rt is empty, due to properties_10#join_rhs/2@5d3367rj.
[2025-06-03 18:22:18] (0s) Inferred that _AST::AstNode.getParent/0#dispred#21e1cceb_Classes::MemberDeclaration#5cd163da_Expr::ExprOrType#305a__#join_rhs/2@9fab4bq7 is empty, due to Classes::MemberDeclaration#5cd163da/1@d51f56rt.
[2025-06-03 18:22:18] (0s)  >>> Created relation toplevels/2@39c77di1 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s) Inferred that AST::isAmbientTopLevel/1#ca754f36/1@09f0d91a is empty, due to toplevels/2@39c77di1.
[2025-06-03 18:22:18] (0s) Inferred that AST::AstNode.getTopLevel/0#dispred#10343479/2@5a43ewi2 is empty, due to toplevels/2@39c77di1.
[2025-06-03 18:22:18] (0s) Inferred that AST::AstNode.getTopLevel/0#5d5948b2/2@5a43exi2 is empty, due to toplevels/2@39c77di1.
[2025-06-03 18:22:18] (0s) Inferred that _AST::AstNode.getTopLevel/0#dispred#10343479_AST::isAmbientTopLevel/1#ca754f36__@function_AST::AstNo__#antijoin_rhs/1@40b827k3 is empty, due to AST::isAmbientTopLevel/1#ca754f36/1@09f0d91a.
[2025-06-03 18:22:18] (0s) Inferred that cached_AST::isAmbientTopLevel/1#ca754f36/1@4d886ci9 is empty, due to AST::isAmbientTopLevel/1#ca754f36/1@09f0d91a.
[2025-06-03 18:22:18] (0s) Inferred that cached_AST::AstNode.getTopLevel/0#dispred#10343479/2@0f3bbai4 is empty, due to AST::AstNode.getTopLevel/0#dispred#10343479/2@5a43ewi2.
[2025-06-03 18:22:18] (0s)  >>> Created relation stmts/5@bcd8ccid with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s) Inferred that stmts_10#join_rhs/2@71c68aq8 is empty, due to stmts/5@bcd8ccid.
[2025-06-03 18:22:18] (0s) Inferred that JSDoc::JSDocTypeExpr.getEnclosingStmt/0#dispred#1ddce613/2@c958f18v is empty, due to stmts/5@bcd8ccid.
[2025-06-03 18:22:18] (0s) Inferred that Stmt::DeclStmt#28398b2c/1@fb8d64fs is empty, due to stmts_10#join_rhs/2@71c68aq8.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate AST::AstNode.getChild/1#dispred#615b8db2/3@9a4c66g8
[2025-06-03 18:22:18] (0s)  >>> Created relation AST::AstNode.getChild/1#dispred#615b8db2/3@9a4c66g8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s) Inferred that AST::AstNode.getParent/0#dispred#21e1cceb/2@dbe57dni is empty, due to AST::AstNode.getChild/1#dispred#615b8db2/3@9a4c66g8.
[2025-06-03 18:22:18] (0s) Inferred that AST::AstNode.getChild/1#dispred#615b8db2_120#join_rhs/3@94bbd000 is empty, due to AST::AstNode.getChild/1#dispred#615b8db2/3@9a4c66g8.
[2025-06-03 18:22:18] (0s) Inferred that _@function_AST::AstNode.getChild/1#dispred#615b8db2_AST::ExprOrStmt#142dc628#antijoin_rhs/1@ec8df2qf is empty, due to AST::AstNode.getChild/1#dispred#615b8db2/3@9a4c66g8.
[2025-06-03 18:22:18] (0s) Inferred that AST::AstNode.getParent/0#dispred#21e1cceb_10#join_rhs/2@7bd7de1r is empty, due to AST::AstNode.getParent/0#dispred#21e1cceb/2@dbe57dni.
[2025-06-03 18:22:18] (0s) Inferred that cached_AST::AstNode.getParent/0#dispred#21e1cceb/2@790d003s is empty, due to AST::AstNode.getParent/0#dispred#21e1cceb/2@dbe57dni.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate @function/1@5f634chc
[2025-06-03 18:22:18] (0s)  >>> Created relation @function/1@5f634chc with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s)  >>> Created relation stmt_containers/2@11b6ea31 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s)  >>> Created relation expr_containers/2@2e4cfcgs with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s)  >>> Created relation entry_cfg_node/2@563e818j with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s)  >>> Created relation exit_cfg_node/2@341fbakl with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate StmtContainers::getStmtContainer/1#845eb172/2@8b6734vr
[2025-06-03 18:22:18] (0s)  >>> Created relation StmtContainers::getStmtContainer/1#845eb172/2@8b6734vr with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s) Inferred that AST::StmtContainer.getEnclosingContainer/0#dispred#77c848c6/2@aa2751ia is empty, due to StmtContainers::getStmtContainer/1#845eb172/2@8b6734vr.
[2025-06-03 18:22:18] (0s) Inferred that cached_StmtContainers::getStmtContainer/1#845eb172/2@7374fbre is empty, due to StmtContainers::getStmtContainer/1#845eb172/2@8b6734vr.
[2025-06-03 18:22:18] (0s) Inferred that cached_AST::StmtContainer.getEnclosingContainer/0#dispred#77c848c6/2@36e74d65 is empty, due to AST::StmtContainer.getEnclosingContainer/0#dispred#77c848c6/2@aa2751ia.
[2025-06-03 18:22:18] (0s)  >>> Created relation cached_CachedStages::Stages::Ast::backref/0#7ac52a97/0@a80d27a0 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 18:22:18] (0s)  >>> Created relation folders/2@bad7d9u0 with 6 rows and digest dd5622utv00818ejc55tfsv49k9.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate Files::FsInput::ContainerBase.getAbsolutePath/0#dispred#e3a6eb29/2@966d67vo
[2025-06-03 18:22:18] (0s)  >>> Created relation Files::FsInput::ContainerBase.getAbsolutePath/0#dispred#e3a6eb29/2@966d67vo with 7 rows and digest 48074ba8d2hld0hrr742t1e8s0b.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b/3@e59cbcdu
[2025-06-03 18:22:18] (0s)  >>> Created relation Files::Container.splitAbsolutePath/2#dispred#43b82b7b/3@e59cbcdu with 15 rows and digest 6b5f402u73p4h45ie8cmj3lr221.
[2025-06-03 18:22:18] (0s) Promoting strings for predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b
[2025-06-03 18:22:18] (0s) Promoted strings in predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b in memory, took 1ms
[2025-06-03 18:22:18] (0s) Saving stringpool to save strings from predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b
[2025-06-03 18:22:18] (0s) Saved stringpool to save strings from predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b, took 0ms
[2025-06-03 18:22:18] (0s)  >>> Created relation cached_Files::Container.splitAbsolutePath/2#dispred#43b82b7b/3@c992f399 with 15 rows and digest 6b5f402u73p4h45ie8cmj3lr221.
[2025-06-03 18:22:18] (0s)  >>> Created relation has_declare_keyword/1@676f31pn with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s)  >>> Created relation has_type_keyword/1@d8c0925e with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate _@function_AST::AstNode.getChild/1#dispred#615b8db2_120#join_rhs__@function_AST::AstNode.getChild/1#__#shared/1@52a890m0
[2025-06-03 18:22:18] (0s)  >>> Created relation _@function_AST::AstNode.getChild/1#dispred#615b8db2_120#join_rhs__@function_AST::AstNode.getChild/1#__#shared/1@52a890m0 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s) Starting to evaluate predicate AST::AstNode.isAmbientInternal/0#ebd19382/1@i1#ed1d7ekc (iteration 1)
[2025-06-03 18:22:18] (0s) Empty delta for AST::AstNode.isAmbientInternal/0#ebd19382_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:22:18] (0s) Accumulating deltas
[2025-06-03 18:22:18] (0s)  >>> Created relation AST::AstNode.isAmbientInternal/0#ebd19382/1@ed1d7ekc with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s) Inferred that cached_AST::AstNode.isAmbientInternal/0#ebd19382/1@43d67air is empty, due to AST::AstNode.isAmbientInternal/0#ebd19382/1@ed1d7ekc.
[2025-06-03 18:22:18] (0s)  >>> Created relation literals/3@f05366h0 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s) Inferred that literals_20#join_rhs/2@2856f00u is empty, due to literals/3@f05366h0.
[2025-06-03 18:22:18] (0s) Inferred that literals_201#join_rhs/3@d5f6d8jq is empty, due to literals/3@f05366h0.
[2025-06-03 18:22:18] (0s) Inferred that Expr::Identifier.getName/0#3671b62f/2@904b86vm is empty, due to literals_20#join_rhs/2@2856f00u.
[2025-06-03 18:22:18] (0s) Inferred that Expr::Identifier.getName/0#dispred#1dec9a9a/2@07b00a5l is empty, due to Expr::Identifier.getName/0#3671b62f/2@904b86vm.
[2025-06-03 18:22:18] (0s) Inferred that cached_Expr::Identifier.getName/0#dispred#1dec9a9a/2@d5770a68 is empty, due to Expr::Identifier.getName/0#dispred#1dec9a9a/2@07b00a5l.
[2025-06-03 18:22:18] (0s)  >>> Created relation jsdoc/3@a172dbu7 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:22:18] (0s) Inferred that m#AST::AstNode.getFirstToken/0#dispred#b897a56e#fb/1@d534dfb1 is empty, due to jsdoc/3@a172dbu7.
[2025-06-03 18:22:18] (0s) Inferred that jsdoc_20#join_rhs/2@f78685i3 is empty, due to jsdoc/3@a172dbu7.
[2025-06-03 18:22:18] (0s) Inferred that Tokens::Token.getPreviousToken/0#dispred#924795eb#fb/2@7a247df5 is empty, due to m#AST::AstNode.getFirstToken/0#dispred#b897a56e#fb/1@d534dfb1.
[2025-06-03 18:22:18] (0s) Inferred that m#AST::AstNode.getFirstToken/0#5411fa9d#fb/1@3b24a9ir is empty, due to m#AST::AstNode.getFirstToken/0#dispred#b897a56e#fb/1@d534dfb1.
[2025-06-03 18:22:18] (0s) Inferred that Expr::ExprOrType.getOwnDocumentation/0#dispred#3c812d8f/2@e735f4an is empty, due to jsdoc_20#join_rhs/2@f78685i3.
[2025-06-03 18:22:18] (0s) Inferred that JSDoc::Documentable.getDocumentation/0#5aa175df/2@170516el is empty, due to jsdoc_20#join_rhs/2@f78685i3.
[2025-06-03 18:22:18] (0s) Inferred that JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@a8a9e47r is empty, due to jsdoc_20#join_rhs/2@f78685i3.
[2025-06-03 18:22:18] (0s) Inferred that AST::AstNode.getFirstToken/0#5411fa9d#fb/2@03f9bfa8 is empty, due to m#AST::AstNode.getFirstToken/0#5411fa9d#fb/1@3b24a9ir.
[2025-06-03 18:22:18] (0s) Inferred that project#Expr::ExprOrType.getOwnDocumentation/0#dispred#3c812d8f/1@fee808e7 is empty, due to Expr::ExprOrType.getOwnDocumentation/0#dispred#3c812d8f/2@e735f4an.
[2025-06-03 18:22:18] (0s) Inferred that AST::AstNode.getFirstToken/0#dispred#b897a56e#fb/2@c31219k7 is empty, due to AST::AstNode.getFirstToken/0#5411fa9d#fb/2@03f9bfa8.
[2025-06-03 18:22:18] (0s) Inferred that cached_JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@3216d56j is empty, due to JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@a8a9e47r.
[2025-06-03 18:22:18] (0s) Inferred that AST::AstNode.getFirstToken/0#dispred#b897a56e#fb_10#join_rhs/2@a969a52l is empty, due to AST::AstNode.getFirstToken/0#dispred#b897a56e#fb/2@c31219k7.
[2025-06-03 18:22:18] (0s) Query done
[2025-06-03 18:22:18] (0s) Sequence stamp origin is -6042180891424638080
[2025-06-03 18:22:18] (0s) Pausing evaluation to sync to disk at sequence stamp o+0
[2025-06-03 18:22:18] (0s) Unpausing evaluation
[2025-06-03 18:22:18] Evaluation of E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql produced BQRS results.
[2025-06-03 18:22:18] [PROGRESS] execute queries> [1/1 eval 229ms] Evaluation done; writing results to nodejs-security-queries\hardcoded-credentials.bqrs.
[2025-06-03 18:22:18] [PROGRESS] execute queries> Shutting down query evaluator.
[2025-06-03 18:22:18] Pausing evaluation to close the cache at sequence stamp o+1
[2025-06-03 18:22:18] Doing closing disk-cache trim now.
[2025-06-03 18:22:18] After trimming, disk cache uses 5.29kiB.
[2025-06-03 18:22:18] Unpausing evaluation
[2025-06-03 18:22:18] Exiting with code 0
