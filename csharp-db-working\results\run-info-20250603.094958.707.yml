---
queries: []
extensionPacks: []
packs:
  codeql/csharp-all#0:
    name: codeql/csharp-all
    version: 5.1.6
    isLibrary: true
    isExtensionPack: false
    localPath: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/csharp-all/5.1.6/
    localPackDefinitionFile: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/csharp-all/5.1.6/qlpack.yml
    headSha: 4bb829ebec2082e8c06f02a7d9c07e181d27c784
    runDataExtensions: []
  csharp-security-queries#3:
    name: csharp-security-queries
    version: 1.0.0
    isLibrary: false
    isExtensionPack: false
    localPath: file:///E:/advance_javascript/codeQL/7/csharp-queries/
    localPackDefinitionFile: file:///E:/advance_javascript/codeQL/7/csharp-queries/qlpack.yml
    runDataExtensions:
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Amazon.Lambda.model.yml
      index: 0
      firstRowId: 0
      rowCount: 6
      locations:
        lineNumbers: A=6+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Amazon.Lambda.model.yml
      index: 1
      firstRowId: 6
      rowCount: 10
      locations:
        lineNumbers: A=17+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Dapper.model.yml
      index: 0
      firstRowId: 16
      rowCount: 55
      locations:
        lineNumbers: A=6+1*54
        columnNumbers: A=9*55
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Dapper.model.yml
      index: 1
      firstRowId: 71
      rowCount: 42
      locations:
        lineNumbers: A=65+1*41
        columnNumbers: A=9*42
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Dapper.model.yml
      index: 2
      firstRowId: 113
      rowCount: 1
      locations:
        lineNumbers: A=111
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.ApplicationBlocks.Data.model.yml
      index: 0
      firstRowId: 114
      rowCount: 28
      locations:
        lineNumbers: A=6+1*27
        columnNumbers: A=9*28
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.AspNetCore.Components.CompilerServices.model.yml
      index: 0
      firstRowId: 142
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.AspNetCore.Components.model.yml
      index: 0
      firstRowId: 143
      rowCount: 4
      locations:
        lineNumbers: A=6+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.AspNetCore.Components.model.yml
      index: 1
      firstRowId: 147
      rowCount: 1
      locations:
        lineNumbers: A=14
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.AspNetCore.Components.model.yml
      index: 2
      firstRowId: 148
      rowCount: 2
      locations:
        lineNumbers: A=19+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.AspNetCore.Http.model.yml
      index: 0
      firstRowId: 150
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.AspNetCore.Mvc.model.yml
      index: 0
      firstRowId: 151
      rowCount: 2
      locations:
        lineNumbers: A=6+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.AspNetCore.WebUtilities.model.yml
      index: 0
      firstRowId: 153
      rowCount: 2
      locations:
        lineNumbers: A=6+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.EntityFrameworkCore.model.yml
      index: 0
      firstRowId: 155
      rowCount: 6
      locations:
        lineNumbers: A=6+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.EntityFrameworkCore.model.yml
      index: 1
      firstRowId: 161
      rowCount: 12
      locations:
        lineNumbers: A=16+1*11
        columnNumbers: A=9*12
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.Extensions.Configuration.model.yml
      index: 0
      firstRowId: 173
      rowCount: 2
      locations:
        lineNumbers: A=6+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.Extensions.Configuration.model.yml
      index: 1
      firstRowId: 175
      rowCount: 6
      locations:
        lineNumbers: A=12+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.Extensions.Primitives.model.yml
      index: 0
      firstRowId: 181
      rowCount: 54
      locations:
        lineNumbers: A=6+1*53
        columnNumbers: A=9*54
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.JSInterop.model.yml
      index: 0
      firstRowId: 235
      rowCount: 2
      locations:
        lineNumbers: A=6+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.VisualBasic.model.yml
      index: 0
      firstRowId: 237
      rowCount: 5
      locations:
        lineNumbers: A=6+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Microsoft.Win32.model.yml
      index: 0
      firstRowId: 242
      rowCount: 4
      locations:
        lineNumbers: A=6+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/MySql.Data.MySqlClient.model.yml
      index: 0
      firstRowId: 246
      rowCount: 48
      locations:
        lineNumbers: A=6+1*47
        columnNumbers: A=9*48
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Newtonsoft.Json.Linq.model.yml
      index: 0
      firstRowId: 294
      rowCount: 25
      locations:
        lineNumbers: A=6+1*24
        columnNumbers: A=9*25
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Newtonsoft.Json.model.yml
      index: 0
      firstRowId: 319
      rowCount: 66
      locations:
        lineNumbers: A=6+1*65
        columnNumbers: A=9*66
     -
      pack: codeql/csharp-all#0
      relativePath: ext/ServiceStack.OrmLite.model.yml
      index: 0
      firstRowId: 385
      rowCount: 92
      locations:
        lineNumbers: A=6+1*91
        columnNumbers: A=9*92
     -
      pack: codeql/csharp-all#0
      relativePath: ext/ServiceStack.Redis.model.yml
      index: 0
      firstRowId: 477
      rowCount: 27
      locations:
        lineNumbers: A=6+1*26
        columnNumbers: A=9*27
     -
      pack: codeql/csharp-all#0
      relativePath: ext/ServiceStack.model.yml
      index: 0
      firstRowId: 504
      rowCount: 75
      locations:
        lineNumbers: A=6+1*74
        columnNumbers: A=9*75
     -
      pack: codeql/csharp-all#0
      relativePath: ext/ServiceStack.model.yml
      index: 1
      firstRowId: 579
      rowCount: 7
      locations:
        lineNumbers: A=85+1*6
        columnNumbers: A=9*7
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.CodeDom.model.yml
      index: 0
      firstRowId: 586
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Collections.Concurrent.model.yml
      index: 0
      firstRowId: 587
      rowCount: 17
      locations:
        lineNumbers: A=6+1*16
        columnNumbers: A=9*17
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Collections.Generic.model.yml
      index: 0
      firstRowId: 604
      rowCount: 81
      locations:
        lineNumbers: A=6+1*80
        columnNumbers: A=9*81
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Collections.Immutable.model.yml
      index: 0
      firstRowId: 685
      rowCount: 86
      locations:
        lineNumbers: A=6+1*85
        columnNumbers: A=9*86
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Collections.ObjectModel.model.yml
      index: 0
      firstRowId: 771
      rowCount: 9
      locations:
        lineNumbers: A=6+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Collections.Specialized.model.yml
      index: 0
      firstRowId: 780
      rowCount: 15
      locations:
        lineNumbers: A=6+1*14
        columnNumbers: A=9*15
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Collections.model.yml
      index: 0
      firstRowId: 795
      rowCount: 51
      locations:
        lineNumbers: A=6+1*50
        columnNumbers: A=9*51
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.ComponentModel.Design.model.yml
      index: 0
      firstRowId: 846
      rowCount: 10
      locations:
        lineNumbers: A=6+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.ComponentModel.model.yml
      index: 0
      firstRowId: 856
      rowCount: 33
      locations:
        lineNumbers: A=6+1*32
        columnNumbers: A=9*33
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Configuration.Provider.model.yml
      index: 0
      firstRowId: 889
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Configuration.model.yml
      index: 0
      firstRowId: 890
      rowCount: 12
      locations:
        lineNumbers: A=6+1*11
        columnNumbers: A=9*12
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Configuration.model.yml
      index: 1
      firstRowId: 902
      rowCount: 1
      locations:
        lineNumbers: A=22
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.Common.model.yml
      index: 0
      firstRowId: 903
      rowCount: 29
      locations:
        lineNumbers: A=6+1*28
        columnNumbers: A=9*29
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.Entity.model.yml
      index: 0
      firstRowId: 932
      rowCount: 9
      locations:
        lineNumbers: A=6+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.Entity.model.yml
      index: 1
      firstRowId: 941
      rowCount: 3
      locations:
        lineNumbers: A=19+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.EntityClient.model.yml
      index: 0
      firstRowId: 944
      rowCount: 3
      locations:
        lineNumbers: A=6+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.Odbc.model.yml
      index: 0
      firstRowId: 947
      rowCount: 3
      locations:
        lineNumbers: A=6+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.OleDb.model.yml
      index: 0
      firstRowId: 950
      rowCount: 3
      locations:
        lineNumbers: A=6+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.SQLite.model.yml
      index: 0
      firstRowId: 953
      rowCount: 7
      locations:
        lineNumbers: A=6+1*6
        columnNumbers: A=9*7
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.SQLite.model.yml
      index: 1
      firstRowId: 960
      rowCount: 3
      locations:
        lineNumbers: A=17+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.SqlClient.model.yml
      index: 0
      firstRowId: 963
      rowCount: 6
      locations:
        lineNumbers: A=6+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.SqlClient.model.yml
      index: 1
      firstRowId: 969
      rowCount: 3
      locations:
        lineNumbers: A=16+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.model.yml
      index: 0
      firstRowId: 972
      rowCount: 76
      locations:
        lineNumbers: A=6+1*75
        columnNumbers: A=9*76
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Data.model.yml
      index: 1
      firstRowId: 1048
      rowCount: 3
      locations:
        lineNumbers: A=87+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Diagnostics.model.yml
      index: 0
      firstRowId: 1051
      rowCount: 19
      locations:
        lineNumbers: A=6+1*18
        columnNumbers: A=9*19
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Diagnostics.model.yml
      index: 1
      firstRowId: 1070
      rowCount: 1
      locations:
        lineNumbers: A=29
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Dynamic.model.yml
      index: 0
      firstRowId: 1071
      rowCount: 2
      locations:
        lineNumbers: A=6+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.IO.Compression.model.yml
      index: 0
      firstRowId: 1073
      rowCount: 4
      locations:
        lineNumbers: A=6+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.IO.model.yml
      index: 0
      firstRowId: 1077
      rowCount: 32
      locations:
        lineNumbers: A=6+1*31
        columnNumbers: A=9*32
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.IO.model.yml
      index: 1
      firstRowId: 1109
      rowCount: 99
      locations:
        lineNumbers: A=42+1*49+2+1+2+1*37+2+1*3+2+1*4
        columnNumbers: A=9*99
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Linq.model.yml
      index: 0
      firstRowId: 1208
      rowCount: 634
      locations:
        lineNumbers: A=6+1*633
        columnNumbers: A=9*634
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Net.Http.Headers.model.yml
      index: 0
      firstRowId: 1842
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Net.Http.model.yml
      index: 0
      firstRowId: 1843
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Net.Http.model.yml
      index: 1
      firstRowId: 1844
      rowCount: 8
      locations:
        lineNumbers: A=11+1*7
        columnNumbers: A=9*8
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Net.Mail.model.yml
      index: 0
      firstRowId: 1852
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Net.Sockets.model.yml
      index: 0
      firstRowId: 1853
      rowCount: 4
      locations:
        lineNumbers: A=6+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Net.model.yml
      index: 0
      firstRowId: 1857
      rowCount: 9
      locations:
        lineNumbers: A=6+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Runtime.CompilerServices.model.yml
      index: 0
      firstRowId: 1866
      rowCount: 4
      locations:
        lineNumbers: A=6+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Security.Cryptography.X509Certificates.model.yml
      index: 0
      firstRowId: 1870
      rowCount: 21
      locations:
        lineNumbers: A=6+1*20
        columnNumbers: A=9*21
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Security.Cryptography.model.yml
      index: 0
      firstRowId: 1891
      rowCount: 3
      locations:
        lineNumbers: A=6+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Security.Cryptography.model.yml
      index: 1
      firstRowId: 1894
      rowCount: 6
      locations:
        lineNumbers: A=13+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Security.Permissions.model.yml
      index: 0
      firstRowId: 1900
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Security.Policy.model.yml
      index: 0
      firstRowId: 1901
      rowCount: 2
      locations:
        lineNumbers: A=6+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Text.RegularExpressions.model.yml
      index: 0
      firstRowId: 1903
      rowCount: 4
      locations:
        lineNumbers: A=6+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Text.model.yml
      index: 0
      firstRowId: 1907
      rowCount: 131
      locations:
        lineNumbers: A=6+1*130
        columnNumbers: A=9*131
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Threading.Tasks.model.yml
      index: 0
      firstRowId: 2038
      rowCount: 176
      locations:
        lineNumbers: A=6+1*175
        columnNumbers: A=9*176
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Web.UI.WebControls.model.yml
      index: 0
      firstRowId: 2214
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Web.model.yml
      index: 0
      firstRowId: 2215
      rowCount: 4
      locations:
        lineNumbers: A=6+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Web.model.yml
      index: 1
      firstRowId: 2219
      rowCount: 16
      locations:
        lineNumbers: A=14+1*15
        columnNumbers: A=9*16
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Xml.Schema.model.yml
      index: 0
      firstRowId: 2235
      rowCount: 10
      locations:
        lineNumbers: A=6+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Xml.Serialization.model.yml
      index: 0
      firstRowId: 2245
      rowCount: 23
      locations:
        lineNumbers: A=6+1*22
        columnNumbers: A=9*23
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.Xml.model.yml
      index: 0
      firstRowId: 2268
      rowCount: 46
      locations:
        lineNumbers: A=6+1*45
        columnNumbers: A=9*46
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.model.yml
      index: 0
      firstRowId: 2314
      rowCount: 8
      locations:
        lineNumbers: A=6+1*7
        columnNumbers: A=9*8
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.model.yml
      index: 1
      firstRowId: 2322
      rowCount: 906
      locations:
        lineNumbers: A=18+1*905
        columnNumbers: A=9*906
     -
      pack: codeql/csharp-all#0
      relativePath: ext/System.model.yml
      index: 2
      firstRowId: 3228
      rowCount: 1
      locations:
        lineNumbers: A=928
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/Windows.Security.Cryptography.Core.model.yml
      index: 0
      firstRowId: 3229
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Generators.model.yml
      index: 0
      firstRowId: 3230
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.IBC.model.yml
      index: 0
      firstRowId: 3232
      rowCount: 4
      locations:
        lineNumbers: A=7+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.IBC.model.yml
      index: 1
      firstRowId: 3236
      rowCount: 6
      locations:
        lineNumbers: A=15+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.Reflection.ReadyToRun.Amd64.model.yml
      index: 0
      firstRowId: 3242
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.Reflection.ReadyToRun.Amd64.model.yml
      index: 1
      firstRowId: 3245
      rowCount: 12
      locations:
        lineNumbers: A=14+1*11
        columnNumbers: A=9*12
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.Reflection.ReadyToRun.Arm.model.yml
      index: 0
      firstRowId: 3257
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.Reflection.ReadyToRun.Arm64.model.yml
      index: 0
      firstRowId: 3262
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.Reflection.ReadyToRun.LoongArch64.model.yml
      index: 0
      firstRowId: 3267
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.Reflection.ReadyToRun.RiscV64.model.yml
      index: 0
      firstRowId: 3272
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.Reflection.ReadyToRun.model.yml
      index: 0
      firstRowId: 3277
      rowCount: 105
      locations:
        lineNumbers: A=7+1*104
        columnNumbers: A=9*105
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.Reflection.ReadyToRun.model.yml
      index: 1
      firstRowId: 3382
      rowCount: 158
      locations:
        lineNumbers: A=116+1*157
        columnNumbers: A=9*158
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.Reflection.ReadyToRun.x86.model.yml
      index: 0
      firstRowId: 3540
      rowCount: 4
      locations:
        lineNumbers: A=7+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.Reflection.ReadyToRun.x86.model.yml
      index: 1
      firstRowId: 3544
      rowCount: 23
      locations:
        lineNumbers: A=15+1*22
        columnNumbers: A=9*23
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.model.yml
      index: 0
      firstRowId: 3567
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILCompiler.model.yml
      index: 1
      firstRowId: 3572
      rowCount: 9
      locations:
        lineNumbers: A=16+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.CodeFix.model.yml
      index: 0
      firstRowId: 3581
      rowCount: 23
      locations:
        lineNumbers: A=7+1*22
        columnNumbers: A=9*23
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.RoslynAnalyzer.DataFlow.model.yml
      index: 0
      firstRowId: 3604
      rowCount: 94
      locations:
        lineNumbers: A=7+1*93
        columnNumbers: A=9*94
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.RoslynAnalyzer.DataFlow.model.yml
      index: 1
      firstRowId: 3698
      rowCount: 74
      locations:
        lineNumbers: A=105+1*73
        columnNumbers: A=9*74
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.RoslynAnalyzer.TrimAnalysis.model.yml
      index: 0
      firstRowId: 3772
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.RoslynAnalyzer.model.yml
      index: 0
      firstRowId: 3773
      rowCount: 12
      locations:
        lineNumbers: A=7+1*11
        columnNumbers: A=9*12
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.RoslynAnalyzer.model.yml
      index: 1
      firstRowId: 3785
      rowCount: 40
      locations:
        lineNumbers: A=23+1*39
        columnNumbers: A=9*40
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.Shared.DataFlow.model.yml
      index: 0
      firstRowId: 3825
      rowCount: 26
      locations:
        lineNumbers: A=7+1*25
        columnNumbers: A=9*26
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.Shared.DataFlow.model.yml
      index: 1
      firstRowId: 3851
      rowCount: 62
      locations:
        lineNumbers: A=37+1*61
        columnNumbers: A=9*62
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.Shared.TrimAnalysis.model.yml
      index: 0
      firstRowId: 3913
      rowCount: 7
      locations:
        lineNumbers: A=7+1*6
        columnNumbers: A=9*7
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.Shared.TrimAnalysis.model.yml
      index: 1
      firstRowId: 3920
      rowCount: 32
      locations:
        lineNumbers: A=18+1*31
        columnNumbers: A=9*32
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.Shared.TypeSystemProxy.model.yml
      index: 0
      firstRowId: 3952
      rowCount: 15
      locations:
        lineNumbers: A=7+1*14
        columnNumbers: A=9*15
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.Shared.model.yml
      index: 0
      firstRowId: 3967
      rowCount: 4
      locations:
        lineNumbers: A=7+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.Shared.model.yml
      index: 1
      firstRowId: 3971
      rowCount: 6
      locations:
        lineNumbers: A=15+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.Tasks.model.yml
      index: 0
      firstRowId: 3977
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/ILLink.Tasks.model.yml
      index: 1
      firstRowId: 3982
      rowCount: 15
      locations:
        lineNumbers: A=16+1*14
        columnNumbers: A=9*15
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.IL.Stubs.model.yml
      index: 0
      firstRowId: 3997
      rowCount: 27
      locations:
        lineNumbers: A=7+1*26
        columnNumbers: A=9*27
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.IL.Stubs.model.yml
      index: 1
      firstRowId: 4024
      rowCount: 37
      locations:
        lineNumbers: A=38+1*36
        columnNumbers: A=9*37
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.IL.model.yml
      index: 0
      firstRowId: 4061
      rowCount: 27
      locations:
        lineNumbers: A=7+1*26
        columnNumbers: A=9*27
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.IL.model.yml
      index: 1
      firstRowId: 4088
      rowCount: 27
      locations:
        lineNumbers: A=38+1*26
        columnNumbers: A=9*27
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.NativeFormat.model.yml
      index: 0
      firstRowId: 4115
      rowCount: 16
      locations:
        lineNumbers: A=7+1*15
        columnNumbers: A=9*16
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.Pgo.model.yml
      index: 0
      firstRowId: 4131
      rowCount: 9
      locations:
        lineNumbers: A=7+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.Pgo.model.yml
      index: 1
      firstRowId: 4140
      rowCount: 22
      locations:
        lineNumbers: A=20+1*21
        columnNumbers: A=9*22
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.TypeSystem.Ecma.model.yml
      index: 0
      firstRowId: 4162
      rowCount: 54
      locations:
        lineNumbers: A=7+1*53
        columnNumbers: A=9*54
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.TypeSystem.Ecma.model.yml
      index: 1
      firstRowId: 4216
      rowCount: 137
      locations:
        lineNumbers: A=65+1*136
        columnNumbers: A=9*137
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.TypeSystem.model.yml
      index: 0
      firstRowId: 4353
      rowCount: 288
      locations:
        lineNumbers: A=7+1*287
        columnNumbers: A=9*288
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.TypeSystem.model.yml
      index: 1
      firstRowId: 4641
      rowCount: 668
      locations:
        lineNumbers: A=299+1*667
        columnNumbers: A=9*668
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Internal.model.yml
      index: 0
      firstRowId: 5309
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/IntrinsicsInSystemPrivateCoreLib.model.yml
      index: 0
      firstRowId: 5314
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.CSharp.RuntimeBinder.model.yml
      index: 0
      firstRowId: 5316
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.CSharp.RuntimeBinder.model.yml
      index: 1
      firstRowId: 5317
      rowCount: 17
      locations:
        lineNumbers: A=12+1*16
        columnNumbers: A=9*17
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.CSharp.model.yml
      index: 0
      firstRowId: 5334
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.CSharp.model.yml
      index: 1
      firstRowId: 5335
      rowCount: 2
      locations:
        lineNumbers: A=12+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Diagnostics.JitTrace.model.yml
      index: 0
      firstRowId: 5337
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Diagnostics.Tools.Pgo.TypeRefTypeSystem.model.yml
      index: 0
      firstRowId: 5342
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Diagnostics.Tools.Pgo.TypeRefTypeSystem.model.yml
      index: 1
      firstRowId: 5343
      rowCount: 8
      locations:
        lineNumbers: A=12+1*7
        columnNumbers: A=9*8
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Diagnostics.Tools.Pgo.model.yml
      index: 0
      firstRowId: 5351
      rowCount: 20
      locations:
        lineNumbers: A=7+1*19
        columnNumbers: A=9*20
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Diagnostics.Tools.Pgo.model.yml
      index: 1
      firstRowId: 5371
      rowCount: 18
      locations:
        lineNumbers: A=31+1*17
        columnNumbers: A=9*18
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.DotNet.Build.Tasks.model.yml
      index: 0
      firstRowId: 5389
      rowCount: 11
      locations:
        lineNumbers: A=7+1*10
        columnNumbers: A=9*11
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.DotNet.Build.Tasks.model.yml
      index: 1
      firstRowId: 5400
      rowCount: 10
      locations:
        lineNumbers: A=22+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.DotNet.PlatformAbstractions.model.yml
      index: 0
      firstRowId: 5410
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.DotNet.PlatformAbstractions.model.yml
      index: 1
      firstRowId: 5411
      rowCount: 5
      locations:
        lineNumbers: A=12+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Caching.Distributed.model.yml
      index: 0
      firstRowId: 5416
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Caching.Distributed.model.yml
      index: 1
      firstRowId: 5419
      rowCount: 30
      locations:
        lineNumbers: A=14+1*29
        columnNumbers: A=9*30
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Caching.Hybrid.model.yml
      index: 0
      firstRowId: 5449
      rowCount: 10
      locations:
        lineNumbers: A=7+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Caching.Memory.model.yml
      index: 0
      firstRowId: 5459
      rowCount: 37
      locations:
        lineNumbers: A=7+1*36
        columnNumbers: A=9*37
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Caching.Memory.model.yml
      index: 1
      firstRowId: 5496
      rowCount: 21
      locations:
        lineNumbers: A=48+1*20
        columnNumbers: A=9*21
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.Binder.SourceGeneration.model.yml
      index: 0
      firstRowId: 5517
      rowCount: 22
      locations:
        lineNumbers: A=7+1*21
        columnNumbers: A=9*22
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.Binder.SourceGeneration.model.yml
      index: 1
      firstRowId: 5539
      rowCount: 80
      locations:
        lineNumbers: A=33+1*79
        columnNumbers: A=9*80
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.CommandLine.model.yml
      index: 0
      firstRowId: 5619
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.CommandLine.model.yml
      index: 1
      firstRowId: 5620
      rowCount: 2
      locations:
        lineNumbers: A=12+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.EnvironmentVariables.model.yml
      index: 0
      firstRowId: 5622
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.EnvironmentVariables.model.yml
      index: 1
      firstRowId: 5624
      rowCount: 1
      locations:
        lineNumbers: A=13
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.Ini.model.yml
      index: 0
      firstRowId: 5625
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.Ini.model.yml
      index: 1
      firstRowId: 5627
      rowCount: 4
      locations:
        lineNumbers: A=13+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.Json.model.yml
      index: 0
      firstRowId: 5631
      rowCount: 4
      locations:
        lineNumbers: A=7+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.Memory.model.yml
      index: 0
      firstRowId: 5635
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.Memory.model.yml
      index: 1
      firstRowId: 5636
      rowCount: 1
      locations:
        lineNumbers: A=12
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.UserSecrets.model.yml
      index: 0
      firstRowId: 5637
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.UserSecrets.model.yml
      index: 1
      firstRowId: 5639
      rowCount: 1
      locations:
        lineNumbers: A=13
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.UserSecrets.model.yml
      index: 2
      firstRowId: 5640
      rowCount: 1
      locations:
        lineNumbers: A=18
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.Xml.model.yml
      index: 0
      firstRowId: 5641
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.Xml.model.yml
      index: 1
      firstRowId: 5644
      rowCount: 4
      locations:
        lineNumbers: A=14+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.model.yml
      index: 0
      firstRowId: 5648
      rowCount: 79
      locations:
        lineNumbers: A=7+1*78
        columnNumbers: A=9*79
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Configuration.model.yml
      index: 1
      firstRowId: 5727
      rowCount: 62
      locations:
        lineNumbers: A=90+1*61
        columnNumbers: A=9*62
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyInjection.Extensions.model.yml
      index: 0
      firstRowId: 5789
      rowCount: 18
      locations:
        lineNumbers: A=7+1*17
        columnNumbers: A=9*18
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyInjection.Extensions.model.yml
      index: 1
      firstRowId: 5807
      rowCount: 37
      locations:
        lineNumbers: A=29+1*36
        columnNumbers: A=9*37
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyInjection.Specification.Fakes.model.yml
      index: 0
      firstRowId: 5844
      rowCount: 24
      locations:
        lineNumbers: A=7+1*23
        columnNumbers: A=9*24
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyInjection.Specification.Fakes.model.yml
      index: 1
      firstRowId: 5868
      rowCount: 52
      locations:
        lineNumbers: A=35+1*51
        columnNumbers: A=9*52
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyInjection.Specification.model.yml
      index: 0
      firstRowId: 5920
      rowCount: 11
      locations:
        lineNumbers: A=7+1*10
        columnNumbers: A=9*11
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyInjection.Specification.model.yml
      index: 1
      firstRowId: 5931
      rowCount: 119
      locations:
        lineNumbers: A=22+1*118
        columnNumbers: A=9*119
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyInjection.model.yml
      index: 0
      firstRowId: 6050
      rowCount: 156
      locations:
        lineNumbers: A=7+1*155
        columnNumbers: A=9*156
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyInjection.model.yml
      index: 1
      firstRowId: 6206
      rowCount: 86
      locations:
        lineNumbers: A=167+1*85
        columnNumbers: A=9*86
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyModel.Resolution.model.yml
      index: 0
      firstRowId: 6292
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyModel.Resolution.model.yml
      index: 1
      firstRowId: 6297
      rowCount: 1
      locations:
        lineNumbers: A=16
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyModel.Resolution.model.yml
      index: 2
      firstRowId: 6298
      rowCount: 4
      locations:
        lineNumbers: A=21+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyModel.model.yml
      index: 0
      firstRowId: 6302
      rowCount: 52
      locations:
        lineNumbers: A=7+1*51
        columnNumbers: A=9*52
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.DependencyModel.model.yml
      index: 1
      firstRowId: 6354
      rowCount: 72
      locations:
        lineNumbers: A=63+1*71
        columnNumbers: A=9*72
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Diagnostics.Metrics.Configuration.model.yml
      index: 0
      firstRowId: 6426
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Diagnostics.Metrics.model.yml
      index: 0
      firstRowId: 6427
      rowCount: 14
      locations:
        lineNumbers: A=7+1*13
        columnNumbers: A=9*14
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Diagnostics.Metrics.model.yml
      index: 1
      firstRowId: 6441
      rowCount: 14
      locations:
        lineNumbers: A=25+1*13
        columnNumbers: A=9*14
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileProviders.Composite.model.yml
      index: 0
      firstRowId: 6455
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileProviders.Composite.model.yml
      index: 1
      firstRowId: 6457
      rowCount: 1
      locations:
        lineNumbers: A=13
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileProviders.Internal.model.yml
      index: 0
      firstRowId: 6458
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileProviders.Physical.model.yml
      index: 0
      firstRowId: 6461
      rowCount: 11
      locations:
        lineNumbers: A=7+1*10
        columnNumbers: A=9*11
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileProviders.Physical.model.yml
      index: 1
      firstRowId: 6472
      rowCount: 17
      locations:
        lineNumbers: A=22+1*16
        columnNumbers: A=9*17
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileProviders.model.yml
      index: 0
      firstRowId: 6489
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileProviders.model.yml
      index: 1
      firstRowId: 6494
      rowCount: 36
      locations:
        lineNumbers: A=16+1*35
        columnNumbers: A=9*36
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileSystemGlobbing.Abstractions.model.yml
      index: 0
      firstRowId: 6530
      rowCount: 8
      locations:
        lineNumbers: A=7+1*7
        columnNumbers: A=9*8
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileSystemGlobbing.Abstractions.model.yml
      index: 1
      firstRowId: 6538
      rowCount: 3
      locations:
        lineNumbers: A=19+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileSystemGlobbing.Internal.PathSegments.model.yml
      index: 0
      firstRowId: 6541
      rowCount: 4
      locations:
        lineNumbers: A=7+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileSystemGlobbing.Internal.PathSegments.model.yml
      index: 1
      firstRowId: 6545
      rowCount: 16
      locations:
        lineNumbers: A=15+1*15
        columnNumbers: A=9*16
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileSystemGlobbing.Internal.PatternContexts.model.yml
      index: 0
      firstRowId: 6561
      rowCount: 15
      locations:
        lineNumbers: A=7+1*14
        columnNumbers: A=9*15
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileSystemGlobbing.Internal.PatternContexts.model.yml
      index: 1
      firstRowId: 6576
      rowCount: 28
      locations:
        lineNumbers: A=26+1*27
        columnNumbers: A=9*28
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileSystemGlobbing.Internal.Patterns.model.yml
      index: 0
      firstRowId: 6604
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileSystemGlobbing.Internal.model.yml
      index: 0
      firstRowId: 6607
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileSystemGlobbing.Internal.model.yml
      index: 1
      firstRowId: 6609
      rowCount: 17
      locations:
        lineNumbers: A=13+1*16
        columnNumbers: A=9*17
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileSystemGlobbing.model.yml
      index: 0
      firstRowId: 6626
      rowCount: 8
      locations:
        lineNumbers: A=7+1*7
        columnNumbers: A=9*8
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.FileSystemGlobbing.model.yml
      index: 1
      firstRowId: 6634
      rowCount: 15
      locations:
        lineNumbers: A=19+1*14
        columnNumbers: A=9*15
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Hosting.Internal.model.yml
      index: 0
      firstRowId: 6649
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Hosting.Internal.model.yml
      index: 1
      firstRowId: 6654
      rowCount: 6
      locations:
        lineNumbers: A=16+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Hosting.Systemd.model.yml
      index: 0
      firstRowId: 6660
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Hosting.Systemd.model.yml
      index: 1
      firstRowId: 6665
      rowCount: 7
      locations:
        lineNumbers: A=16+1*6
        columnNumbers: A=9*7
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Hosting.WindowsServices.model.yml
      index: 0
      firstRowId: 6672
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Hosting.WindowsServices.model.yml
      index: 1
      firstRowId: 6675
      rowCount: 7
      locations:
        lineNumbers: A=14+1*6
        columnNumbers: A=9*7
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Hosting.model.yml
      index: 0
      firstRowId: 6682
      rowCount: 48
      locations:
        lineNumbers: A=7+1*47
        columnNumbers: A=9*48
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Hosting.model.yml
      index: 1
      firstRowId: 6730
      rowCount: 50
      locations:
        lineNumbers: A=59+1*49
        columnNumbers: A=9*50
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Http.Logging.model.yml
      index: 0
      firstRowId: 6780
      rowCount: 6
      locations:
        lineNumbers: A=7+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Http.Logging.model.yml
      index: 1
      firstRowId: 6786
      rowCount: 10
      locations:
        lineNumbers: A=17+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Http.model.yml
      index: 0
      firstRowId: 6796
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Http.model.yml
      index: 1
      firstRowId: 6799
      rowCount: 6
      locations:
        lineNumbers: A=14+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Internal.model.yml
      index: 0
      firstRowId: 6805
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.Abstractions.model.yml
      index: 0
      firstRowId: 6807
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.Abstractions.model.yml
      index: 1
      firstRowId: 6812
      rowCount: 23
      locations:
        lineNumbers: A=16+1*22
        columnNumbers: A=9*23
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.Configuration.model.yml
      index: 0
      firstRowId: 6835
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.Console.model.yml
      index: 0
      firstRowId: 6840
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.Console.model.yml
      index: 1
      firstRowId: 6845
      rowCount: 9
      locations:
        lineNumbers: A=16+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.Debug.model.yml
      index: 0
      firstRowId: 6854
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.EventLog.model.yml
      index: 0
      firstRowId: 6855
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.EventLog.model.yml
      index: 1
      firstRowId: 6856
      rowCount: 2
      locations:
        lineNumbers: A=12+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.EventSource.model.yml
      index: 0
      firstRowId: 6858
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.EventSource.model.yml
      index: 1
      firstRowId: 6859
      rowCount: 2
      locations:
        lineNumbers: A=12+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.Generators.model.yml
      index: 0
      firstRowId: 6861
      rowCount: 29
      locations:
        lineNumbers: A=7+1*28
        columnNumbers: A=9*29
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.TraceSource.model.yml
      index: 0
      firstRowId: 6890
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.TraceSource.model.yml
      index: 1
      firstRowId: 6892
      rowCount: 2
      locations:
        lineNumbers: A=13+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.model.yml
      index: 0
      firstRowId: 6894
      rowCount: 93
      locations:
        lineNumbers: A=7+1*92
        columnNumbers: A=9*93
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Logging.model.yml
      index: 1
      firstRowId: 6987
      rowCount: 75
      locations:
        lineNumbers: A=104+1*74
        columnNumbers: A=9*75
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Options.Generators.model.yml
      index: 0
      firstRowId: 7062
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Options.model.yml
      index: 0
      firstRowId: 7063
      rowCount: 174
      locations:
        lineNumbers: A=7+1*173
        columnNumbers: A=9*174
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Options.model.yml
      index: 1
      firstRowId: 7237
      rowCount: 137
      locations:
        lineNumbers: A=185+1*136
        columnNumbers: A=9*137
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Primitives.model.yml
      index: 0
      firstRowId: 7374
      rowCount: 21
      locations:
        lineNumbers: A=7+1*20
        columnNumbers: A=9*21
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Extensions.Primitives.model.yml
      index: 1
      firstRowId: 7395
      rowCount: 74
      locations:
        lineNumbers: A=32+1*73
        columnNumbers: A=9*74
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Interop.Analyzers.model.yml
      index: 0
      firstRowId: 7469
      rowCount: 16
      locations:
        lineNumbers: A=7+1*15
        columnNumbers: A=9*16
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Interop.Analyzers.model.yml
      index: 1
      firstRowId: 7485
      rowCount: 67
      locations:
        lineNumbers: A=27+1*66
        columnNumbers: A=9*67
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Interop.JavaScript.model.yml
      index: 0
      firstRowId: 7552
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Interop.model.yml
      index: 0
      firstRowId: 7554
      rowCount: 200
      locations:
        lineNumbers: A=7+1*199
        columnNumbers: A=9*200
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Interop.model.yml
      index: 1
      firstRowId: 7754
      rowCount: 479
      locations:
        lineNumbers: A=211+1*478
        columnNumbers: A=9*479
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.NET.Build.Tasks.model.yml
      index: 0
      firstRowId: 8233
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.NET.Build.Tasks.model.yml
      index: 1
      firstRowId: 8238
      rowCount: 13
      locations:
        lineNumbers: A=16+1*12
        columnNumbers: A=9*13
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.NETCore.Platforms.model.yml
      index: 0
      firstRowId: 8251
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.VisualBasic.CompilerServices.model.yml
      index: 0
      firstRowId: 8252
      rowCount: 200
      locations:
        lineNumbers: A=7+1*199
        columnNumbers: A=9*200
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.VisualBasic.FileIO.model.yml
      index: 0
      firstRowId: 8452
      rowCount: 89
      locations:
        lineNumbers: A=7+1*88
        columnNumbers: A=9*89
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.VisualBasic.model.yml
      index: 0
      firstRowId: 8541
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.VisualBasic.model.yml
      index: 1
      firstRowId: 8542
      rowCount: 292
      locations:
        lineNumbers: A=12+1*291
        columnNumbers: A=9*292
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Win32.SafeHandles.model.yml
      index: 0
      firstRowId: 8834
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Win32.SafeHandles.model.yml
      index: 1
      firstRowId: 8836
      rowCount: 33
      locations:
        lineNumbers: A=13+1*32
        columnNumbers: A=9*33
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Microsoft.Win32.model.yml
      index: 0
      firstRowId: 8869
      rowCount: 94
      locations:
        lineNumbers: A=7+1*93
        columnNumbers: A=9*94
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Mono.Linker.Dataflow.model.yml
      index: 0
      firstRowId: 8963
      rowCount: 24
      locations:
        lineNumbers: A=7+1*23
        columnNumbers: A=9*24
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Mono.Linker.Dataflow.model.yml
      index: 1
      firstRowId: 8987
      rowCount: 34
      locations:
        lineNumbers: A=35+1*33
        columnNumbers: A=9*34
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Mono.Linker.Steps.model.yml
      index: 0
      firstRowId: 9021
      rowCount: 91
      locations:
        lineNumbers: A=7+1*90
        columnNumbers: A=9*91
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Mono.Linker.Steps.model.yml
      index: 1
      firstRowId: 9112
      rowCount: 159
      locations:
        lineNumbers: A=102+1*158
        columnNumbers: A=9*159
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Mono.Linker.model.yml
      index: 0
      firstRowId: 9271
      rowCount: 163
      locations:
        lineNumbers: A=7+1*162
        columnNumbers: A=9*163
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/Mono.Linker.model.yml
      index: 1
      firstRowId: 9434
      rowCount: 291
      locations:
        lineNumbers: A=174+1*290
        columnNumbers: A=9*291
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/SourceGenerators.model.yml
      index: 0
      firstRowId: 9725
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/SourceGenerators.model.yml
      index: 1
      firstRowId: 9730
      rowCount: 15
      locations:
        lineNumbers: A=16+1*14
        columnNumbers: A=9*15
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Buffers.Binary.model.yml
      index: 0
      firstRowId: 9745
      rowCount: 126
      locations:
        lineNumbers: A=7+1*125
        columnNumbers: A=9*126
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Buffers.Text.model.yml
      index: 0
      firstRowId: 9871
      rowCount: 67
      locations:
        lineNumbers: A=7+1*66
        columnNumbers: A=9*67
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Buffers.model.yml
      index: 0
      firstRowId: 9938
      rowCount: 53
      locations:
        lineNumbers: A=7+1*52
        columnNumbers: A=9*53
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Buffers.model.yml
      index: 1
      firstRowId: 9991
      rowCount: 115
      locations:
        lineNumbers: A=64+1*114
        columnNumbers: A=9*115
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.CodeDom.Compiler.model.yml
      index: 0
      firstRowId: 10106
      rowCount: 187
      locations:
        lineNumbers: A=7+1*186
        columnNumbers: A=9*187
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.CodeDom.Compiler.model.yml
      index: 1
      firstRowId: 10293
      rowCount: 132
      locations:
        lineNumbers: A=198+1*131
        columnNumbers: A=9*132
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.CodeDom.model.yml
      index: 0
      firstRowId: 10425
      rowCount: 224
      locations:
        lineNumbers: A=7+1*223
        columnNumbers: A=9*224
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.CodeDom.model.yml
      index: 1
      firstRowId: 10649
      rowCount: 125
      locations:
        lineNumbers: A=235+1*124
        columnNumbers: A=9*125
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.Concurrent.model.yml
      index: 0
      firstRowId: 10774
      rowCount: 44
      locations:
        lineNumbers: A=7+1*43
        columnNumbers: A=9*44
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.Concurrent.model.yml
      index: 1
      firstRowId: 10818
      rowCount: 98
      locations:
        lineNumbers: A=55+1*97
        columnNumbers: A=9*98
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.Frozen.model.yml
      index: 0
      firstRowId: 10916
      rowCount: 23
      locations:
        lineNumbers: A=7+1*22
        columnNumbers: A=9*23
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.Frozen.model.yml
      index: 1
      firstRowId: 10939
      rowCount: 45
      locations:
        lineNumbers: A=34+1*44
        columnNumbers: A=9*45
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.Generic.model.yml
      index: 0
      firstRowId: 10984
      rowCount: 164
      locations:
        lineNumbers: A=7+1*163
        columnNumbers: A=9*164
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.Generic.model.yml
      index: 1
      firstRowId: 11148
      rowCount: 434
      locations:
        lineNumbers: A=175+1*433
        columnNumbers: A=9*434
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.Immutable.model.yml
      index: 0
      firstRowId: 11582
      rowCount: 368
      locations:
        lineNumbers: A=7+1*367
        columnNumbers: A=9*368
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.Immutable.model.yml
      index: 1
      firstRowId: 11950
      rowCount: 370
      locations:
        lineNumbers: A=379+1*369
        columnNumbers: A=9*370
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.ObjectModel.model.yml
      index: 0
      firstRowId: 12320
      rowCount: 19
      locations:
        lineNumbers: A=7+1*18
        columnNumbers: A=9*19
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.ObjectModel.model.yml
      index: 1
      firstRowId: 12339
      rowCount: 96
      locations:
        lineNumbers: A=30+1*95
        columnNumbers: A=9*96
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.Specialized.model.yml
      index: 0
      firstRowId: 12435
      rowCount: 46
      locations:
        lineNumbers: A=7+1*45
        columnNumbers: A=9*46
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.Specialized.model.yml
      index: 1
      firstRowId: 12481
      rowCount: 113
      locations:
        lineNumbers: A=57+1*112
        columnNumbers: A=9*113
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.model.yml
      index: 0
      firstRowId: 12594
      rowCount: 58
      locations:
        lineNumbers: A=7+1*57
        columnNumbers: A=9*58
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Collections.model.yml
      index: 1
      firstRowId: 12652
      rowCount: 153
      locations:
        lineNumbers: A=69+1*152
        columnNumbers: A=9*153
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Composition.Hosting.model.yml
      index: 0
      firstRowId: 12805
      rowCount: 98
      locations:
        lineNumbers: A=7+1*97
        columnNumbers: A=9*98
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Composition.Hosting.model.yml
      index: 1
      firstRowId: 12903
      rowCount: 123
      locations:
        lineNumbers: A=109+1*122
        columnNumbers: A=9*123
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Composition.Primitives.model.yml
      index: 0
      firstRowId: 13026
      rowCount: 38
      locations:
        lineNumbers: A=7+1*37
        columnNumbers: A=9*38
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Composition.Primitives.model.yml
      index: 1
      firstRowId: 13064
      rowCount: 16
      locations:
        lineNumbers: A=49+1*15
        columnNumbers: A=9*16
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Composition.ReflectionModel.model.yml
      index: 0
      firstRowId: 13080
      rowCount: 25
      locations:
        lineNumbers: A=7+1*24
        columnNumbers: A=9*25
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Composition.ReflectionModel.model.yml
      index: 1
      firstRowId: 13105
      rowCount: 9
      locations:
        lineNumbers: A=36+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Composition.Registration.model.yml
      index: 0
      firstRowId: 13114
      rowCount: 43
      locations:
        lineNumbers: A=7+1*42
        columnNumbers: A=9*43
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Composition.Registration.model.yml
      index: 1
      firstRowId: 13157
      rowCount: 8
      locations:
        lineNumbers: A=54+1*7
        columnNumbers: A=9*8
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Composition.model.yml
      index: 0
      firstRowId: 13165
      rowCount: 30
      locations:
        lineNumbers: A=7+1*29
        columnNumbers: A=9*30
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Composition.model.yml
      index: 1
      firstRowId: 13195
      rowCount: 56
      locations:
        lineNumbers: A=41+1*55
        columnNumbers: A=9*56
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.DataAnnotations.Schema.model.yml
      index: 0
      firstRowId: 13251
      rowCount: 4
      locations:
        lineNumbers: A=7+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.DataAnnotations.Schema.model.yml
      index: 1
      firstRowId: 13255
      rowCount: 6
      locations:
        lineNumbers: A=15+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.DataAnnotations.model.yml
      index: 0
      firstRowId: 13261
      rowCount: 58
      locations:
        lineNumbers: A=7+1*57
        columnNumbers: A=9*58
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.DataAnnotations.model.yml
      index: 1
      firstRowId: 13319
      rowCount: 96
      locations:
        lineNumbers: A=69+1*95
        columnNumbers: A=9*96
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Design.Serialization.model.yml
      index: 0
      firstRowId: 13415
      rowCount: 20
      locations:
        lineNumbers: A=7+1*19
        columnNumbers: A=9*20
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Design.Serialization.model.yml
      index: 1
      firstRowId: 13435
      rowCount: 76
      locations:
        lineNumbers: A=31+1*75
        columnNumbers: A=9*76
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Design.model.yml
      index: 0
      firstRowId: 13511
      rowCount: 26
      locations:
        lineNumbers: A=7+1*25
        columnNumbers: A=9*26
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.Design.model.yml
      index: 1
      firstRowId: 13537
      rowCount: 226
      locations:
        lineNumbers: A=37+1*225
        columnNumbers: A=9*226
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.model.yml
      index: 0
      firstRowId: 13763
      rowCount: 237
      locations:
        lineNumbers: A=7+1*236
        columnNumbers: A=9*237
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ComponentModel.model.yml
      index: 1
      firstRowId: 14000
      rowCount: 814
      locations:
        lineNumbers: A=248+1*813
        columnNumbers: A=9*814
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Composition.Convention.model.yml
      index: 0
      firstRowId: 14814
      rowCount: 45
      locations:
        lineNumbers: A=7+1*44
        columnNumbers: A=9*45
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Composition.Convention.model.yml
      index: 1
      firstRowId: 14859
      rowCount: 12
      locations:
        lineNumbers: A=56+1*11
        columnNumbers: A=9*12
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Composition.Hosting.Core.model.yml
      index: 0
      firstRowId: 14871
      rowCount: 48
      locations:
        lineNumbers: A=7+1*47
        columnNumbers: A=9*48
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Composition.Hosting.Core.model.yml
      index: 1
      firstRowId: 14919
      rowCount: 12
      locations:
        lineNumbers: A=59+1*11
        columnNumbers: A=9*12
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Composition.Hosting.model.yml
      index: 0
      firstRowId: 14931
      rowCount: 17
      locations:
        lineNumbers: A=7+1*16
        columnNumbers: A=9*17
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Composition.Hosting.model.yml
      index: 1
      firstRowId: 14948
      rowCount: 6
      locations:
        lineNumbers: A=28+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Composition.model.yml
      index: 0
      firstRowId: 14954
      rowCount: 28
      locations:
        lineNumbers: A=7+1*27
        columnNumbers: A=9*28
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Composition.model.yml
      index: 1
      firstRowId: 14982
      rowCount: 20
      locations:
        lineNumbers: A=39+1*19
        columnNumbers: A=9*20
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Configuration.Internal.model.yml
      index: 0
      firstRowId: 15002
      rowCount: 29
      locations:
        lineNumbers: A=7+1*28
        columnNumbers: A=9*29
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Configuration.Internal.model.yml
      index: 1
      firstRowId: 15031
      rowCount: 101
      locations:
        lineNumbers: A=40+1*100
        columnNumbers: A=9*101
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Configuration.Provider.model.yml
      index: 0
      firstRowId: 15132
      rowCount: 6
      locations:
        lineNumbers: A=7+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Configuration.Provider.model.yml
      index: 1
      firstRowId: 15138
      rowCount: 8
      locations:
        lineNumbers: A=17+1*7
        columnNumbers: A=9*8
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Configuration.model.yml
      index: 0
      firstRowId: 15146
      rowCount: 217
      locations:
        lineNumbers: A=7+1*216
        columnNumbers: A=9*217
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Configuration.model.yml
      index: 1
      firstRowId: 15363
      rowCount: 328
      locations:
        lineNumbers: A=228+1*327
        columnNumbers: A=9*328
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.Common.model.yml
      index: 0
      firstRowId: 15691
      rowCount: 97
      locations:
        lineNumbers: A=7+1*96
        columnNumbers: A=9*97
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.Common.model.yml
      index: 1
      firstRowId: 15788
      rowCount: 338
      locations:
        lineNumbers: A=108+1*337
        columnNumbers: A=9*338
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.Odbc.model.yml
      index: 0
      firstRowId: 16126
      rowCount: 77
      locations:
        lineNumbers: A=7+1*76
        columnNumbers: A=9*77
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.Odbc.model.yml
      index: 1
      firstRowId: 16203
      rowCount: 2
      locations:
        lineNumbers: A=88+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.Odbc.model.yml
      index: 2
      firstRowId: 16205
      rowCount: 108
      locations:
        lineNumbers: A=94+1*107
        columnNumbers: A=9*108
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.OleDb.model.yml
      index: 0
      firstRowId: 16313
      rowCount: 172
      locations:
        lineNumbers: A=7+1*171
        columnNumbers: A=9*172
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.OracleClient.model.yml
      index: 0
      firstRowId: 16485
      rowCount: 10
      locations:
        lineNumbers: A=7+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.SqlClient.model.yml
      index: 0
      firstRowId: 16495
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.SqlTypes.model.yml
      index: 0
      firstRowId: 16500
      rowCount: 34
      locations:
        lineNumbers: A=7+1*33
        columnNumbers: A=9*34
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.SqlTypes.model.yml
      index: 1
      firstRowId: 16534
      rowCount: 631
      locations:
        lineNumbers: A=45+1*630
        columnNumbers: A=9*631
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.model.yml
      index: 0
      firstRowId: 17165
      rowCount: 212
      locations:
        lineNumbers: A=7+1*211
        columnNumbers: A=9*212
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Data.model.yml
      index: 1
      firstRowId: 17377
      rowCount: 547
      locations:
        lineNumbers: A=223+1*546
        columnNumbers: A=9*547
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.CodeAnalysis.model.yml
      index: 0
      firstRowId: 17924
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.CodeAnalysis.model.yml
      index: 1
      firstRowId: 17929
      rowCount: 44
      locations:
        lineNumbers: A=16+1*43
        columnNumbers: A=9*44
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.Contracts.model.yml
      index: 0
      firstRowId: 17973
      rowCount: 23
      locations:
        lineNumbers: A=7+1*22
        columnNumbers: A=9*23
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.Contracts.model.yml
      index: 1
      firstRowId: 17996
      rowCount: 35
      locations:
        lineNumbers: A=34+1*34
        columnNumbers: A=9*35
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.Eventing.Reader.model.yml
      index: 0
      firstRowId: 18031
      rowCount: 190
      locations:
        lineNumbers: A=7+1*189
        columnNumbers: A=9*190
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.Metrics.model.yml
      index: 0
      firstRowId: 18221
      rowCount: 22
      locations:
        lineNumbers: A=7+1*21
        columnNumbers: A=9*22
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.Metrics.model.yml
      index: 1
      firstRowId: 18243
      rowCount: 91
      locations:
        lineNumbers: A=33+1*90
        columnNumbers: A=9*91
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.PerformanceData.model.yml
      index: 0
      firstRowId: 18334
      rowCount: 14
      locations:
        lineNumbers: A=7+1*13
        columnNumbers: A=9*14
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.SymbolStore.model.yml
      index: 0
      firstRowId: 18348
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.SymbolStore.model.yml
      index: 1
      firstRowId: 18351
      rowCount: 78
      locations:
        lineNumbers: A=14+1*77
        columnNumbers: A=9*78
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.Tracing.model.yml
      index: 0
      firstRowId: 18429
      rowCount: 22
      locations:
        lineNumbers: A=7+1*21
        columnNumbers: A=9*22
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.Tracing.model.yml
      index: 1
      firstRowId: 18451
      rowCount: 76
      locations:
        lineNumbers: A=33+1*75
        columnNumbers: A=9*76
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.model.yml
      index: 0
      firstRowId: 18527
      rowCount: 161
      locations:
        lineNumbers: A=7+1*160
        columnNumbers: A=9*161
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Diagnostics.model.yml
      index: 1
      firstRowId: 18688
      rowCount: 718
      locations:
        lineNumbers: A=172+1*717
        columnNumbers: A=9*718
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.DirectoryServices.AccountManagement.model.yml
      index: 0
      firstRowId: 19406
      rowCount: 178
      locations:
        lineNumbers: A=7+1*177
        columnNumbers: A=9*178
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.DirectoryServices.ActiveDirectory.model.yml
      index: 0
      firstRowId: 19584
      rowCount: 631
      locations:
        lineNumbers: A=7+1*630
        columnNumbers: A=9*631
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.DirectoryServices.Protocols.model.yml
      index: 0
      firstRowId: 20215
      rowCount: 84
      locations:
        lineNumbers: A=7+1*83
        columnNumbers: A=9*84
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.DirectoryServices.Protocols.model.yml
      index: 1
      firstRowId: 20299
      rowCount: 141
      locations:
        lineNumbers: A=95+1*140
        columnNumbers: A=9*141
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.DirectoryServices.model.yml
      index: 0
      firstRowId: 20440
      rowCount: 226
      locations:
        lineNumbers: A=7+1*225
        columnNumbers: A=9*226
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Drawing.Printing.model.yml
      index: 0
      firstRowId: 20666
      rowCount: 8
      locations:
        lineNumbers: A=7+1*7
        columnNumbers: A=9*8
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Drawing.model.yml
      index: 0
      firstRowId: 20674
      rowCount: 13
      locations:
        lineNumbers: A=7+1*12
        columnNumbers: A=9*13
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Drawing.model.yml
      index: 1
      firstRowId: 20687
      rowCount: 361
      locations:
        lineNumbers: A=24+1*360
        columnNumbers: A=9*361
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Dynamic.model.yml
      index: 0
      firstRowId: 21048
      rowCount: 35
      locations:
        lineNumbers: A=7+1*34
        columnNumbers: A=9*35
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Dynamic.model.yml
      index: 1
      firstRowId: 21083
      rowCount: 115
      locations:
        lineNumbers: A=46+1*114
        columnNumbers: A=9*115
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Formats.Asn1.model.yml
      index: 0
      firstRowId: 21198
      rowCount: 23
      locations:
        lineNumbers: A=7+1*22
        columnNumbers: A=9*23
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Formats.Asn1.model.yml
      index: 1
      firstRowId: 21221
      rowCount: 116
      locations:
        lineNumbers: A=34+1*115
        columnNumbers: A=9*116
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Formats.Cbor.model.yml
      index: 0
      firstRowId: 21337
      rowCount: 7
      locations:
        lineNumbers: A=7+1*6
        columnNumbers: A=9*7
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Formats.Cbor.model.yml
      index: 1
      firstRowId: 21344
      rowCount: 79
      locations:
        lineNumbers: A=18+1*78
        columnNumbers: A=9*79
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Formats.Nrbf.model.yml
      index: 0
      firstRowId: 21423
      rowCount: 10
      locations:
        lineNumbers: A=7+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Formats.Nrbf.model.yml
      index: 1
      firstRowId: 21433
      rowCount: 33
      locations:
        lineNumbers: A=21+1*32
        columnNumbers: A=9*33
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Formats.Tar.model.yml
      index: 0
      firstRowId: 21466
      rowCount: 10
      locations:
        lineNumbers: A=7+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Formats.Tar.model.yml
      index: 1
      firstRowId: 21476
      rowCount: 32
      locations:
        lineNumbers: A=21+1*31
        columnNumbers: A=9*32
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Globalization.model.yml
      index: 0
      firstRowId: 21508
      rowCount: 91
      locations:
        lineNumbers: A=7+1*90
        columnNumbers: A=9*91
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Globalization.model.yml
      index: 1
      firstRowId: 21599
      rowCount: 456
      locations:
        lineNumbers: A=102+1*455
        columnNumbers: A=9*456
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Compression.model.yml
      index: 0
      firstRowId: 22055
      rowCount: 26
      locations:
        lineNumbers: A=7+1*25
        columnNumbers: A=9*26
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Compression.model.yml
      index: 1
      firstRowId: 22081
      rowCount: 104
      locations:
        lineNumbers: A=37+1*103
        columnNumbers: A=9*104
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Enumeration.model.yml
      index: 0
      firstRowId: 22185
      rowCount: 6
      locations:
        lineNumbers: A=7+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Enumeration.model.yml
      index: 1
      firstRowId: 22191
      rowCount: 21
      locations:
        lineNumbers: A=17+1*20
        columnNumbers: A=9*21
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Hashing.model.yml
      index: 0
      firstRowId: 22212
      rowCount: 80
      locations:
        lineNumbers: A=7+1*79
        columnNumbers: A=9*80
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.IsolatedStorage.model.yml
      index: 0
      firstRowId: 22292
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.IsolatedStorage.model.yml
      index: 1
      firstRowId: 22295
      rowCount: 80
      locations:
        lineNumbers: A=14+1*79
        columnNumbers: A=9*80
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.MemoryMappedFiles.model.yml
      index: 0
      firstRowId: 22375
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.MemoryMappedFiles.model.yml
      index: 1
      firstRowId: 22380
      rowCount: 29
      locations:
        lineNumbers: A=16+1*28
        columnNumbers: A=9*29
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Packaging.model.yml
      index: 0
      firstRowId: 22409
      rowCount: 45
      locations:
        lineNumbers: A=7+1*44
        columnNumbers: A=9*45
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Packaging.model.yml
      index: 1
      firstRowId: 22454
      rowCount: 50
      locations:
        lineNumbers: A=56+1*49
        columnNumbers: A=9*50
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Pipelines.model.yml
      index: 0
      firstRowId: 22504
      rowCount: 22
      locations:
        lineNumbers: A=7+1*21
        columnNumbers: A=9*22
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Pipelines.model.yml
      index: 1
      firstRowId: 22526
      rowCount: 48
      locations:
        lineNumbers: A=33+1*47
        columnNumbers: A=9*48
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Pipes.model.yml
      index: 0
      firstRowId: 22574
      rowCount: 8
      locations:
        lineNumbers: A=7+1*7
        columnNumbers: A=9*8
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Pipes.model.yml
      index: 1
      firstRowId: 22582
      rowCount: 95
      locations:
        lineNumbers: A=19+1*94
        columnNumbers: A=9*95
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Ports.model.yml
      index: 0
      firstRowId: 22677
      rowCount: 10
      locations:
        lineNumbers: A=7+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.Ports.model.yml
      index: 1
      firstRowId: 22687
      rowCount: 29
      locations:
        lineNumbers: A=21+1*28
        columnNumbers: A=9*29
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.model.yml
      index: 0
      firstRowId: 22716
      rowCount: 274
      locations:
        lineNumbers: A=7+1*273
        columnNumbers: A=9*274
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.model.yml
      index: 1
      firstRowId: 22990
      rowCount: 1
      locations:
        lineNumbers: A=285
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.IO.model.yml
      index: 2
      firstRowId: 22991
      rowCount: 480
      locations:
        lineNumbers: A=290+1*479
        columnNumbers: A=9*480
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Linq.Expressions.Interpreter.model.yml
      index: 0
      firstRowId: 23471
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Linq.Expressions.Interpreter.model.yml
      index: 1
      firstRowId: 23472
      rowCount: 1
      locations:
        lineNumbers: A=12
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Linq.Expressions.model.yml
      index: 0
      firstRowId: 23473
      rowCount: 541
      locations:
        lineNumbers: A=7+1*540
        columnNumbers: A=9*541
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Linq.Expressions.model.yml
      index: 1
      firstRowId: 24014
      rowCount: 252
      locations:
        lineNumbers: A=552+1*251
        columnNumbers: A=9*252
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Linq.model.yml
      index: 0
      firstRowId: 24266
      rowCount: 242
      locations:
        lineNumbers: A=7+1*241
        columnNumbers: A=9*242
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Linq.model.yml
      index: 1
      firstRowId: 24508
      rowCount: 330
      locations:
        lineNumbers: A=253+1*329
        columnNumbers: A=9*330
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Management.model.yml
      index: 0
      firstRowId: 24838
      rowCount: 269
      locations:
        lineNumbers: A=7+1*268
        columnNumbers: A=9*269
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Media.model.yml
      index: 0
      firstRowId: 25107
      rowCount: 25
      locations:
        lineNumbers: A=7+1*24
        columnNumbers: A=9*25
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Cache.model.yml
      index: 0
      firstRowId: 25132
      rowCount: 9
      locations:
        lineNumbers: A=7+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Cache.model.yml
      index: 1
      firstRowId: 25141
      rowCount: 9
      locations:
        lineNumbers: A=20+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Http.Headers.model.yml
      index: 0
      firstRowId: 25150
      rowCount: 112
      locations:
        lineNumbers: A=7+1*111
        columnNumbers: A=9*112
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Http.Headers.model.yml
      index: 1
      firstRowId: 25262
      rowCount: 157
      locations:
        lineNumbers: A=123+1*156
        columnNumbers: A=9*157
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Http.Json.model.yml
      index: 0
      firstRowId: 25419
      rowCount: 4
      locations:
        lineNumbers: A=7+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Http.Json.model.yml
      index: 1
      firstRowId: 25423
      rowCount: 60
      locations:
        lineNumbers: A=15+1*59
        columnNumbers: A=9*60
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Http.Metrics.model.yml
      index: 0
      firstRowId: 25483
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Http.Metrics.model.yml
      index: 1
      firstRowId: 25486
      rowCount: 2
      locations:
        lineNumbers: A=14+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Http.model.yml
      index: 0
      firstRowId: 25488
      rowCount: 59
      locations:
        lineNumbers: A=7+1*58
        columnNumbers: A=9*59
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Http.model.yml
      index: 1
      firstRowId: 25547
      rowCount: 147
      locations:
        lineNumbers: A=70+1*146
        columnNumbers: A=9*147
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Mail.model.yml
      index: 0
      firstRowId: 25694
      rowCount: 77
      locations:
        lineNumbers: A=7+1*76
        columnNumbers: A=9*77
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Mail.model.yml
      index: 1
      firstRowId: 25771
      rowCount: 70
      locations:
        lineNumbers: A=88+1*69
        columnNumbers: A=9*70
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Mime.model.yml
      index: 0
      firstRowId: 25841
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Mime.model.yml
      index: 1
      firstRowId: 25846
      rowCount: 5
      locations:
        lineNumbers: A=16+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.NetworkInformation.model.yml
      index: 0
      firstRowId: 25851
      rowCount: 25
      locations:
        lineNumbers: A=7+1*24
        columnNumbers: A=9*25
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.NetworkInformation.model.yml
      index: 1
      firstRowId: 25876
      rowCount: 275
      locations:
        lineNumbers: A=36+1*274
        columnNumbers: A=9*275
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.PeerToPeer.Collaboration.model.yml
      index: 0
      firstRowId: 26151
      rowCount: 7
      locations:
        lineNumbers: A=7+1*6
        columnNumbers: A=9*7
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.PeerToPeer.model.yml
      index: 0
      firstRowId: 26158
      rowCount: 7
      locations:
        lineNumbers: A=7+1*6
        columnNumbers: A=9*7
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Quic.model.yml
      index: 0
      firstRowId: 26165
      rowCount: 7
      locations:
        lineNumbers: A=7+1*6
        columnNumbers: A=9*7
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Quic.model.yml
      index: 1
      firstRowId: 26172
      rowCount: 36
      locations:
        lineNumbers: A=18+1*35
        columnNumbers: A=9*36
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Security.model.yml
      index: 0
      firstRowId: 26208
      rowCount: 39
      locations:
        lineNumbers: A=7+1*38
        columnNumbers: A=9*39
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Security.model.yml
      index: 1
      firstRowId: 26247
      rowCount: 124
      locations:
        lineNumbers: A=50+1*123
        columnNumbers: A=9*124
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.ServerSentEvents.model.yml
      index: 0
      firstRowId: 26371
      rowCount: 6
      locations:
        lineNumbers: A=7+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.ServerSentEvents.model.yml
      index: 1
      firstRowId: 26377
      rowCount: 4
      locations:
        lineNumbers: A=17+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Sockets.model.yml
      index: 0
      firstRowId: 26381
      rowCount: 79
      locations:
        lineNumbers: A=7+1*78
        columnNumbers: A=9*79
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.Sockets.model.yml
      index: 1
      firstRowId: 26460
      rowCount: 300
      locations:
        lineNumbers: A=90+1*299
        columnNumbers: A=9*300
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.WebSockets.model.yml
      index: 0
      firstRowId: 26760
      rowCount: 17
      locations:
        lineNumbers: A=7+1*16
        columnNumbers: A=9*17
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.WebSockets.model.yml
      index: 1
      firstRowId: 26777
      rowCount: 66
      locations:
        lineNumbers: A=28+1*65
        columnNumbers: A=9*66
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.model.yml
      index: 0
      firstRowId: 26843
      rowCount: 142
      locations:
        lineNumbers: A=7+1*141
        columnNumbers: A=9*142
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Net.model.yml
      index: 1
      firstRowId: 26985
      rowCount: 461
      locations:
        lineNumbers: A=153+1*460
        columnNumbers: A=9*461
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Numerics.Tensors.model.yml
      index: 0
      firstRowId: 27446
      rowCount: 168
      locations:
        lineNumbers: A=7+1*167
        columnNumbers: A=9*168
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Numerics.Tensors.model.yml
      index: 1
      firstRowId: 27614
      rowCount: 551
      locations:
        lineNumbers: A=179+1*550
        columnNumbers: A=9*551
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Numerics.model.yml
      index: 0
      firstRowId: 28165
      rowCount: 90
      locations:
        lineNumbers: A=7+1*89
        columnNumbers: A=9*90
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Numerics.model.yml
      index: 1
      firstRowId: 28255
      rowCount: 1377
      locations:
        lineNumbers: A=101+1*1376
        columnNumbers: A=9*1377
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.Context.model.yml
      index: 0
      firstRowId: 29632
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.Context.model.yml
      index: 1
      firstRowId: 29635
      rowCount: 3
      locations:
        lineNumbers: A=14+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.Emit.model.yml
      index: 0
      firstRowId: 29638
      rowCount: 156
      locations:
        lineNumbers: A=7+1*155
        columnNumbers: A=9*156
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.Emit.model.yml
      index: 1
      firstRowId: 29794
      rowCount: 431
      locations:
        lineNumbers: A=167+1*430
        columnNumbers: A=9*431
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.Metadata.Ecma335.model.yml
      index: 0
      firstRowId: 30225
      rowCount: 63
      locations:
        lineNumbers: A=7+1*62
        columnNumbers: A=9*63
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.Metadata.Ecma335.model.yml
      index: 1
      firstRowId: 30288
      rowCount: 327
      locations:
        lineNumbers: A=74+1*326
        columnNumbers: A=9*327
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.Metadata.model.yml
      index: 0
      firstRowId: 30615
      rowCount: 216
      locations:
        lineNumbers: A=7+1*215
        columnNumbers: A=9*216
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.Metadata.model.yml
      index: 1
      firstRowId: 30831
      rowCount: 853
      locations:
        lineNumbers: A=227+1*852
        columnNumbers: A=9*853
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.PortableExecutable.model.yml
      index: 0
      firstRowId: 31684
      rowCount: 24
      locations:
        lineNumbers: A=7+1*23
        columnNumbers: A=9*24
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.PortableExecutable.model.yml
      index: 1
      firstRowId: 31708
      rowCount: 155
      locations:
        lineNumbers: A=35+1*154
        columnNumbers: A=9*155
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.model.yml
      index: 0
      firstRowId: 31863
      rowCount: 207
      locations:
        lineNumbers: A=7+1*206
        columnNumbers: A=9*207
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Reflection.model.yml
      index: 1
      firstRowId: 32070
      rowCount: 499
      locations:
        lineNumbers: A=218+1*498
        columnNumbers: A=9*499
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Resources.Extensions.model.yml
      index: 0
      firstRowId: 32569
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Resources.Extensions.model.yml
      index: 1
      firstRowId: 32572
      rowCount: 14
      locations:
        lineNumbers: A=14+1*13
        columnNumbers: A=9*14
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Resources.model.yml
      index: 0
      firstRowId: 32586
      rowCount: 23
      locations:
        lineNumbers: A=7+1*22
        columnNumbers: A=9*23
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Resources.model.yml
      index: 1
      firstRowId: 32609
      rowCount: 49
      locations:
        lineNumbers: A=34+1*48
        columnNumbers: A=9*49
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Caching.Hosting.model.yml
      index: 0
      firstRowId: 32658
      rowCount: 5
      locations:
        lineNumbers: A=7+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Caching.model.yml
      index: 0
      firstRowId: 32663
      rowCount: 26
      locations:
        lineNumbers: A=7+1*25
        columnNumbers: A=9*26
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Caching.model.yml
      index: 1
      firstRowId: 32689
      rowCount: 57
      locations:
        lineNumbers: A=37+1*56
        columnNumbers: A=9*57
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.CompilerServices.model.yml
      index: 0
      firstRowId: 32746
      rowCount: 72
      locations:
        lineNumbers: A=7+1*71
        columnNumbers: A=9*72
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.CompilerServices.model.yml
      index: 1
      firstRowId: 32818
      rowCount: 282
      locations:
        lineNumbers: A=83+1*281
        columnNumbers: A=9*282
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.ConstrainedExecution.model.yml
      index: 0
      firstRowId: 33100
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.ExceptionServices.model.yml
      index: 0
      firstRowId: 33103
      rowCount: 6
      locations:
        lineNumbers: A=7+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.ExceptionServices.model.yml
      index: 1
      firstRowId: 33109
      rowCount: 5
      locations:
        lineNumbers: A=17+1*4
        columnNumbers: A=9*5
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.InteropServices.ComTypes.model.yml
      index: 0
      firstRowId: 33114
      rowCount: 182
      locations:
        lineNumbers: A=7+1*181
        columnNumbers: A=9*182
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.InteropServices.JavaScript.model.yml
      index: 0
      firstRowId: 33296
      rowCount: 162
      locations:
        lineNumbers: A=7+1*161
        columnNumbers: A=9*162
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.InteropServices.Marshalling.model.yml
      index: 0
      firstRowId: 33458
      rowCount: 42
      locations:
        lineNumbers: A=7+1*41
        columnNumbers: A=9*42
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.InteropServices.Marshalling.model.yml
      index: 1
      firstRowId: 33500
      rowCount: 133
      locations:
        lineNumbers: A=53+1*132
        columnNumbers: A=9*133
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.InteropServices.ObjectiveC.model.yml
      index: 0
      firstRowId: 33633
      rowCount: 4
      locations:
        lineNumbers: A=7+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.InteropServices.Swift.model.yml
      index: 0
      firstRowId: 33637
      rowCount: 8
      locations:
        lineNumbers: A=7+1*7
        columnNumbers: A=9*8
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.InteropServices.model.yml
      index: 0
      firstRowId: 33645
      rowCount: 44
      locations:
        lineNumbers: A=7+1*43
        columnNumbers: A=9*44
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.InteropServices.model.yml
      index: 1
      firstRowId: 33689
      rowCount: 634
      locations:
        lineNumbers: A=55+1*633
        columnNumbers: A=9*634
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Intrinsics.Arm.model.yml
      index: 0
      firstRowId: 34323
      rowCount: 4207
      locations:
        lineNumbers: A=7+1*4206
        columnNumbers: A=9*4207
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Intrinsics.Wasm.model.yml
      index: 0
      firstRowId: 38530
      rowCount: 471
      locations:
        lineNumbers: A=7+1*470
        columnNumbers: A=9*471
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Intrinsics.X86.model.yml
      index: 0
      firstRowId: 39001
      rowCount: 3062
      locations:
        lineNumbers: A=7+1*3061
        columnNumbers: A=9*3062
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Intrinsics.model.yml
      index: 0
      firstRowId: 42063
      rowCount: 68
      locations:
        lineNumbers: A=7+1*67
        columnNumbers: A=9*68
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Intrinsics.model.yml
      index: 1
      firstRowId: 42131
      rowCount: 1179
      locations:
        lineNumbers: A=79+1*1178
        columnNumbers: A=9*1179
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Loader.model.yml
      index: 0
      firstRowId: 43310
      rowCount: 8
      locations:
        lineNumbers: A=7+1*7
        columnNumbers: A=9*8
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Loader.model.yml
      index: 1
      firstRowId: 43318
      rowCount: 28
      locations:
        lineNumbers: A=19+1*27
        columnNumbers: A=9*28
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Remoting.model.yml
      index: 0
      firstRowId: 43346
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Serialization.DataContracts.model.yml
      index: 0
      firstRowId: 43348
      rowCount: 9
      locations:
        lineNumbers: A=7+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Serialization.DataContracts.model.yml
      index: 1
      firstRowId: 43357
      rowCount: 19
      locations:
        lineNumbers: A=20+1*18
        columnNumbers: A=9*19
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Serialization.Formatters.Binary.model.yml
      index: 0
      firstRowId: 43376
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Serialization.Formatters.Binary.model.yml
      index: 1
      firstRowId: 43378
      rowCount: 2
      locations:
        lineNumbers: A=13+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Serialization.Json.model.yml
      index: 0
      firstRowId: 43380
      rowCount: 18
      locations:
        lineNumbers: A=7+1*17
        columnNumbers: A=9*18
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Serialization.Json.model.yml
      index: 1
      firstRowId: 43398
      rowCount: 35
      locations:
        lineNumbers: A=29+1*34
        columnNumbers: A=9*35
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Serialization.model.yml
      index: 0
      firstRowId: 43433
      rowCount: 68
      locations:
        lineNumbers: A=7+1*67
        columnNumbers: A=9*68
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Serialization.model.yml
      index: 1
      firstRowId: 43501
      rowCount: 247
      locations:
        lineNumbers: A=79+1*246
        columnNumbers: A=9*247
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Versioning.model.yml
      index: 0
      firstRowId: 43748
      rowCount: 12
      locations:
        lineNumbers: A=7+1*11
        columnNumbers: A=9*12
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.Versioning.model.yml
      index: 1
      firstRowId: 43760
      rowCount: 28
      locations:
        lineNumbers: A=23+1*27
        columnNumbers: A=9*28
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.model.yml
      index: 0
      firstRowId: 43788
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Runtime.model.yml
      index: 1
      firstRowId: 43789
      rowCount: 18
      locations:
        lineNumbers: A=12+1*17
        columnNumbers: A=9*18
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.AccessControl.model.yml
      index: 0
      firstRowId: 43807
      rowCount: 382
      locations:
        lineNumbers: A=7+1*381
        columnNumbers: A=9*382
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Authentication.ExtendedProtection.model.yml
      index: 0
      firstRowId: 44189
      rowCount: 10
      locations:
        lineNumbers: A=7+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Authentication.ExtendedProtection.model.yml
      index: 1
      firstRowId: 44199
      rowCount: 10
      locations:
        lineNumbers: A=21+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Authentication.model.yml
      index: 0
      firstRowId: 44209
      rowCount: 6
      locations:
        lineNumbers: A=7+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Claims.model.yml
      index: 0
      firstRowId: 44215
      rowCount: 37
      locations:
        lineNumbers: A=7+1*36
        columnNumbers: A=9*37
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Claims.model.yml
      index: 1
      firstRowId: 44252
      rowCount: 48
      locations:
        lineNumbers: A=48+1*47
        columnNumbers: A=9*48
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.Cose.model.yml
      index: 0
      firstRowId: 44300
      rowCount: 14
      locations:
        lineNumbers: A=7+1*13
        columnNumbers: A=9*14
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.Cose.model.yml
      index: 1
      firstRowId: 44314
      rowCount: 97
      locations:
        lineNumbers: A=25+1*96
        columnNumbers: A=9*97
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.Pkcs.model.yml
      index: 0
      firstRowId: 44411
      rowCount: 57
      locations:
        lineNumbers: A=7+1*56
        columnNumbers: A=9*57
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.Pkcs.model.yml
      index: 1
      firstRowId: 44468
      rowCount: 190
      locations:
        lineNumbers: A=68+1*189
        columnNumbers: A=9*190
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.X509Certificates.model.yml
      index: 0
      firstRowId: 44658
      rowCount: 70
      locations:
        lineNumbers: A=7+1*69
        columnNumbers: A=9*70
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.X509Certificates.model.yml
      index: 1
      firstRowId: 44728
      rowCount: 322
      locations:
        lineNumbers: A=81+1*321
        columnNumbers: A=9*322
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.Xml.model.yml
      index: 0
      firstRowId: 45050
      rowCount: 108
      locations:
        lineNumbers: A=7+1*107
        columnNumbers: A=9*108
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.Xml.model.yml
      index: 1
      firstRowId: 45158
      rowCount: 11
      locations:
        lineNumbers: A=119+1*10
        columnNumbers: A=9*11
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.Xml.model.yml
      index: 2
      firstRowId: 45169
      rowCount: 99
      locations:
        lineNumbers: A=134+1*98
        columnNumbers: A=9*99
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.model.yml
      index: 0
      firstRowId: 45268
      rowCount: 149
      locations:
        lineNumbers: A=7+1*148
        columnNumbers: A=9*149
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.model.yml
      index: 1
      firstRowId: 45417
      rowCount: 2
      locations:
        lineNumbers: A=160+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Cryptography.model.yml
      index: 2
      firstRowId: 45419
      rowCount: 1478
      locations:
        lineNumbers: A=166+1*1477
        columnNumbers: A=9*1478
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Permissions.model.yml
      index: 0
      firstRowId: 46897
      rowCount: 239
      locations:
        lineNumbers: A=7+1*238
        columnNumbers: A=9*239
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Policy.model.yml
      index: 0
      firstRowId: 47136
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Policy.model.yml
      index: 1
      firstRowId: 47138
      rowCount: 282
      locations:
        lineNumbers: A=13+1*281
        columnNumbers: A=9*282
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Principal.model.yml
      index: 0
      firstRowId: 47420
      rowCount: 13
      locations:
        lineNumbers: A=7+1*12
        columnNumbers: A=9*13
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.Principal.model.yml
      index: 1
      firstRowId: 47433
      rowCount: 96
      locations:
        lineNumbers: A=24+1*95
        columnNumbers: A=9*96
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.model.yml
      index: 0
      firstRowId: 47529
      rowCount: 18
      locations:
        lineNumbers: A=7+1*17
        columnNumbers: A=9*18
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Security.model.yml
      index: 1
      firstRowId: 47547
      rowCount: 146
      locations:
        lineNumbers: A=29+1*145
        columnNumbers: A=9*146
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ServiceModel.Syndication.model.yml
      index: 0
      firstRowId: 47693
      rowCount: 127
      locations:
        lineNumbers: A=7+1*126
        columnNumbers: A=9*127
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ServiceModel.Syndication.model.yml
      index: 1
      firstRowId: 47820
      rowCount: 305
      locations:
        lineNumbers: A=138+1*304
        columnNumbers: A=9*305
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.ServiceProcess.model.yml
      index: 0
      firstRowId: 48125
      rowCount: 77
      locations:
        lineNumbers: A=7+1*76
        columnNumbers: A=9*77
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Speech.AudioFormat.model.yml
      index: 0
      firstRowId: 48202
      rowCount: 11
      locations:
        lineNumbers: A=7+1*10
        columnNumbers: A=9*11
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Speech.Recognition.SrgsGrammar.model.yml
      index: 0
      firstRowId: 48213
      rowCount: 54
      locations:
        lineNumbers: A=7+1*53
        columnNumbers: A=9*54
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Speech.Recognition.model.yml
      index: 0
      firstRowId: 48267
      rowCount: 241
      locations:
        lineNumbers: A=7+1*240
        columnNumbers: A=9*241
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Speech.Synthesis.TtsEngine.model.yml
      index: 0
      firstRowId: 48508
      rowCount: 59
      locations:
        lineNumbers: A=7+1*58
        columnNumbers: A=9*59
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Speech.Synthesis.model.yml
      index: 0
      firstRowId: 48567
      rowCount: 128
      locations:
        lineNumbers: A=7+1*127
        columnNumbers: A=9*128
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Encodings.Web.model.yml
      index: 0
      firstRowId: 48695
      rowCount: 6
      locations:
        lineNumbers: A=7+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Encodings.Web.model.yml
      index: 1
      firstRowId: 48701
      rowCount: 28
      locations:
        lineNumbers: A=17+1*27
        columnNumbers: A=9*28
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Json.Nodes.model.yml
      index: 0
      firstRowId: 48729
      rowCount: 19
      locations:
        lineNumbers: A=7+1*18
        columnNumbers: A=9*19
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Json.Nodes.model.yml
      index: 1
      firstRowId: 48748
      rowCount: 77
      locations:
        lineNumbers: A=30+1*76
        columnNumbers: A=9*77
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Json.Schema.model.yml
      index: 0
      firstRowId: 48825
      rowCount: 7
      locations:
        lineNumbers: A=7+1*6
        columnNumbers: A=9*7
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Json.Serialization.Metadata.model.yml
      index: 0
      firstRowId: 48832
      rowCount: 33
      locations:
        lineNumbers: A=7+1*32
        columnNumbers: A=9*33
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Json.Serialization.Metadata.model.yml
      index: 1
      firstRowId: 48865
      rowCount: 67
      locations:
        lineNumbers: A=44+1*66
        columnNumbers: A=9*67
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Json.Serialization.model.yml
      index: 0
      firstRowId: 48932
      rowCount: 9
      locations:
        lineNumbers: A=7+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Json.Serialization.model.yml
      index: 1
      firstRowId: 48941
      rowCount: 46
      locations:
        lineNumbers: A=20+1*45
        columnNumbers: A=9*46
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Json.SourceGeneration.model.yml
      index: 0
      firstRowId: 48987
      rowCount: 1
      locations:
        lineNumbers: A=7
        columnNumbers: A=9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Json.SourceGeneration.model.yml
      index: 1
      firstRowId: 48988
      rowCount: 17
      locations:
        lineNumbers: A=12+1*16
        columnNumbers: A=9*17
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Json.model.yml
      index: 0
      firstRowId: 49005
      rowCount: 73
      locations:
        lineNumbers: A=7+1*72
        columnNumbers: A=9*73
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Json.model.yml
      index: 1
      firstRowId: 49078
      rowCount: 323
      locations:
        lineNumbers: A=84+1*322
        columnNumbers: A=9*323
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.RegularExpressions.Generator.model.yml
      index: 0
      firstRowId: 49401
      rowCount: 6
      locations:
        lineNumbers: A=7+1*5
        columnNumbers: A=9*6
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.RegularExpressions.model.yml
      index: 0
      firstRowId: 49407
      rowCount: 104
      locations:
        lineNumbers: A=7+1*103
        columnNumbers: A=9*104
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.RegularExpressions.model.yml
      index: 1
      firstRowId: 49511
      rowCount: 115
      locations:
        lineNumbers: A=115+1*114
        columnNumbers: A=9*115
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Unicode.model.yml
      index: 0
      firstRowId: 49626
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.Unicode.model.yml
      index: 1
      firstRowId: 49629
      rowCount: 183
      locations:
        lineNumbers: A=14+1*182
        columnNumbers: A=9*183
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.model.yml
      index: 0
      firstRowId: 49812
      rowCount: 86
      locations:
        lineNumbers: A=7+1*85
        columnNumbers: A=9*86
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Text.model.yml
      index: 1
      firstRowId: 49898
      rowCount: 324
      locations:
        lineNumbers: A=97+1*323
        columnNumbers: A=9*324
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Threading.Channels.model.yml
      index: 0
      firstRowId: 50222
      rowCount: 26
      locations:
        lineNumbers: A=7+1*25
        columnNumbers: A=9*26
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Threading.RateLimiting.model.yml
      index: 0
      firstRowId: 50248
      rowCount: 18
      locations:
        lineNumbers: A=7+1*17
        columnNumbers: A=9*18
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Threading.RateLimiting.model.yml
      index: 1
      firstRowId: 50266
      rowCount: 65
      locations:
        lineNumbers: A=29+1*64
        columnNumbers: A=9*65
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Threading.Tasks.Dataflow.model.yml
      index: 0
      firstRowId: 50331
      rowCount: 74
      locations:
        lineNumbers: A=7+1*73
        columnNumbers: A=9*74
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Threading.Tasks.Dataflow.model.yml
      index: 1
      firstRowId: 50405
      rowCount: 137
      locations:
        lineNumbers: A=85+1*136
        columnNumbers: A=9*137
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Threading.Tasks.Sources.model.yml
      index: 0
      firstRowId: 50542
      rowCount: 3
      locations:
        lineNumbers: A=7+1*2
        columnNumbers: A=9*3
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Threading.Tasks.Sources.model.yml
      index: 1
      firstRowId: 50545
      rowCount: 10
      locations:
        lineNumbers: A=14+1*9
        columnNumbers: A=9*10
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Threading.Tasks.model.yml
      index: 0
      firstRowId: 50555
      rowCount: 168
      locations:
        lineNumbers: A=7+1*167
        columnNumbers: A=9*168
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Threading.Tasks.model.yml
      index: 1
      firstRowId: 50723
      rowCount: 221
      locations:
        lineNumbers: A=179+1*220
        columnNumbers: A=9*221
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Threading.model.yml
      index: 0
      firstRowId: 50944
      rowCount: 50
      locations:
        lineNumbers: A=7+1*49
        columnNumbers: A=9*50
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Threading.model.yml
      index: 1
      firstRowId: 50994
      rowCount: 519
      locations:
        lineNumbers: A=61+1*518
        columnNumbers: A=9*519
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Timers.model.yml
      index: 0
      firstRowId: 51513
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Timers.model.yml
      index: 1
      firstRowId: 51515
      rowCount: 13
      locations:
        lineNumbers: A=13+1*12
        columnNumbers: A=9*13
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Transactions.model.yml
      index: 0
      firstRowId: 51528
      rowCount: 21
      locations:
        lineNumbers: A=7+1*20
        columnNumbers: A=9*21
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Transactions.model.yml
      index: 1
      firstRowId: 51549
      rowCount: 96
      locations:
        lineNumbers: A=32+1*95
        columnNumbers: A=9*96
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Web.model.yml
      index: 0
      firstRowId: 51645
      rowCount: 4
      locations:
        lineNumbers: A=7+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Web.model.yml
      index: 1
      firstRowId: 51649
      rowCount: 22
      locations:
        lineNumbers: A=15+1*21
        columnNumbers: A=9*22
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Windows.Input.model.yml
      index: 0
      firstRowId: 51671
      rowCount: 4
      locations:
        lineNumbers: A=7+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Windows.Markup.model.yml
      index: 0
      firstRowId: 51675
      rowCount: 2
      locations:
        lineNumbers: A=7+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Windows.Markup.model.yml
      index: 1
      firstRowId: 51677
      rowCount: 2
      locations:
        lineNumbers: A=13+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xaml.Permissions.model.yml
      index: 0
      firstRowId: 51679
      rowCount: 17
      locations:
        lineNumbers: A=7+1*16
        columnNumbers: A=9*17
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Linq.model.yml
      index: 0
      firstRowId: 51696
      rowCount: 151
      locations:
        lineNumbers: A=7+1*150
        columnNumbers: A=9*151
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Linq.model.yml
      index: 1
      firstRowId: 51847
      rowCount: 114
      locations:
        lineNumbers: A=162+1*113
        columnNumbers: A=9*114
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Resolvers.model.yml
      index: 0
      firstRowId: 51961
      rowCount: 4
      locations:
        lineNumbers: A=7+1*3
        columnNumbers: A=9*4
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Resolvers.model.yml
      index: 1
      firstRowId: 51965
      rowCount: 9
      locations:
        lineNumbers: A=15+1*8
        columnNumbers: A=9*9
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Schema.model.yml
      index: 0
      firstRowId: 51974
      rowCount: 124
      locations:
        lineNumbers: A=7+1*123
        columnNumbers: A=9*124
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Schema.model.yml
      index: 1
      firstRowId: 52098
      rowCount: 110
      locations:
        lineNumbers: A=135+1*109
        columnNumbers: A=9*110
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Serialization.model.yml
      index: 0
      firstRowId: 52208
      rowCount: 296
      locations:
        lineNumbers: A=7+1*295
        columnNumbers: A=9*296
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Serialization.model.yml
      index: 1
      firstRowId: 52504
      rowCount: 205
      locations:
        lineNumbers: A=307+1*204
        columnNumbers: A=9*205
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.XPath.model.yml
      index: 0
      firstRowId: 52709
      rowCount: 57
      locations:
        lineNumbers: A=7+1*56
        columnNumbers: A=9*57
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.XPath.model.yml
      index: 1
      firstRowId: 52766
      rowCount: 102
      locations:
        lineNumbers: A=68+1*101
        columnNumbers: A=9*102
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Xsl.Runtime.model.yml
      index: 0
      firstRowId: 52868
      rowCount: 172
      locations:
        lineNumbers: A=7+1*171
        columnNumbers: A=9*172
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Xsl.Runtime.model.yml
      index: 1
      firstRowId: 53040
      rowCount: 203
      locations:
        lineNumbers: A=183+1*202
        columnNumbers: A=9*203
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Xsl.model.yml
      index: 0
      firstRowId: 53243
      rowCount: 25
      locations:
        lineNumbers: A=7+1*24
        columnNumbers: A=9*25
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.Xsl.model.yml
      index: 1
      firstRowId: 53268
      rowCount: 75
      locations:
        lineNumbers: A=36+1*74
        columnNumbers: A=9*75
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.model.yml
      index: 0
      firstRowId: 53343
      rowCount: 511
      locations:
        lineNumbers: A=7+1*510
        columnNumbers: A=9*511
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.Xml.model.yml
      index: 1
      firstRowId: 53854
      rowCount: 503
      locations:
        lineNumbers: A=522+1*502
        columnNumbers: A=9*503
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.model.yml
      index: 0
      firstRowId: 54357
      rowCount: 754
      locations:
        lineNumbers: A=7+1*753
        columnNumbers: A=9*754
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.model.yml
      index: 1
      firstRowId: 55111
      rowCount: 2
      locations:
        lineNumbers: A=765+1
        columnNumbers: A=9*2
     -
      pack: codeql/csharp-all#0
      relativePath: ext/generated/System.model.yml
      index: 2
      firstRowId: 55113
      rowCount: 4759
      locations:
        lineNumbers: A=771+1*4758
        columnNumbers: A=9*4759
     -
      pack: codeql/threat-models#1
      relativePath: ext/supported-threat-models.model.yml
      index: 0
      firstRowId: 59872
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/threat-models#1
      relativePath: ext/threat-model-grouping.model.yml
      index: 0
      firstRowId: 59873
      rowCount: 15
      locations:
        lineNumbers: A=8+3+1+3+1*5+3+1+5+1*3
        columnNumbers: A=9*15
  codeql/util#2:
    name: codeql/util
    version: 2.0.10
    isLibrary: true
    isExtensionPack: false
    localPath: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/util/2.0.10/
    localPackDefinitionFile: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/util/2.0.10/qlpack.yml
    headSha: 4bb829ebec2082e8c06f02a7d9c07e181d27c784
    runDataExtensions: []
  codeql/threat-models#1:
    name: codeql/threat-models
    version: 1.0.23
    isLibrary: true
    isExtensionPack: false
    localPath: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/threat-models/1.0.23/
    localPackDefinitionFile: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/threat-models/1.0.23/qlpack.yml
    headSha: 4bb829ebec2082e8c06f02a7d9c07e181d27c784
    runDataExtensions: []
