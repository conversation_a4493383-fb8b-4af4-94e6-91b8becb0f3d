<dbstats>
  <typesizes><e>
  <k>@py_ParamSpec</k><v>100</v></e><e>
  <k>@py_TypeAlias</k><v>100</v></e><e>
  <k>@py_TypeVar</k><v>100</v></e><e>
  <k>@py_TypeVarTuple</k><v>100</v></e><e>
  <k>@py_type_parameter_list</k><v>100</v></e><e>
<k>@py_Guard</k><v>100</v></e><e>
<k>@py_MatchAsPattern</k><v>100</v></e><e>
<k>@py_MatchOrPattern</k><v>100</v></e><e>
<k>@py_MatchLiteralPattern</k><v>100</v></e><e>
<k>@py_MatchCapturePattern</k><v>100</v></e><e>
<k>@py_MatchWildcardPattern</k><v>100</v></e><e>
<k>@py_MatchValuePattern</k><v>100</v></e><e>
<k>@py_MatchSequencePattern</k><v>100</v></e><e>
<k>@py_MatchStarPattern</k><v>100</v></e><e>
<k>@py_MatchMappingPattern</k><v>100</v></e><e>
<k>@py_MatchDoubleStarPattern</k><v>100</v></e><e>
<k>@py_MatchKeyValuePattern</k><v>100</v></e><e>
<k>@py_MatchClassPattern</k><v>100</v></e><e>
<k>@py_MatchKeywordPattern</k><v>100</v></e><e>
<k>@py_Case</k><v>100</v></e><e>
<k>@py_MatchStmt</k><v>100</v></e><e>
<k>@py_pattern_list</k><v>100</v></e><e>
<k>@externalDefect</k>
<v>100</v>
</e>
<e>
<k>@externalMetric</k>
<v>100</v>
</e>
<e>
<k>@externalDataElement</k>
<v>20</v>
</e>
<e>
<k>@duplication</k>
<v>890</v>
</e>
<e>
<k>@similarity</k>
<v>5591</v>
</e>
<e>
<k>@svnentry</k>
<v>100</v>
</e>
<e>
<k>@file</k>
<v>3066</v>
</e>
<e>
<k>@folder</k>
<v>686</v>
</e>
<e>
<k>@location_default</k>
<v>100</v>
</e>
<e>
<k>@location_ast</k>
<v>2310679</v>
</e>
<e>
<k>@py_variable</k>
<v>242770</v>
</e>
<e>
<k>@py_line</k>
<v>100</v>
</e>
<e>
<k>@py_Class</k>
<v>10244</v>
</e>
<e>
<k>@py_Function</k>
<v>44860</v>
</e>
<e>
<k>@py_Module</k>
<v>5983</v>
</e>
<e>
<k>@py_StringPart</k>
<v>6399</v>
</e>
<e>
<k>@py_StringPart_list</k>
<v>2296</v>
</e>
<e>
<k>@py_alias</k>
<v>21374</v>
</e>
<e>
<k>@py_alias_list</k>
<v>14396</v>
</e>
<e>
<k>@py_arguments</k>
<v>41982</v>
</e>
<e>
<k>@py_boolop</k>
<v>10907</v>
</e>
<e>
<k>@py_And</k>
<v>7243</v>
</e>
<e>
<k>@py_Or</k>
<v>3663</v>
</e>
<e>
<k>@py_cmpop</k>
<v>38007</v>
</e>
<e>
<k>@py_Eq</k>
<v>11370</v>
</e>
<e>
<k>@py_Gt</k>
<v>1999</v>
</e>
<e>
<k>@py_GtE</k>
<v>1306</v>
</e>
<e>
<k>@py_In</k>
<v>4743</v>
</e>
<e>
<k>@py_Is</k>
<v>6368</v>
</e>
<e>
<k>@py_IsNot</k>
<v>4541</v>
</e>
<e>
<k>@py_Lt</k>
<v>1920</v>
</e>
<e>
<k>@py_LtE</k>
<v>1128</v>
</e>
<e>
<k>@py_NotEq</k>
<v>3050</v>
</e>
<e>
<k>@py_NotIn</k>
<v>1672</v>
</e>
<e>
<k>@py_cmpop_list</k>
<v>37666</v>
</e>
<e>
<k>@py_comprehension</k>
<v>1688</v>
</e>
<e>
<k>@py_comprehension_list</k>
<v>1682</v>
</e>
<e>
<k>@py_dict_item</k>
<v>167901</v>
</e>
<e>
<k>@py_DictUnpacking</k>
<v>1521</v>
</e>
<e>
<k>@py_KeyValuePair</k>
<v>92837</v>
</e>
<e>
<k>@py_keyword</k>
<v>74612</v>
</e>
<e>
<k>@py_dict_item_list</k>
<v>33758</v>
</e>
<e>
<k>@py_expr</k>
<v>1684031</v>
</e>
<e>
<k>@py_Attribute</k>
<v>249565</v>
</e>
<e>
<k>@py_BinaryExpr</k>
<v>28868</v>
</e>
<e>
<k>@py_BoolExpr</k>
<v>10907</v>
</e>
<e>
<k>@py_Bytes</k>
<v>105600</v>
</e>
<e>
<k>@py_Call</k>
<v>198138</v>
</e>
<e>
<k>@py_ClassExpr</k>
<v>10244</v>
</e>
<e>
<k>@py_Compare</k>
<v>37666</v>
</e>
<e>
<k>@py_Dict</k>
<v>9635</v>
</e>
<e>
<k>@py_DictComp</k>
<v>99</v>
</e>
<e>
<k>@py_Ellipsis</k>
<v>115</v>
</e>
<e>
<k>@py_Fstring</k>
<v>100</v>
</e>
<e>
<k>@py_FormattedValue</k>
<v>100</v>
</e>
<e>
<k>@py_FunctionExpr</k>
<v>41531</v>
</e>
<e>
<k>@py_GeneratorExp</k>
<v>1066</v>
</e>
<e>
<k>@py_IfExp</k>
<v>923</v>
</e>
<e>
<k>@py_ImportExpr</k>
<v>21532</v>
</e>
<e>
<k>@py_ImportMember</k>
<v>17714</v>
</e>
<e>
<k>@py_Lambda</k>
<v>870</v>
</e>
<e>
<k>@py_List</k>
<v>23200</v>
</e>
<e>
<k>@py_ListComp</k>
<v>1690</v>
</e>
<e>
<k>@py_Name</k>
<v>845963</v>
</e>
<e>
<k>@py_Num</k>
<v>58723</v>
</e>
<e>
<k>@py_Set</k>
<v>261</v>
</e>
<e>
<k>@py_SetComp</k>
<v>49</v>
</e>
<e>
<k>@py_Slice</k>
<v>5316</v>
</e>
<e>
<k>@py_Starred</k>
<v>1265</v>
</e>
<e>
<k>@py_Str</k>
<v>288427</v>
</e>
<e>
<k>@py_Subscript</k>
<v>31583</v>
</e>
<e>
<k>@py_Tuple</k>
<v>27693</v>
</e>
<e>
<k>@py_UnaryExpr</k>
<v>13295</v>
</e>
<e>
<k>@py_Yield</k>
<v>3941</v>
</e>
<e>
<k>@py_YieldFrom</k>
<v>398</v>
</e>
<e>
<k>@py_Repr</k>
<v>100</v>
</e>
<e>
<k>@py_TemplateDottedNotation</k>
<v>100</v>
</e>
<e>
<k>@py_Filter</k>
<v>100</v>
</e>
<e>
<k>@py_PlaceHolder</k>
<v>100</v>
</e>
<e>
<k>@py_Await</k>
<v>500</v>
</e>
<e>
<k>@py_AssignExpr</k>
<v>200</v>
</e>
<e>
<k>@py_SpecialOperation</k>
<v>100</v>
</e>
<e>
<k>@py_expr_context</k>
<v>1140675</v>
</e>
<e>
<k>@py_Del</k>
<v>1324</v>
</e>
<e>
<k>@py_Load</k>
<v>853094</v>
</e>
<e>
<k>@py_Param</k>
<v>96047</v>
</e>
<e>
<k>@py_Store</k>
<v>198700</v>
</e>
<e>
<k>@py_AugLoad</k>
<v>100</v>
</e>
<e>
<k>@py_AugStore</k>
<v>100</v>
</e>
<e>
<k>@py_expr_list</k>
<v>430986</v>
</e>
<e>
<k>@py_operator</k>
<v>28868</v>
</e>
<e>
<k>@py_Add</k>
<v>13603</v>
</e>
<e>
<k>@py_BitAnd</k>
<v>796</v>
</e>
<e>
<k>@py_BitOr</k>
<v>799</v>
</e>
<e>
<k>@py_BitXor</k>
<v>190</v>
</e>
<e>
<k>@py_Div</k>
<v>393</v>
</e>
<e>
<k>@py_FloorDiv</k>
<v>362</v>
</e>
<e>
<k>@py_LShift</k>
<v>279</v>
</e>
<e>
<k>@py_Mod</k>
<v>8234</v>
</e>
<e>
<k>@py_Mult</k>
<v>2218</v>
</e>
<e>
<k>@py_Pow</k>
<v>501</v>
</e>
<e>
<k>@py_RShift</k>
<v>157</v>
</e>
<e>
<k>@py_Sub</k>
<v>3136</v>
</e>
<e>
<k>@py_MatMult</k>
<v>100</v>
</e>
<e>
<k>@py_parameter_list</k>
<v>43271</v>
</e>
<e>
<k>@py_stmt</k>
<v>372643</v>
</e>
<e>
<k>@py_Assert</k>
<v>1999</v>
</e>
<e>
<k>@py_Assign</k>
<v>151576</v>
</e>
<e>
<k>@py_AugAssign</k>
<v>3656</v>
</e>
<e>
<k>@py_Break</k>
<v>1699</v>
</e>
<e>
<k>@py_Continue</k>
<v>1199</v>
</e>
<e>
<k>@py_Delete</k>
<v>1149</v>
</e>
<e>
<k>@py_ExceptStmt</k>
<v>5610</v>
</e>
<e>
<k>@py_ExceptGroupStmt</k>
<v>1000</v>
</e>
<e>
<k>@py_Expr_stmt</k>
<v>76750</v>
</e>
<e>
<k>@py_For</k>
<v>11495</v>
</e>
<e>
<k>@py_Global</k>
<v>392</v>
</e>
<e>
<k>@py_If</k>
<v>53619</v>
</e>
<e>
<k>@py_Import</k>
<v>14396</v>
</e>
<e>
<k>@py_ImportStar</k>
<v>158</v>
</e>
<e>
<k>@py_Nonlocal</k>
<v>35</v>
</e>
<e>
<k>@py_Pass</k>
<v>2872</v>
</e>
<e>
<k>@py_Raise</k>
<v>7794</v>
</e>
<e>
<k>@py_Return</k>
<v>36127</v>
</e>
<e>
<k>@py_Try</k>
<v>6210</v>
</e>
<e>
<k>@py_While</k>
<v>2138</v>
</e>
<e>
<k>@py_With</k>
<v>4193</v>
</e>
<e>
<k>@py_Exec</k>
<v>43</v>
</e>
<e>
<k>@py_Print</k>
<v>1032</v>
</e>
<e>
<k>@py_TemplateWrite</k>
<v>100</v>
</e>
<e>
<k>@py_AnnAssign</k>
<v>100</v>
</e>
<e>
<k>@py_stmt_list</k>
<v>156700</v>
</e>
<e>
<k>@py_str_list</k>
<v>427</v>
</e>
<e>
<k>@py_unaryop</k>
<v>13295</v>
</e>
<e>
<k>@py_Invert</k>
<v>107</v>
</e>
<e>
<k>@py_Not</k>
<v>8655</v>
</e>
<e>
<k>@py_UAdd</k>
<v>14</v>
</e>
<e>
<k>@py_USub</k>
<v>4565</v>
</e>
<e>
<k>@py_flow_node</k>
<v>2323431</v>
</e>
<e>
<k>@py_ssa_var</k>
<v>272292</v>
</e>
<e>
<k>@py_comment</k>
<v>77830</v>
</e>
<e>
<k>@py_cobject</k>
<v>112856</v>
</e>
<e>
<k>@xmldtd</k>
<v>100</v>
</e>
<e>
<k>@xmlelement</k>
<v>100</v>
</e>
<e>
<k>@xmlattribute</k>
<v>100</v>
</e>
<e>
<k>@xmlnamespace</k>
<v>100</v>
</e>
<e>
<k>@xmlcomment</k>
<v>100</v>
</e>
<e>
<k>@xmlcharacters</k>
<v>100</v>
</e>
<e>
<k>@yaml_node</k>
<v>885</v>
</e>
<e>
<k>@yaml_scalar_node</k>
<v>700</v>
</e>
<e>
<k>@yaml_mapping_node</k>
<v>149</v>
</e>
<e>
<k>@yaml_sequence_node</k>
<v>35</v>
</e>
<e>
<k>@yaml_alias_node</k>
<v>1</v>
</e>
<e>
<k>@yaml_error</k>
<v>1</v>
</e>
</typesizes>
  <stats><relation>
<name>externalDefects</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>queryPath</k>
<v>100</v>
</e>
<e>
<k>location</k>
<v>100</v>
</e>
<e>
<k>message</k>
<v>100</v>
</e>
<e>
<k>severity</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>queryPath</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>severity</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>queryPath</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>queryPath</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>queryPath</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>queryPath</src>
<trg>severity</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>queryPath</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>severity</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>queryPath</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>severity</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>severity</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>severity</src>
<trg>queryPath</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>severity</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>severity</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>externalMetrics</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>queryPath</k>
<v>100</v>
</e>
<e>
<k>location</k>
<v>100</v>
</e>
<e>
<k>value</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>queryPath</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>queryPath</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>queryPath</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>queryPath</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>queryPath</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>queryPath</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>externalData</name>
<cardinality>41</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>20</v>
</e>
<e>
<k>path</k>
<v>2</v>
</e>
<e>
<k>column</k>
<v>5</v>
</e>
<e>
<k>value</k>
<v>41</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>path</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>20</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>column</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>20</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>20</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>path</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>7</a>
<b>8</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>path</src>
<trg>column</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>path</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>14</a>
<b>15</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>column</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>7</a>
<b>8</b>
<v>5</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>column</src>
<trg>path</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>column</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>7</a>
<b>8</b>
<v>5</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>41</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>path</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>41</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>column</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>41</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>snapshotDate</name>
<cardinality>2</cardinality>
<columnsizes>
<e>
<k>snapshotDate</k>
<v>2</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>sourceLocationPrefix</name>
<cardinality>2</cardinality>
<columnsizes>
<e>
<k>prefix</k>
<v>2</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>duplicateCode</name>
<cardinality>890</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>890</v>
</e>
<e>
<k>relativePath</k>
<v>91</v>
</e>
<e>
<k>equivClass</k>
<v>415</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>relativePath</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>890</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>equivClass</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>890</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>relativePath</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>30</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>16</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>4</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>8</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>6</v>
</b>
<b>
<a>8</a>
<b>12</b>
<v>6</v>
</b>
<b>
<a>12</a>
<b>19</b>
<v>6</v>
</b>
<b>
<a>23</a>
<b>47</b>
<v>6</v>
</b>
<b>
<a>48</a>
<b>109</b>
<v>4</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>relativePath</src>
<trg>equivClass</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>38</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>12</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>6</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>8</v>
</b>
<b>
<a>6</a>
<b>10</b>
<v>8</v>
</b>
<b>
<a>10</a>
<b>15</b>
<v>6</v>
</b>
<b>
<a>15</a>
<b>46</b>
<v>6</v>
</b>
<b>
<a>92</a>
<b>105</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>equivClass</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>371</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>31</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>12</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>equivClass</src>
<trg>relativePath</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>95</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>288</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>31</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>similarCode</name>
<cardinality>5591</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>5591</v>
</e>
<e>
<k>relativePath</k>
<v>347</v>
</e>
<e>
<k>equivClass</k>
<v>1696</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>relativePath</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5591</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>equivClass</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5591</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>relativePath</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>44</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>33</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>31</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>30</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>18</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>26</v>
</b>
<b>
<a>11</a>
<b>13</b>
<v>26</v>
</b>
<b>
<a>13</a>
<b>18</b>
<v>29</v>
</b>
<b>
<a>18</a>
<b>23</b>
<v>29</v>
</b>
<b>
<a>23</a>
<b>30</b>
<v>24</v>
</b>
<b>
<a>30</a>
<b>42</b>
<v>26</v>
</b>
<b>
<a>45</a>
<b>155</b>
<v>26</v>
</b>
<b>
<a>161</a>
<b>162</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>relativePath</src>
<trg>equivClass</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>66</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>19</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>20</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>18</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>18</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>27</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>30</v>
</b>
<b>
<a>10</a>
<b>13</b>
<v>26</v>
</b>
<b>
<a>13</a>
<b>18</b>
<v>26</v>
</b>
<b>
<a>18</a>
<b>23</b>
<v>26</v>
</b>
<b>
<a>23</a>
<b>31</b>
<v>31</v>
</b>
<b>
<a>31</a>
<b>53</b>
<v>26</v>
</b>
<b>
<a>54</a>
<b>145</b>
<v>9</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>equivClass</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>937</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>260</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>166</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>88</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>138</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>105</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>equivClass</src>
<trg>relativePath</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>358</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>733</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>216</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>139</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>110</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>127</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>9</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>tokens</name>
<cardinality>889686</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>6481</v>
</e>
<e>
<k>offset</k>
<v>10514</v>
</e>
<e>
<k>beginLine</k>
<v>9882</v>
</e>
<e>
<k>beginColumn</k>
<v>1197</v>
</e>
<e>
<k>endLine</k>
<v>9882</v>
</e>
<e>
<k>endColumn</k>
<v>1207</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>offset</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>100</a>
<b>101</b>
<v>394</v>
</b>
<b>
<a>101</a>
<b>102</b>
<v>750</v>
</b>
<b>
<a>102</a>
<b>103</b>
<v>347</v>
</b>
<b>
<a>103</a>
<b>104</b>
<v>414</v>
</b>
<b>
<a>104</a>
<b>105</b>
<v>405</v>
</b>
<b>
<a>105</a>
<b>107</b>
<v>528</v>
</b>
<b>
<a>107</a>
<b>108</b>
<v>414</v>
</b>
<b>
<a>108</a>
<b>111</b>
<v>513</v>
</b>
<b>
<a>111</a>
<b>117</b>
<v>555</v>
</b>
<b>
<a>117</a>
<b>127</b>
<v>494</v>
</b>
<b>
<a>127</a>
<b>145</b>
<v>490</v>
</b>
<b>
<a>145</a>
<b>176</b>
<v>487</v>
</b>
<b>
<a>176</a>
<b>284</b>
<v>488</v>
</b>
<b>
<a>289</a>
<b>7594</b>
<v>196</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>5</a>
<b>9</b>
<v>396</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>299</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>559</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>432</v>
</b>
<b>
<a>12</a>
<b>13</b>
<v>598</v>
</b>
<b>
<a>13</a>
<b>14</b>
<v>747</v>
</b>
<b>
<a>14</a>
<b>15</b>
<v>541</v>
</b>
<b>
<a>15</a>
<b>17</b>
<v>564</v>
</b>
<b>
<a>17</a>
<b>20</b>
<v>589</v>
</b>
<b>
<a>20</a>
<b>24</b>
<v>573</v>
</b>
<b>
<a>24</a>
<b>28</b>
<v>526</v>
</b>
<b>
<a>28</a>
<b>51</b>
<v>498</v>
</b>
<b>
<a>51</a>
<b>1520</b>
<v>155</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>9</a>
<b>17</b>
<v>516</v>
</b>
<b>
<a>17</a>
<b>22</b>
<v>488</v>
</b>
<b>
<a>22</a>
<b>31</b>
<v>563</v>
</b>
<b>
<a>31</a>
<b>37</b>
<v>566</v>
</b>
<b>
<a>37</a>
<b>43</b>
<v>585</v>
</b>
<b>
<a>43</a>
<b>46</b>
<v>472</v>
</b>
<b>
<a>46</a>
<b>49</b>
<v>591</v>
</b>
<b>
<a>49</a>
<b>51</b>
<v>438</v>
</b>
<b>
<a>51</a>
<b>54</b>
<v>571</v>
</b>
<b>
<a>54</a>
<b>56</b>
<v>443</v>
</b>
<b>
<a>56</a>
<b>59</b>
<v>484</v>
</b>
<b>
<a>59</a>
<b>68</b>
<v>524</v>
</b>
<b>
<a>68</a>
<b>131</b>
<v>234</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>5</a>
<b>9</b>
<v>396</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>299</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>559</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>432</v>
</b>
<b>
<a>12</a>
<b>13</b>
<v>598</v>
</b>
<b>
<a>13</a>
<b>14</b>
<v>747</v>
</b>
<b>
<a>14</a>
<b>15</b>
<v>541</v>
</b>
<b>
<a>15</a>
<b>17</b>
<v>564</v>
</b>
<b>
<a>17</a>
<b>20</b>
<v>589</v>
</b>
<b>
<a>20</a>
<b>24</b>
<v>573</v>
</b>
<b>
<a>24</a>
<b>28</b>
<v>526</v>
</b>
<b>
<a>28</a>
<b>51</b>
<v>502</v>
</b>
<b>
<a>51</a>
<b>1520</b>
<v>150</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>10</a>
<b>18</b>
<v>450</v>
</b>
<b>
<a>18</a>
<b>23</b>
<v>523</v>
</b>
<b>
<a>23</a>
<b>33</b>
<v>531</v>
</b>
<b>
<a>33</a>
<b>39</b>
<v>495</v>
</b>
<b>
<a>39</a>
<b>44</b>
<v>504</v>
</b>
<b>
<a>44</a>
<b>48</b>
<v>533</v>
</b>
<b>
<a>48</a>
<b>51</b>
<v>544</v>
</b>
<b>
<a>51</a>
<b>54</b>
<v>549</v>
</b>
<b>
<a>54</a>
<b>56</b>
<v>492</v>
</b>
<b>
<a>56</a>
<b>58</b>
<v>458</v>
</b>
<b>
<a>58</a>
<b>61</b>
<v>508</v>
</b>
<b>
<a>61</a>
<b>67</b>
<v>498</v>
</b>
<b>
<a>67</a>
<b>133</b>
<v>391</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>offset</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>6935</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>693</v>
</b>
<b>
<a>6</a>
<b>11</b>
<v>706</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>887</v>
</b>
<b>
<a>16</a>
<b>93</b>
<v>790</v>
</b>
<b>
<a>94</a>
<b>4682</b>
<v>499</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>offset</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>6935</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>693</v>
</b>
<b>
<a>6</a>
<b>11</b>
<v>706</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>891</v>
</b>
<b>
<a>16</a>
<b>91</b>
<v>789</v>
</b>
<b>
<a>91</a>
<b>1817</b>
<v>497</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>offset</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6952</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>722</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>674</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>969</v>
</b>
<b>
<a>8</a>
<b>41</b>
<v>797</v>
</b>
<b>
<a>41</a>
<b>169</b>
<v>397</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>offset</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>6935</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>693</v>
</b>
<b>
<a>6</a>
<b>11</b>
<v>706</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>891</v>
</b>
<b>
<a>16</a>
<b>91</b>
<v>789</v>
</b>
<b>
<a>91</a>
<b>1817</b>
<v>497</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>offset</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6973</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>696</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>929</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>801</v>
</b>
<b>
<a>9</a>
<b>57</b>
<v>798</v>
</b>
<b>
<a>57</a>
<b>172</b>
<v>314</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1613</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1931</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>987</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>650</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>825</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>744</v>
</b>
<b>
<a>9</a>
<b>12</b>
<v>772</v>
</b>
<b>
<a>12</a>
<b>17</b>
<v>836</v>
</b>
<b>
<a>17</a>
<b>37</b>
<v>749</v>
</b>
<b>
<a>37</a>
<b>148</b>
<v>742</v>
</b>
<b>
<a>151</a>
<b>217</b>
<v>29</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>offset</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>4</b>
<v>697</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>882</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>746</v>
</b>
<b>
<a>11</a>
<b>15</b>
<v>883</v>
</b>
<b>
<a>15</a>
<b>20</b>
<v>801</v>
</b>
<b>
<a>20</a>
<b>25</b>
<v>756</v>
</b>
<b>
<a>25</a>
<b>32</b>
<v>757</v>
</b>
<b>
<a>32</a>
<b>42</b>
<v>743</v>
</b>
<b>
<a>42</a>
<b>55</b>
<v>742</v>
</b>
<b>
<a>55</a>
<b>72</b>
<v>778</v>
</b>
<b>
<a>72</a>
<b>98</b>
<v>747</v>
</b>
<b>
<a>98</a>
<b>148</b>
<v>751</v>
</b>
<b>
<a>148</a>
<b>211</b>
<v>594</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>3</b>
<v>749</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>686</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>605</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>779</v>
</b>
<b>
<a>10</a>
<b>12</b>
<v>733</v>
</b>
<b>
<a>12</a>
<b>14</b>
<v>714</v>
</b>
<b>
<a>14</a>
<b>17</b>
<v>726</v>
</b>
<b>
<a>17</a>
<b>21</b>
<v>880</v>
</b>
<b>
<a>21</a>
<b>26</b>
<v>872</v>
</b>
<b>
<a>26</a>
<b>32</b>
<v>852</v>
</b>
<b>
<a>32</a>
<b>40</b>
<v>810</v>
</b>
<b>
<a>40</a>
<b>54</b>
<v>771</v>
</b>
<b>
<a>54</a>
<b>184</b>
<v>699</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>9740</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>142</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>3</b>
<v>750</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>666</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>621</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>722</v>
</b>
<b>
<a>10</a>
<b>12</b>
<v>720</v>
</b>
<b>
<a>12</a>
<b>14</b>
<v>699</v>
</b>
<b>
<a>14</a>
<b>17</b>
<v>721</v>
</b>
<b>
<a>17</a>
<b>21</b>
<v>890</v>
</b>
<b>
<a>21</a>
<b>26</b>
<v>862</v>
</b>
<b>
<a>26</a>
<b>32</b>
<v>839</v>
</b>
<b>
<a>32</a>
<b>40</b>
<v>794</v>
</b>
<b>
<a>40</a>
<b>53</b>
<v>790</v>
</b>
<b>
<a>53</a>
<b>81</b>
<v>746</v>
</b>
<b>
<a>81</a>
<b>185</b>
<v>56</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>389</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>200</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>80</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>105</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>90</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>91</v>
</b>
<b>
<a>11</a>
<b>45</b>
<v>91</v>
</b>
<b>
<a>48</a>
<b>2322</b>
<v>90</v>
</b>
<b>
<a>2328</a>
<b>3928</b>
<v>59</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>offset</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>404</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>206</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>65</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>101</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>88</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>94</v>
</b>
<b>
<a>11</a>
<b>33</b>
<v>90</v>
</b>
<b>
<a>33</a>
<b>345</b>
<v>90</v>
</b>
<b>
<a>360</a>
<b>2645</b>
<v>58</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>628</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>204</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>90</v>
</b>
<b>
<a>4</a>
<b>10</b>
<v>99</v>
</b>
<b>
<a>10</a>
<b>750</b>
<v>90</v>
</b>
<b>
<a>762</a>
<b>5047</b>
<v>84</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>628</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>204</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>90</v>
</b>
<b>
<a>4</a>
<b>10</b>
<v>99</v>
</b>
<b>
<a>10</a>
<b>750</b>
<v>90</v>
</b>
<b>
<a>762</a>
<b>5046</b>
<v>84</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>822</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>152</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>95</v>
</b>
<b>
<a>6</a>
<b>31</b>
<v>92</v>
</b>
<b>
<a>31</a>
<b>99</b>
<v>34</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1613</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1931</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>987</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>652</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>823</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>744</v>
</b>
<b>
<a>9</a>
<b>12</b>
<v>772</v>
</b>
<b>
<a>12</a>
<b>17</b>
<v>836</v>
</b>
<b>
<a>17</a>
<b>37</b>
<v>749</v>
</b>
<b>
<a>37</a>
<b>148</b>
<v>742</v>
</b>
<b>
<a>151</a>
<b>217</b>
<v>29</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>offset</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>4</b>
<v>702</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>876</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>749</v>
</b>
<b>
<a>11</a>
<b>15</b>
<v>883</v>
</b>
<b>
<a>15</a>
<b>20</b>
<v>801</v>
</b>
<b>
<a>20</a>
<b>25</b>
<v>756</v>
</b>
<b>
<a>25</a>
<b>32</b>
<v>753</v>
</b>
<b>
<a>32</a>
<b>42</b>
<v>744</v>
</b>
<b>
<a>42</a>
<b>55</b>
<v>743</v>
</b>
<b>
<a>55</a>
<b>72</b>
<v>779</v>
</b>
<b>
<a>72</a>
<b>98</b>
<v>746</v>
</b>
<b>
<a>98</a>
<b>148</b>
<v>751</v>
</b>
<b>
<a>148</a>
<b>211</b>
<v>594</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>9734</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>148</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>3</b>
<v>749</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>685</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>607</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>782</v>
</b>
<b>
<a>10</a>
<b>12</b>
<v>728</v>
</b>
<b>
<a>12</a>
<b>14</b>
<v>714</v>
</b>
<b>
<a>14</a>
<b>17</b>
<v>728</v>
</b>
<b>
<a>17</a>
<b>21</b>
<v>880</v>
</b>
<b>
<a>21</a>
<b>26</b>
<v>873</v>
</b>
<b>
<a>26</a>
<b>32</b>
<v>851</v>
</b>
<b>
<a>32</a>
<b>40</b>
<v>810</v>
</b>
<b>
<a>40</a>
<b>54</b>
<v>771</v>
</b>
<b>
<a>54</a>
<b>184</b>
<v>699</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>3</b>
<v>750</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>664</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>625</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>721</v>
</b>
<b>
<a>10</a>
<b>12</b>
<v>718</v>
</b>
<b>
<a>12</a>
<b>14</b>
<v>702</v>
</b>
<b>
<a>14</a>
<b>17</b>
<v>721</v>
</b>
<b>
<a>17</a>
<b>21</b>
<v>883</v>
</b>
<b>
<a>21</a>
<b>26</b>
<v>862</v>
</b>
<b>
<a>26</a>
<b>32</b>
<v>841</v>
</b>
<b>
<a>32</a>
<b>40</b>
<v>797</v>
</b>
<b>
<a>40</a>
<b>53</b>
<v>792</v>
</b>
<b>
<a>53</a>
<b>81</b>
<v>743</v>
</b>
<b>
<a>81</a>
<b>185</b>
<v>56</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>391</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>192</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>84</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>102</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>92</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>98</v>
</b>
<b>
<a>11</a>
<b>47</b>
<v>91</v>
</b>
<b>
<a>50</a>
<b>2174</b>
<v>91</v>
</b>
<b>
<a>2189</a>
<b>4114</b>
<v>62</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>offset</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>408</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>193</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>74</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>95</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>85</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>103</v>
</b>
<b>
<a>11</a>
<b>36</b>
<v>91</v>
</b>
<b>
<a>37</a>
<b>353</b>
<v>91</v>
</b>
<b>
<a>364</a>
<b>1140</b>
<v>62</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>625</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>211</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>84</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>91</v>
</b>
<b>
<a>8</a>
<b>405</b>
<v>91</v>
</b>
<b>
<a>414</a>
<b>3303</b>
<v>91</v>
</b>
<b>
<a>3320</a>
<b>3523</b>
<v>11</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>812</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>167</v>
</b>
<b>
<a>3</a>
<b>8</b>
<v>95</v>
</b>
<b>
<a>8</a>
<b>33</b>
<v>92</v>
</b>
<b>
<a>33</a>
<b>42</b>
<v>38</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>625</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>211</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>84</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>91</v>
</b>
<b>
<a>8</a>
<b>405</b>
<v>91</v>
</b>
<b>
<a>414</a>
<b>3303</b>
<v>91</v>
</b>
<b>
<a>3320</a>
<b>3523</b>
<v>11</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_codelines</name>
<cardinality>52985</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>52985</v>
</e>
<e>
<k>count</k>
<v>732</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>count</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>52985</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>count</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>307</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>116</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>59</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>61</v>
</b>
<b>
<a>6</a>
<b>11</b>
<v>62</v>
</b>
<b>
<a>11</a>
<b>28</b>
<v>57</v>
</b>
<b>
<a>28</a>
<b>612</b>
<v>55</v>
</b>
<b>
<a>631</a>
<b>13079</b>
<v>15</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_commentlines</name>
<cardinality>52983</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>52983</v>
</e>
<e>
<k>count</k>
<v>198</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>count</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>52983</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>count</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>78</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>26</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>11</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>16</v>
</b>
<b>
<a>6</a>
<b>10</b>
<v>15</v>
</b>
<b>
<a>10</a>
<b>19</b>
<v>15</v>
</b>
<b>
<a>19</a>
<b>48</b>
<v>15</v>
</b>
<b>
<a>49</a>
<b>351</b>
<v>15</v>
</b>
<b>
<a>494</a>
<b>40367</b>
<v>7</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_docstringlines</name>
<cardinality>52983</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>52983</v>
</e>
<e>
<k>count</k>
<v>123</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>count</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>52983</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>count</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>20</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>11</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>9</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>10</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>11</v>
</b>
<b>
<a>8</a>
<b>13</b>
<v>10</v>
</b>
<b>
<a>14</a>
<b>22</b>
<v>11</v>
</b>
<b>
<a>22</a>
<b>29</b>
<v>10</v>
</b>
<b>
<a>29</a>
<b>54</b>
<v>10</v>
</b>
<b>
<a>56</a>
<b>175</b>
<v>10</v>
</b>
<b>
<a>232</a>
<b>5368</b>
<v>10</v>
</b>
<b>
<a>36413</a>
<b>36414</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_alllines</name>
<cardinality>52983</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>52983</v>
</e>
<e>
<k>count</k>
<v>829</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>count</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>52983</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>count</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>361</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>108</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>68</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>47</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>69</v>
</b>
<b>
<a>8</a>
<b>17</b>
<v>65</v>
</b>
<b>
<a>17</a>
<b>93</b>
<v>64</v>
</b>
<b>
<a>113</a>
<b>9596</b>
<v>47</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>svnentries</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>revision</k>
<v>100</v>
</e>
<e>
<k>author</k>
<v>100</v>
</e>
<e>
<k>revisionDate</k>
<v>100</v>
</e>
<e>
<k>changeSize</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>revision</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>author</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>revisionDate</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>changeSize</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>revision</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>revision</src>
<trg>author</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>revision</src>
<trg>revisionDate</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>revision</src>
<trg>changeSize</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>author</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>author</src>
<trg>revision</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>author</src>
<trg>revisionDate</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>author</src>
<trg>changeSize</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>revisionDate</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>revisionDate</src>
<trg>revision</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>revisionDate</src>
<trg>author</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>revisionDate</src>
<trg>changeSize</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>changeSize</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>changeSize</src>
<trg>revision</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>changeSize</src>
<trg>author</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>changeSize</src>
<trg>revisionDate</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>svnaffectedfiles</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>file</k>
<v>100</v>
</e>
<e>
<k>action</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>action</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>action</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>action</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>action</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>svnentrymsg</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>message</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>svnchurn</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>commit</k>
<v>100</v>
</e>
<e>
<k>file</k>
<v>100</v>
</e>
<e>
<k>addedLines</k>
<v>100</v>
</e>
<e>
<k>deletedLines</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>commit</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>commit</src>
<trg>addedLines</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>commit</src>
<trg>deletedLines</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>commit</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>addedLines</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>deletedLines</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>addedLines</src>
<trg>commit</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>addedLines</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>addedLines</src>
<trg>deletedLines</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>deletedLines</src>
<trg>commit</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>deletedLines</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>deletedLines</src>
<trg>addedLines</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>files</name>
<cardinality>3066</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>3066</v>
</e>
<e>
<k>name</k>
<v>3066</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3066</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3066</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>folders</name>
<cardinality>686</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>686</v>
</e>
<e>
<k>name</k>
<v>686</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>686</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>686</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>containerparent</name>
<cardinality>3750</cardinality>
<columnsizes>
<e>
<k>parent</k>
<v>685</v>
</e>
<e>
<k>child</k>
<v>3750</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>parent</src>
<trg>child</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>53</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>202</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>176</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>57</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>34</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>56</v>
</b>
<b>
<a>8</a>
<b>13</b>
<v>54</v>
</b>
<b>
<a>13</a>
<b>149</b>
<v>52</v>
</b>
<b>
<a>204</a>
<b>205</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>child</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3750</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>numlines</name>
<cardinality>2553</cardinality>
<columnsizes>
<e>
<k>element_id</k>
<v>2553</v>
</e>
<e>
<k>num_lines</k>
<v>687</v>
</e>
<e>
<k>num_code</k>
<v>648</v>
</e>
<e>
<k>num_comment</k>
<v>193</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>element_id</src>
<trg>num_lines</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2553</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>element_id</src>
<trg>num_code</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2553</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>element_id</src>
<trg>num_comment</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2553</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_lines</src>
<trg>element_id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>345</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>129</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>44</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>57</v>
</b>
<b>
<a>6</a>
<b>11</b>
<v>54</v>
</b>
<b>
<a>11</a>
<b>34</b>
<v>52</v>
</b>
<b>
<a>35</a>
<b>60</b>
<v>6</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_lines</src>
<trg>num_code</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>348</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>134</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>46</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>41</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>39</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>60</v>
</b>
<b>
<a>9</a>
<b>17</b>
<v>19</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_lines</src>
<trg>num_comment</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>348</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>134</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>46</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>41</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>39</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>60</v>
</b>
<b>
<a>9</a>
<b>17</b>
<v>19</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_code</src>
<trg>element_id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>319</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>110</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>53</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>56</v>
</b>
<b>
<a>6</a>
<b>11</b>
<v>54</v>
</b>
<b>
<a>11</a>
<b>36</b>
<v>49</v>
</b>
<b>
<a>36</a>
<b>56</b>
<v>7</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_code</src>
<trg>num_lines</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>321</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>110</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>62</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>38</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>52</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>51</v>
</b>
<b>
<a>10</a>
<b>14</b>
<v>14</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_code</src>
<trg>num_comment</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>321</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>110</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>62</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>38</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>52</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>51</v>
</b>
<b>
<a>10</a>
<b>14</b>
<v>14</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_comment</src>
<trg>element_id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>72</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>29</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>16</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>15</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>12</v>
</b>
<b>
<a>8</a>
<b>13</b>
<v>15</v>
</b>
<b>
<a>13</a>
<b>29</b>
<v>16</v>
</b>
<b>
<a>30</a>
<b>98</b>
<v>15</v>
</b>
<b>
<a>112</a>
<b>578</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_comment</src>
<trg>num_lines</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>72</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>29</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>16</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>15</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>12</v>
</b>
<b>
<a>8</a>
<b>13</b>
<v>15</v>
</b>
<b>
<a>13</a>
<b>26</b>
<v>15</v>
</b>
<b>
<a>27</a>
<b>75</b>
<v>16</v>
</b>
<b>
<a>75</a>
<b>112</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>num_comment</src>
<trg>num_code</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>72</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>29</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>16</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>15</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>12</v>
</b>
<b>
<a>8</a>
<b>13</b>
<v>15</v>
</b>
<b>
<a>13</a>
<b>26</b>
<v>15</v>
</b>
<b>
<a>27</a>
<b>75</b>
<v>16</v>
</b>
<b>
<a>75</a>
<b>112</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>locations_default</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>file</k>
<v>100</v>
</e>
<e>
<k>beginLine</k>
<v>100</v>
</e>
<e>
<k>beginColumn</k>
<v>100</v>
</e>
<e>
<k>endLine</k>
<v>100</v>
</e>
<e>
<k>endColumn</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>locations_ast</name>
<cardinality>2310679</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>2310679</v>
</e>
<e>
<k>module</k>
<v>1527</v>
</e>
<e>
<k>beginLine</k>
<v>12546</v>
</e>
<e>
<k>beginColumn</k>
<v>2819</v>
</e>
<e>
<k>endLine</k>
<v>12539</v>
</e>
<e>
<k>endColumn</k>
<v>2939</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>module</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2310679</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2310679</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2310679</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2310679</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2310679</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>module</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>288</v>
</b>
<b>
<a>2</a>
<b>30</b>
<v>114</v>
</b>
<b>
<a>30</a>
<b>159</b>
<v>114</v>
</b>
<b>
<a>159</a>
<b>276</b>
<v>114</v>
</b>
<b>
<a>279</a>
<b>427</b>
<v>116</v>
</b>
<b>
<a>434</a>
<b>716</b>
<v>114</v>
</b>
<b>
<a>719</a>
<b>1003</b>
<v>114</v>
</b>
<b>
<a>1007</a>
<b>1409</b>
<v>116</v>
</b>
<b>
<a>1426</a>
<b>1860</b>
<v>114</v>
</b>
<b>
<a>1862</a>
<b>2782</b>
<v>114</v>
</b>
<b>
<a>2798</a>
<b>5578</b>
<v>114</v>
</b>
<b>
<a>5667</a>
<b>58828</b>
<v>87</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>module</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>288</v>
</b>
<b>
<a>2</a>
<b>17</b>
<v>116</v>
</b>
<b>
<a>17</a>
<b>42</b>
<v>114</v>
</b>
<b>
<a>42</a>
<b>72</b>
<v>116</v>
</b>
<b>
<a>72</a>
<b>113</b>
<v>116</v>
</b>
<b>
<a>114</a>
<b>165</b>
<v>116</v>
</b>
<b>
<a>167</a>
<b>231</b>
<v>116</v>
</b>
<b>
<a>232</a>
<b>314</b>
<v>114</v>
</b>
<b>
<a>314</a>
<b>411</b>
<v>114</v>
</b>
<b>
<a>413</a>
<b>634</b>
<v>114</v>
</b>
<b>
<a>640</a>
<b>1326</b>
<v>114</v>
</b>
<b>
<a>1326</a>
<b>6932</b>
<v>83</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>module</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>288</v>
</b>
<b>
<a>2</a>
<b>7</b>
<v>114</v>
</b>
<b>
<a>7</a>
<b>29</b>
<v>117</v>
</b>
<b>
<a>29</a>
<b>41</b>
<v>119</v>
</b>
<b>
<a>41</a>
<b>49</b>
<v>126</v>
</b>
<b>
<a>49</a>
<b>56</b>
<v>137</v>
</b>
<b>
<a>56</a>
<b>60</b>
<v>110</v>
</b>
<b>
<a>60</a>
<b>64</b>
<v>123</v>
</b>
<b>
<a>64</a>
<b>68</b>
<v>117</v>
</b>
<b>
<a>68</a>
<b>74</b>
<v>127</v>
</b>
<b>
<a>74</a>
<b>91</b>
<v>116</v>
</b>
<b>
<a>91</a>
<b>1405</b>
<v>29</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>module</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>288</v>
</b>
<b>
<a>2</a>
<b>17</b>
<v>117</v>
</b>
<b>
<a>17</a>
<b>43</b>
<v>119</v>
</b>
<b>
<a>44</a>
<b>74</b>
<v>121</v>
</b>
<b>
<a>74</a>
<b>117</b>
<v>114</v>
</b>
<b>
<a>117</a>
<b>173</b>
<v>114</v>
</b>
<b>
<a>173</a>
<b>238</b>
<v>114</v>
</b>
<b>
<a>238</a>
<b>322</b>
<v>114</v>
</b>
<b>
<a>326</a>
<b>421</b>
<v>114</v>
</b>
<b>
<a>421</a>
<b>666</b>
<v>116</v>
</b>
<b>
<a>668</a>
<b>1461</b>
<v>114</v>
</b>
<b>
<a>1472</a>
<b>6948</b>
<v>74</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>module</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>288</v>
</b>
<b>
<a>2</a>
<b>18</b>
<v>116</v>
</b>
<b>
<a>18</a>
<b>45</b>
<v>114</v>
</b>
<b>
<a>45</a>
<b>59</b>
<v>130</v>
</b>
<b>
<a>59</a>
<b>65</b>
<v>131</v>
</b>
<b>
<a>65</a>
<b>69</b>
<v>108</v>
</b>
<b>
<a>69</a>
<b>72</b>
<v>109</v>
</b>
<b>
<a>72</a>
<b>75</b>
<v>114</v>
</b>
<b>
<a>75</a>
<b>79</b>
<v>121</v>
</b>
<b>
<a>79</a>
<b>86</b>
<v>120</v>
</b>
<b>
<a>86</a>
<b>99</b>
<v>120</v>
</b>
<b>
<a>99</a>
<b>1425</b>
<v>51</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>8</b>
<v>783</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>960</v>
</b>
<b>
<a>11</a>
<b>15</b>
<v>1027</v>
</b>
<b>
<a>15</a>
<b>20</b>
<v>1012</v>
</b>
<b>
<a>20</a>
<b>27</b>
<v>1050</v>
</b>
<b>
<a>27</a>
<b>36</b>
<v>995</v>
</b>
<b>
<a>36</a>
<b>49</b>
<v>1003</v>
</b>
<b>
<a>49</a>
<b>66</b>
<v>977</v>
</b>
<b>
<a>66</a>
<b>107</b>
<v>951</v>
</b>
<b>
<a>107</a>
<b>170</b>
<v>949</v>
</b>
<b>
<a>170</a>
<b>297</b>
<v>947</v>
</b>
<b>
<a>297</a>
<b>636</b>
<v>941</v>
</b>
<b>
<a>637</a>
<b>2279</b>
<v>941</v>
</b>
<b>
<a>2283</a>
<b>2351</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>module</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1188</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1761</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>510</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>792</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>792</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>1114</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>726</v>
</b>
<b>
<a>11</a>
<b>14</b>
<v>1084</v>
</b>
<b>
<a>14</a>
<b>25</b>
<v>955</v>
</b>
<b>
<a>25</a>
<b>42</b>
<v>942</v>
</b>
<b>
<a>42</a>
<b>71</b>
<v>976</v>
</b>
<b>
<a>71</a>
<b>177</b>
<v>942</v>
</b>
<b>
<a>177</a>
<b>1104</b>
<v>758</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>6</b>
<v>995</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>486</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>780</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>1091</v>
</b>
<b>
<a>11</a>
<b>13</b>
<v>952</v>
</b>
<b>
<a>13</a>
<b>16</b>
<v>1093</v>
</b>
<b>
<a>16</a>
<b>19</b>
<v>954</v>
</b>
<b>
<a>19</a>
<b>23</b>
<v>1128</v>
</b>
<b>
<a>23</a>
<b>29</b>
<v>954</v>
</b>
<b>
<a>29</a>
<b>38</b>
<v>972</v>
</b>
<b>
<a>38</a>
<b>47</b>
<v>980</v>
</b>
<b>
<a>47</a>
<b>59</b>
<v>976</v>
</b>
<b>
<a>59</a>
<b>75</b>
<v>984</v>
</b>
<b>
<a>75</a>
<b>542</b>
<v>196</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3511</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3490</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1501</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>767</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>1110</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>988</v>
</b>
<b>
<a>10</a>
<b>17</b>
<v>1010</v>
</b>
<b>
<a>17</a>
<b>51</b>
<v>166</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginLine</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>5</b>
<v>672</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>785</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>868</v>
</b>
<b>
<a>9</a>
<b>12</b>
<v>1028</v>
</b>
<b>
<a>12</a>
<b>16</b>
<v>1156</v>
</b>
<b>
<a>16</a>
<b>20</b>
<v>952</v>
</b>
<b>
<a>20</a>
<b>25</b>
<v>1052</v>
</b>
<b>
<a>25</a>
<b>30</b>
<v>983</v>
</b>
<b>
<a>30</a>
<b>40</b>
<v>1003</v>
</b>
<b>
<a>40</a>
<b>52</b>
<v>959</v>
</b>
<b>
<a>52</a>
<b>64</b>
<v>1026</v>
</b>
<b>
<a>64</a>
<b>74</b>
<v>951</v>
</b>
<b>
<a>74</a>
<b>89</b>
<v>965</v>
</b>
<b>
<a>89</a>
<b>546</b>
<v>141</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1542</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>877</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>213</v>
</b>
<b>
<a>5</a>
<b>250154</b>
<v>185</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>module</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2376</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>238</v>
</b>
<b>
<a>3</a>
<b>1104</b>
<v>204</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1542</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>882</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>220</v>
</b>
<b>
<a>6</a>
<b>7984</b>
<v>174</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1542</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>882</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>220</v>
</b>
<b>
<a>6</a>
<b>7972</b>
<v>174</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>beginColumn</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2295</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>304</v>
</b>
<b>
<a>3</a>
<b>114</b>
<v>211</v>
</b>
<b>
<a>120</a>
<b>161</b>
<v>6</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>8</b>
<v>793</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>965</v>
</b>
<b>
<a>11</a>
<b>15</b>
<v>996</v>
</b>
<b>
<a>15</a>
<b>20</b>
<v>1005</v>
</b>
<b>
<a>20</a>
<b>27</b>
<v>1056</v>
</b>
<b>
<a>27</a>
<b>36</b>
<v>1016</v>
</b>
<b>
<a>36</a>
<b>49</b>
<v>981</v>
</b>
<b>
<a>49</a>
<b>65</b>
<v>966</v>
</b>
<b>
<a>65</a>
<b>106</b>
<v>956</v>
</b>
<b>
<a>106</a>
<b>169</b>
<v>951</v>
</b>
<b>
<a>169</a>
<b>295</b>
<v>947</v>
</b>
<b>
<a>295</a>
<b>626</b>
<v>941</v>
</b>
<b>
<a>627</a>
<b>2214</b>
<v>941</v>
</b>
<b>
<a>2217</a>
<b>2349</b>
<v>19</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>module</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1210</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1754</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>526</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>797</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>760</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>1109</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>732</v>
</b>
<b>
<a>11</a>
<b>14</b>
<v>1078</v>
</b>
<b>
<a>14</a>
<b>25</b>
<v>947</v>
</b>
<b>
<a>25</a>
<b>42</b>
<v>956</v>
</b>
<b>
<a>42</a>
<b>70</b>
<v>942</v>
</b>
<b>
<a>70</a>
<b>170</b>
<v>941</v>
</b>
<b>
<a>170</a>
<b>1104</b>
<v>782</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4048</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3046</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1345</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>851</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>1021</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>1010</v>
</b>
<b>
<a>10</a>
<b>17</b>
<v>1010</v>
</b>
<b>
<a>17</a>
<b>34</b>
<v>203</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>6</b>
<v>999</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>1140</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>1056</v>
</b>
<b>
<a>11</a>
<b>13</b>
<v>933</v>
</b>
<b>
<a>13</a>
<b>16</b>
<v>1154</v>
</b>
<b>
<a>16</a>
<b>19</b>
<v>992</v>
</b>
<b>
<a>19</a>
<b>23</b>
<v>1129</v>
</b>
<b>
<a>23</a>
<b>29</b>
<v>999</v>
</b>
<b>
<a>29</a>
<b>38</b>
<v>981</v>
</b>
<b>
<a>38</a>
<b>47</b>
<v>983</v>
</b>
<b>
<a>47</a>
<b>59</b>
<v>985</v>
</b>
<b>
<a>59</a>
<b>75</b>
<v>988</v>
</b>
<b>
<a>75</a>
<b>542</b>
<v>192</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endLine</src>
<trg>endColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>6</b>
<v>1045</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>1010</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>1073</v>
</b>
<b>
<a>11</a>
<b>14</b>
<v>933</v>
</b>
<b>
<a>14</a>
<b>18</b>
<v>1055</v>
</b>
<b>
<a>18</a>
<b>23</b>
<v>1084</v>
</b>
<b>
<a>23</a>
<b>28</b>
<v>1020</v>
</b>
<b>
<a>28</a>
<b>36</b>
<v>984</v>
</b>
<b>
<a>36</a>
<b>48</b>
<v>999</v>
</b>
<b>
<a>48</a>
<b>60</b>
<v>991</v>
</b>
<b>
<a>60</a>
<b>70</b>
<v>959</v>
</b>
<b>
<a>70</a>
<b>84</b>
<v>963</v>
</b>
<b>
<a>84</a>
<b>547</b>
<v>418</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1505</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>972</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>227</v>
</b>
<b>
<a>5</a>
<b>41083</b>
<v>221</v>
</b>
<b>
<a>42453</a>
<b>55223</b>
<v>13</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>module</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2435</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>264</v>
</b>
<b>
<a>3</a>
<b>782</b>
<v>221</v>
</b>
<b>
<a>782</a>
<b>1104</b>
<v>18</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>beginLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1606</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>902</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>228</v>
</b>
<b>
<a>6</a>
<b>6777</b>
<v>202</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>beginColumn</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2250</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>408</v>
</b>
<b>
<a>3</a>
<b>56</b>
<v>221</v>
</b>
<b>
<a>56</a>
<b>79</b>
<v>59</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>endColumn</src>
<trg>endLine</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1606</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>902</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>228</v>
</b>
<b>
<a>6</a>
<b>6726</b>
<v>202</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_module_path</name>
<cardinality>3066</cardinality>
<columnsizes>
<e>
<k>module</k>
<v>3066</v>
</e>
<e>
<k>file</k>
<v>3066</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>module</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3066</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>module</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3066</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>file_contents</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>file</k>
<v>3066</v>
</e>
<e>
<k>contents</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>file</src>
<trg>contents</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>100</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>contents</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>100</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>variable</name>
<cardinality>242770</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>242770</v>
</e>
<e>
<k>scope</k>
<v>50174</v>
</e>
<e>
<k>name</k>
<v>54891</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>242770</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>242770</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10764</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>14394</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>7657</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>4580</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>2991</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>4606</v>
</b>
<b>
<a>9</a>
<b>22</b>
<v>3819</v>
</b>
<b>
<a>22</a>
<b>233</b>
<v>1360</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10764</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>14394</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>7657</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>4580</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>2991</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>4606</v>
</b>
<b>
<a>9</a>
<b>22</b>
<v>3819</v>
</b>
<b>
<a>22</a>
<b>233</b>
<v>1360</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>36525</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8506</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>4396</v>
</b>
<b>
<a>5</a>
<b>20</b>
<v>4134</v>
</b>
<b>
<a>20</a>
<b>10542</b>
<v>1327</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>36525</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8506</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>4396</v>
</b>
<b>
<a>5</a>
<b>20</b>
<v>4134</v>
</b>
<b>
<a>20</a>
<b>10542</b>
<v>1327</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_line_lengths</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>file</k>
<v>100</v>
</e>
<e>
<k>line</k>
<v>100</v>
</e>
<e>
<k>length</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>line</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>length</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>line</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>file</src>
<trg>length</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>line</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>line</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>line</src>
<trg>length</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>length</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>length</src>
<trg>file</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>length</src>
<trg>line</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_Classes</name>
<cardinality>10244</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>10244</v>
</e>
<e>
<k>parent</k>
<v>10244</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10244</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10244</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_Functions</name>
<cardinality>44860</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>44860</v>
</e>
<e>
<k>parent</k>
<v>44860</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>44860</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>44860</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_Modules</name>
<cardinality>5983</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>5983</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>py_patterns</name>
<cardinality>1000</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1000</v>
</e>
<e>
<k>kind</k>
<v>13</v>
</e>
<e>
<k>parent</k>
<v>1000</v>
</e>
<e>
<k>idx</k>
<v>100</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>py_type_parameters</name>
<cardinality>1000</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1000</v>
</e>
<e>
<k>kind</k>
<v>3</v>
</e>
<e>
<k>parent</k>
<v>1000</v>
</e>
<e>
<k>idx</k>
<v>100</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>py_type_parameter_lists</name>
<cardinality>1000</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1000</v>
</e>
<e>
<k>parent</k>
<v>1000</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>py_pattern_lists</name>
<cardinality>1000</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1000</v>
</e>
<e>
<k>parent</k>
<v>1000</v>
</e>
<e>
<k>idx</k>
<v>100</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>py_extracted_version</name>
<cardinality>3337</cardinality>
<columnsizes>
<e>
<k>module</k>
<v>3337</v>
</e>
<e>
<k>version</k>
<v>1</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>module</src>
<trg>version</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3337</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>version</src>
<trg>module</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3337</a>
<b>3338</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_StringParts</name>
<cardinality>6399</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>6399</v>
</e>
<e>
<k>parent</k>
<v>2296</v>
</e>
<e>
<k>idx</k>
<v>62</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6399</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>6399</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1598</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>380</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>142</v>
</b>
<b>
<a>5</a>
<b>63</b>
<v>176</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1598</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>380</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>142</v>
</b>
<b>
<a>5</a>
<b>63</b>
<v>176</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>4</a>
<b>5</b>
<v>17</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>23</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>5</v>
</b>
<b>
<a>9</a>
<b>14</b>
<v>5</v>
</b>
<b>
<a>16</a>
<b>59</b>
<v>5</v>
</b>
<b>
<a>72</a>
<b>699</b>
<v>5</v>
</b>
<b>
<a>2296</a>
<b>2297</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>4</a>
<b>5</b>
<v>17</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>23</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>5</v>
</b>
<b>
<a>9</a>
<b>14</b>
<v>5</v>
</b>
<b>
<a>16</a>
<b>59</b>
<v>5</v>
</b>
<b>
<a>72</a>
<b>699</b>
<v>5</v>
</b>
<b>
<a>2296</a>
<b>2297</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_StringPart_lists</name>
<cardinality>2296</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>2296</v>
</e>
<e>
<k>parent</k>
<v>2296</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2296</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2296</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_aliases</name>
<cardinality>21374</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>21374</v>
</e>
<e>
<k>parent</k>
<v>14396</v>
</e>
<e>
<k>idx</k>
<v>110</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21374</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21374</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11488</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1597</v>
</b>
<b>
<a>3</a>
<b>7</b>
<v>1116</v>
</b>
<b>
<a>7</a>
<b>111</b>
<v>195</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11488</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1597</v>
</b>
<b>
<a>3</a>
<b>7</b>
<v>1116</v>
</b>
<b>
<a>7</a>
<b>111</b>
<v>195</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>30</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>4</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>9</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>10</v>
</b>
<b>
<a>9</a>
<b>15</b>
<v>8</v>
</b>
<b>
<a>18</a>
<b>32</b>
<v>9</v>
</b>
<b>
<a>36</a>
<b>113</b>
<v>9</v>
</b>
<b>
<a>142</a>
<b>14397</b>
<v>8</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>30</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>4</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>9</v>
</b>
<b>
<a>6</a>
<b>9</b>
<v>10</v>
</b>
<b>
<a>9</a>
<b>15</b>
<v>8</v>
</b>
<b>
<a>18</a>
<b>32</b>
<v>9</v>
</b>
<b>
<a>36</a>
<b>113</b>
<v>9</v>
</b>
<b>
<a>142</a>
<b>14397</b>
<v>8</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_alias_lists</name>
<cardinality>14396</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>14396</v>
</e>
<e>
<k>parent</k>
<v>14396</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>14396</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>14396</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_arguments</name>
<cardinality>41982</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>41982</v>
</e>
<e>
<k>parent</k>
<v>41982</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>41982</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>41982</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_bools</name>
<cardinality>26986</cardinality>
<columnsizes>
<e>
<k>parent</k>
<v>26986</v>
</e>
<e>
<k>idx</k>
<v>3</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26986</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>964</a>
<b>965</b>
<v>1</v>
</b>
<b>
<a>3487</a>
<b>3488</b>
<v>1</v>
</b>
<b>
<a>22535</a>
<b>22536</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_boolops</name>
<cardinality>10907</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>10907</v>
</e>
<e>
<k>kind</k>
<v>2</v>
</e>
<e>
<k>parent</k>
<v>10907</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10907</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10907</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2646</a>
<b>2647</b>
<v>1</v>
</b>
<b>
<a>5231</a>
<b>5232</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2646</a>
<b>2647</b>
<v>1</v>
</b>
<b>
<a>5231</a>
<b>5232</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10907</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>10907</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_bytes</name>
<cardinality>211200</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>48658</v>
</e>
<e>
<k>parent</k>
<v>105600</v>
</e>
<e>
<k>idx</k>
<v>2</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>37453</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>6003</v>
</b>
<b>
<a>3</a>
<b>8</b>
<v>3791</v>
</b>
<b>
<a>8</a>
<b>71667</b>
<v>1411</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>48644</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>14</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>14</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>105586</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>105600</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>14</a>
<b>15</b>
<v>1</v>
</b>
<b>
<a>48658</a>
<b>48659</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>105600</a>
<b>105601</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_cmpops</name>
<cardinality>38007</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>38007</v>
</e>
<e>
<k>kind</k>
<v>29</v>
</e>
<e>
<k>parent</k>
<v>37666</v>
</e>
<e>
<k>idx</k>
<v>8</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>38007</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>38007</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>38007</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>380</a>
<b>381</b>
<v>2</v>
</b>
<b>
<a>440</a>
<b>441</b>
<v>2</v>
</b>
<b>
<a>563</a>
<b>564</b>
<v>2</v>
</b>
<b>
<a>615</a>
<b>616</b>
<v>2</v>
</b>
<b>
<a>673</a>
<b>674</b>
<v>2</v>
</b>
<b>
<a>1027</a>
<b>1028</b>
<v>2</v>
</b>
<b>
<a>1529</a>
<b>1530</b>
<v>2</v>
</b>
<b>
<a>1597</a>
<b>1598</b>
<v>2</v>
</b>
<b>
<a>2144</a>
<b>2145</b>
<v>2</v>
</b>
<b>
<a>3828</a>
<b>3829</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>317</a>
<b>318</b>
<v>2</v>
</b>
<b>
<a>439</a>
<b>440</b>
<v>2</v>
</b>
<b>
<a>563</a>
<b>564</b>
<v>2</v>
</b>
<b>
<a>612</a>
<b>613</b>
<v>2</v>
</b>
<b>
<a>669</a>
<b>670</b>
<v>2</v>
</b>
<b>
<a>1027</a>
<b>1028</b>
<v>2</v>
</b>
<b>
<a>1529</a>
<b>1530</b>
<v>2</v>
</b>
<b>
<a>1597</a>
<b>1598</b>
<v>2</v>
</b>
<b>
<a>2144</a>
<b>2145</b>
<v>2</v>
</b>
<b>
<a>3819</a>
<b>3820</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>14</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>37330</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>335</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>37562</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>103</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>37330</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>335</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>113</a>
<b>114</b>
<v>2</v>
</b>
<b>
<a>12681</a>
<b>12682</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>2</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>113</a>
<b>114</b>
<v>2</v>
</b>
<b>
<a>12681</a>
<b>12682</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_cmpop_lists</name>
<cardinality>37666</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>37666</v>
</e>
<e>
<k>parent</k>
<v>37666</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>37666</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>37666</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_comprehensions</name>
<cardinality>1688</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1688</v>
</e>
<e>
<k>parent</k>
<v>1682</v>
</e>
<e>
<k>idx</k>
<v>2</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1688</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1688</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1676</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>6</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1676</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>6</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>1682</a>
<b>1683</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>6</a>
<b>7</b>
<v>1</v>
</b>
<b>
<a>1682</a>
<b>1683</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_comprehension_lists</name>
<cardinality>1682</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1682</v>
</e>
<e>
<k>parent</k>
<v>1682</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1682</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1682</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_dict_items</name>
<cardinality>167901</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>167901</v>
</e>
<e>
<k>kind</k>
<v>4</v>
</e>
<e>
<k>parent</k>
<v>19804</v>
</e>
<e>
<k>idx</k>
<v>7730</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>167901</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>167901</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>167901</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>326</a>
<b>327</b>
<v>1</v>
</b>
<b>
<a>53883</a>
<b>53884</b>
<v>1</v>
</b>
<b>
<a>67045</a>
<b>67046</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>326</a>
<b>327</b>
<v>1</v>
</b>
<b>
<a>1881</a>
<b>1882</b>
<v>1</v>
</b>
<b>
<a>12123</a>
<b>12124</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>7</a>
<b>8</b>
<v>1</v>
</b>
<b>
<a>18</a>
<b>19</b>
<v>1</v>
</b>
<b>
<a>5583</a>
<b>5584</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5811</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1851</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>1700</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>8083</v>
</b>
<b>
<a>7</a>
<b>12</b>
<v>1826</v>
</b>
<b>
<a>12</a>
<b>5584</b>
<v>530</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>19765</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>38</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>5811</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1851</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>1700</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>8083</v>
</b>
<b>
<a>7</a>
<b>12</b>
<v>1826</v>
</b>
<b>
<a>12</a>
<b>5584</b>
<v>530</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1654</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1982</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>811</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>192</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>753</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>962</v>
</b>
<b>
<a>8</a>
<b>20</b>
<v>610</v>
</b>
<b>
<a>20</a>
<b>69</b>
<v>584</v>
</b>
<b>
<a>69</a>
<b>14303</b>
<v>178</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7705</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>24</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1654</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1982</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>811</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>192</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>753</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>962</v>
</b>
<b>
<a>8</a>
<b>20</b>
<v>610</v>
</b>
<b>
<a>20</a>
<b>69</b>
<v>584</v>
</b>
<b>
<a>69</a>
<b>14303</b>
<v>178</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_dict_item_lists</name>
<cardinality>33758</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>33758</v>
</e>
<e>
<k>parent</k>
<v>33758</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33758</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33758</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_exprs</name>
<cardinality>1684031</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1684031</v>
</e>
<e>
<k>kind</k>
<v>89</v>
</e>
<e>
<k>parent</k>
<v>1380134</v>
</e>
<e>
<k>idx</k>
<v>597</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1684031</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1684031</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1684031</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>15</a>
<b>28</b>
<v>5</v>
</b>
<b>
<a>39</a>
<b>89</b>
<v>5</v>
</b>
<b>
<a>134</a>
<b>189</b>
<v>5</v>
</b>
<b>
<a>281</a>
<b>360</b>
<v>5</v>
</b>
<b>
<a>426</a>
<b>570</b>
<v>5</v>
</b>
<b>
<a>1056</a>
<b>1205</b>
<v>5</v>
</b>
<b>
<a>1327</a>
<b>1791</b>
<v>5</v>
</b>
<b>
<a>1942</a>
<b>3179</b>
<v>5</v>
</b>
<b>
<a>3398</a>
<b>4019</b>
<v>5</v>
</b>
<b>
<a>4476</a>
<b>4980</b>
<v>5</v>
</b>
<b>
<a>8519</a>
<b>9720</b>
<v>5</v>
</b>
<b>
<a>10633</a>
<b>12682</b>
<v>5</v>
</b>
<b>
<a>13945</a>
<b>16376</b>
<v>5</v>
</b>
<b>
<a>46173</a>
<b>58988</b>
<v>5</v>
</b>
<b>
<a>75624</a>
<b>284809</b>
<v>5</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>15</a>
<b>28</b>
<v>5</v>
</b>
<b>
<a>39</a>
<b>87</b>
<v>5</v>
</b>
<b>
<a>134</a>
<b>175</b>
<v>5</v>
</b>
<b>
<a>271</a>
<b>359</b>
<v>5</v>
</b>
<b>
<a>426</a>
<b>560</b>
<v>5</v>
</b>
<b>
<a>1036</a>
<b>1119</b>
<v>5</v>
</b>
<b>
<a>1327</a>
<b>1791</b>
<v>5</v>
</b>
<b>
<a>1942</a>
<b>3179</b>
<v>5</v>
</b>
<b>
<a>3357</a>
<b>3716</b>
<v>5</v>
</b>
<b>
<a>4285</a>
<b>4980</b>
<v>5</v>
</b>
<b>
<a>8177</a>
<b>9473</b>
<v>5</v>
</b>
<b>
<a>10060</a>
<b>11624</b>
<v>5</v>
</b>
<b>
<a>13945</a>
<b>15094</b>
<v>5</v>
</b>
<b>
<a>35526</a>
<b>57772</b>
<v>5</v>
</b>
<b>
<a>72662</a>
<b>245283</b>
<v>5</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>17</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>5</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>11</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>5</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>5</v>
</b>
<b>
<a>12</a>
<b>13</b>
<v>5</v>
</b>
<b>
<a>15</a>
<b>18</b>
<v>5</v>
</b>
<b>
<a>23</a>
<b>27</b>
<v>5</v>
</b>
<b>
<a>37</a>
<b>127</b>
<v>5</v>
</b>
<b>
<a>201</a>
<b>202</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1147073</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>197316</v>
</b>
<b>
<a>3</a>
<b>202</b>
<v>35744</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1255206</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>120198</v>
</b>
<b>
<a>3</a>
<b>11</b>
<v>4728</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1147073</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>197316</v>
</b>
<b>
<a>3</a>
<b>202</b>
<v>35744</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>23</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>199</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>148</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>35</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>50</v>
</b>
<b>
<a>9</a>
<b>26</b>
<v>47</v>
</b>
<b>
<a>26</a>
<b>102</b>
<v>47</v>
</b>
<b>
<a>113</a>
<b>197687</b>
<v>44</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>222</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>258</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>8</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>47</v>
</b>
<b>
<a>5</a>
<b>21</b>
<v>47</v>
</b>
<b>
<a>22</a>
<b>29</b>
<v>11</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>23</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>199</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>148</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>35</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>50</v>
</b>
<b>
<a>9</a>
<b>26</b>
<v>47</v>
</b>
<b>
<a>26</a>
<b>102</b>
<v>47</v>
</b>
<b>
<a>113</a>
<b>197687</b>
<v>44</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_expr_contexts</name>
<cardinality>1140675</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1140675</v>
</e>
<e>
<k>kind</k>
<v>11</v>
</e>
<e>
<k>parent</k>
<v>1140675</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1140675</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1140675</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>446</a>
<b>447</b>
<v>2</v>
</b>
<b>
<a>29477</a>
<b>29478</b>
<v>2</v>
</b>
<b>
<a>66896</a>
<b>66897</b>
<v>2</v>
</b>
<b>
<a>287209</a>
<b>287210</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>446</a>
<b>447</b>
<v>2</v>
</b>
<b>
<a>29477</a>
<b>29478</b>
<v>2</v>
</b>
<b>
<a>66896</a>
<b>66897</b>
<v>2</v>
</b>
<b>
<a>287209</a>
<b>287210</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1140675</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1140675</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_expr_lists</name>
<cardinality>430986</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>430986</v>
</e>
<e>
<k>parent</k>
<v>423623</v>
</e>
<e>
<k>idx</k>
<v>17</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>430986</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>430986</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>416966</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>6656</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>416966</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>6656</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>175</a>
<b>176</b>
<v>5</v>
</b>
<b>
<a>2522</a>
<b>2523</b>
<v>2</v>
</b>
<b>
<a>12681</a>
<b>12682</b>
<v>2</v>
</b>
<b>
<a>54095</a>
<b>54096</b>
<v>2</v>
</b>
<b>
<a>75451</a>
<b>75452</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>175</a>
<b>176</b>
<v>5</v>
</b>
<b>
<a>2522</a>
<b>2523</b>
<v>2</v>
</b>
<b>
<a>12681</a>
<b>12682</b>
<v>2</v>
</b>
<b>
<a>54095</a>
<b>54096</b>
<v>2</v>
</b>
<b>
<a>75451</a>
<b>75452</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_ints</name>
<cardinality>21532</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>4</v>
</e>
<e>
<k>parent</k>
<v>21532</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>207</a>
<b>208</b>
<v>1</v>
</b>
<b>
<a>2770</a>
<b>2771</b>
<v>1</v>
</b>
<b>
<a>18553</a>
<b>18554</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>21532</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_locations</name>
<cardinality>2184728</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>2184728</v>
</e>
<e>
<k>parent</k>
<v>2184728</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2184728</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2184728</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_numbers</name>
<cardinality>117446</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>4249</v>
</e>
<e>
<k>parent</k>
<v>58723</v>
</e>
<e>
<k>idx</k>
<v>2</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2830</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>632</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>291</v>
</b>
<b>
<a>4</a>
<b>11</b>
<v>320</v>
</b>
<b>
<a>11</a>
<b>15704</b>
<v>176</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1355</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2894</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>57251</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1472</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>58723</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3302</a>
<b>3303</b>
<v>1</v>
</b>
<b>
<a>3841</a>
<b>3842</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>58723</a>
<b>58724</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_operators</name>
<cardinality>28868</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>28868</v>
</e>
<e>
<k>kind</k>
<v>35</v>
</e>
<e>
<k>parent</k>
<v>28868</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>28868</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>28868</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>53</a>
<b>54</b>
<v>2</v>
</b>
<b>
<a>64</a>
<b>65</b>
<v>2</v>
</b>
<b>
<a>94</a>
<b>95</b>
<v>2</v>
</b>
<b>
<a>121</a>
<b>122</b>
<v>2</v>
</b>
<b>
<a>122</a>
<b>123</b>
<v>2</v>
</b>
<b>
<a>169</a>
<b>170</b>
<v>2</v>
</b>
<b>
<a>268</a>
<b>269</b>
<v>2</v>
</b>
<b>
<a>269</a>
<b>270</b>
<v>2</v>
</b>
<b>
<a>747</a>
<b>748</b>
<v>2</v>
</b>
<b>
<a>1056</a>
<b>1057</b>
<v>2</v>
</b>
<b>
<a>2176</a>
<b>2177</b>
<v>2</v>
</b>
<b>
<a>4580</a>
<b>4581</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>53</a>
<b>54</b>
<v>2</v>
</b>
<b>
<a>64</a>
<b>65</b>
<v>2</v>
</b>
<b>
<a>94</a>
<b>95</b>
<v>2</v>
</b>
<b>
<a>121</a>
<b>122</b>
<v>2</v>
</b>
<b>
<a>122</a>
<b>123</b>
<v>2</v>
</b>
<b>
<a>169</a>
<b>170</b>
<v>2</v>
</b>
<b>
<a>268</a>
<b>269</b>
<v>2</v>
</b>
<b>
<a>269</a>
<b>270</b>
<v>2</v>
</b>
<b>
<a>747</a>
<b>748</b>
<v>2</v>
</b>
<b>
<a>1056</a>
<b>1057</b>
<v>2</v>
</b>
<b>
<a>2176</a>
<b>2177</b>
<v>2</v>
</b>
<b>
<a>4580</a>
<b>4581</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>28868</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>28868</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_parameter_lists</name>
<cardinality>43271</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>43271</v>
</e>
<e>
<k>parent</k>
<v>43271</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>43271</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>43271</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_stmts</name>
<cardinality>372643</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>372643</v>
</e>
<e>
<k>kind</k>
<v>59</v>
</e>
<e>
<k>parent</k>
<v>156700</v>
</e>
<e>
<k>idx</k>
<v>888</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>372643</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>372643</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>372643</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>12</a>
<b>13</b>
<v>2</v>
</b>
<b>
<a>47</a>
<b>48</b>
<v>2</v>
</b>
<b>
<a>132</a>
<b>133</b>
<v>2</v>
</b>
<b>
<a>387</a>
<b>388</b>
<v>2</v>
</b>
<b>
<a>404</a>
<b>405</b>
<v>2</v>
</b>
<b>
<a>559</a>
<b>560</b>
<v>2</v>
</b>
<b>
<a>572</a>
<b>573</b>
<v>2</v>
</b>
<b>
<a>673</a>
<b>674</b>
<v>2</v>
</b>
<b>
<a>720</a>
<b>721</b>
<v>2</v>
</b>
<b>
<a>967</a>
<b>968</b>
<v>2</v>
</b>
<b>
<a>1231</a>
<b>1232</b>
<v>2</v>
</b>
<b>
<a>1889</a>
<b>1890</b>
<v>2</v>
</b>
<b>
<a>2091</a>
<b>2092</b>
<v>2</v>
</b>
<b>
<a>2624</a>
<b>2625</b>
<v>2</v>
</b>
<b>
<a>3001</a>
<b>3002</b>
<v>2</v>
</b>
<b>
<a>3870</a>
<b>3871</b>
<v>2</v>
</b>
<b>
<a>12163</a>
<b>12164</b>
<v>2</v>
</b>
<b>
<a>18052</a>
<b>18053</b>
<v>2</v>
</b>
<b>
<a>25032</a>
<b>25033</b>
<v>2</v>
</b>
<b>
<a>51031</a>
<b>51032</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>12</a>
<b>13</b>
<v>2</v>
</b>
<b>
<a>37</a>
<b>38</b>
<v>2</v>
</b>
<b>
<a>123</a>
<b>124</b>
<v>2</v>
</b>
<b>
<a>356</a>
<b>357</b>
<v>2</v>
</b>
<b>
<a>404</a>
<b>405</b>
<v>2</v>
</b>
<b>
<a>471</a>
<b>472</b>
<v>2</v>
</b>
<b>
<a>557</a>
<b>558</b>
<v>2</v>
</b>
<b>
<a>572</a>
<b>573</b>
<v>2</v>
</b>
<b>
<a>677</a>
<b>678</b>
<v>2</v>
</b>
<b>
<a>967</a>
<b>968</b>
<v>2</v>
</b>
<b>
<a>984</a>
<b>985</b>
<v>2</v>
</b>
<b>
<a>1094</a>
<b>1095</b>
<v>2</v>
</b>
<b>
<a>1777</a>
<b>1778</b>
<v>2</v>
</b>
<b>
<a>1895</a>
<b>1896</b>
<v>2</v>
</b>
<b>
<a>2624</a>
<b>2625</b>
<v>2</v>
</b>
<b>
<a>3544</a>
<b>3545</b>
<v>2</v>
</b>
<b>
<a>12163</a>
<b>12164</b>
<v>2</v>
</b>
<b>
<a>12758</a>
<b>12759</b>
<v>2</v>
</b>
<b>
<a>18445</a>
<b>18446</b>
<v>2</v>
</b>
<b>
<a>20426</a>
<b>20427</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>5</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>5</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2</v>
</b>
<b>
<a>13</a>
<b>14</b>
<v>2</v>
</b>
<b>
<a>15</a>
<b>16</b>
<v>2</v>
</b>
<b>
<a>18</a>
<b>19</b>
<v>5</v>
</b>
<b>
<a>21</a>
<b>22</b>
<v>2</v>
</b>
<b>
<a>27</a>
<b>28</b>
<v>2</v>
</b>
<b>
<a>33</a>
<b>34</b>
<v>2</v>
</b>
<b>
<a>37</a>
<b>38</b>
<v>2</v>
</b>
<b>
<a>38</a>
<b>39</b>
<v>2</v>
</b>
<b>
<a>42</a>
<b>43</b>
<v>2</v>
</b>
<b>
<a>51</a>
<b>52</b>
<v>2</v>
</b>
<b>
<a>84</a>
<b>85</b>
<v>2</v>
</b>
<b>
<a>187</a>
<b>188</b>
<v>2</v>
</b>
<b>
<a>293</a>
<b>294</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>96284</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>25704</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>11789</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>14376</v>
</b>
<b>
<a>7</a>
<b>300</b>
<v>8545</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>106000</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>31003</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>12071</v>
</b>
<b>
<a>4</a>
<b>9</b>
<v>7624</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>96284</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>25704</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>11789</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>14376</v>
</b>
<b>
<a>7</a>
<b>300</b>
<v>8545</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>335</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>59</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>83</v>
</b>
<b>
<a>6</a>
<b>14</b>
<v>74</v>
</b>
<b>
<a>14</a>
<b>25</b>
<v>68</v>
</b>
<b>
<a>25</a>
<b>53</b>
<v>68</v>
</b>
<b>
<a>53</a>
<b>103</b>
<v>68</v>
</b>
<b>
<a>107</a>
<b>335</b>
<v>68</v>
</b>
<b>
<a>369</a>
<b>52757</b>
<v>62</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>344</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>267</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>83</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>62</v>
</b>
<b>
<a>5</a>
<b>10</b>
<v>71</v>
</b>
<b>
<a>10</a>
<b>21</b>
<v>59</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>335</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>59</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>83</v>
</b>
<b>
<a>6</a>
<b>14</b>
<v>74</v>
</b>
<b>
<a>14</a>
<b>25</b>
<v>68</v>
</b>
<b>
<a>25</a>
<b>53</b>
<v>68</v>
</b>
<b>
<a>53</a>
<b>103</b>
<v>68</v>
</b>
<b>
<a>107</a>
<b>335</b>
<v>68</v>
</b>
<b>
<a>369</a>
<b>52757</b>
<v>62</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_stmt_lists</name>
<cardinality>156700</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>156700</v>
</e>
<e>
<k>parent</k>
<v>132647</v>
</e>
<e>
<k>idx</k>
<v>14</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>156700</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>156700</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>109538</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>22179</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>929</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>109538</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>22179</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>929</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>460</a>
<b>461</b>
<v>2</v>
</b>
<b>
<a>4033</a>
<b>4034</b>
<v>2</v>
</b>
<b>
<a>13686</a>
<b>13687</b>
<v>2</v>
</b>
<b>
<a>15103</a>
<b>15104</b>
<v>2</v>
</b>
<b>
<a>19474</a>
<b>19475</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>460</a>
<b>461</b>
<v>2</v>
</b>
<b>
<a>4033</a>
<b>4034</b>
<v>2</v>
</b>
<b>
<a>13686</a>
<b>13687</b>
<v>2</v>
</b>
<b>
<a>15103</a>
<b>15104</b>
<v>2</v>
</b>
<b>
<a>19474</a>
<b>19475</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_strs</name>
<cardinality>985327</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>140335</v>
</e>
<e>
<k>parent</k>
<v>695288</v>
</e>
<e>
<k>idx</k>
<v>5</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>79968</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>31802</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>9602</v>
</b>
<b>
<a>4</a>
<b>8</b>
<v>11026</v>
</b>
<b>
<a>8</a>
<b>143732</b>
<v>7935</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>106110</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>22027</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>12190</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>6</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>405951</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>289317</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>19</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>405275</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>289993</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>19</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>34</a>
<b>35</b>
<v>1</v>
</b>
<b>
<a>17059</a>
<b>17060</b>
<v>1</v>
</b>
<b>
<a>25371</a>
<b>25372</b>
<v>1</v>
</b>
<b>
<a>92414</a>
<b>92415</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>42</a>
<b>43</b>
<v>1</v>
</b>
<b>
<a>37559</a>
<b>37560</b>
<v>1</v>
</b>
<b>
<a>294366</a>
<b>294367</b>
<v>1</v>
</b>
<b>
<a>379612</a>
<b>379613</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_str_lists</name>
<cardinality>427</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>427</v>
</e>
<e>
<k>parent</k>
<v>427</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>427</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>427</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_unaryops</name>
<cardinality>13295</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>13295</v>
</e>
<e>
<k>kind</k>
<v>11</v>
</e>
<e>
<k>parent</k>
<v>13295</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>13295</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>13295</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>5</a>
<b>6</b>
<v>2</v>
</b>
<b>
<a>20</a>
<b>21</b>
<v>2</v>
</b>
<b>
<a>1537</a>
<b>1538</b>
<v>2</v>
</b>
<b>
<a>2914</a>
<b>2915</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>5</a>
<b>6</b>
<v>2</v>
</b>
<b>
<a>20</a>
<b>21</b>
<v>2</v>
</b>
<b>
<a>1537</a>
<b>1538</b>
<v>2</v>
</b>
<b>
<a>2914</a>
<b>2915</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>13295</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>13295</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_variables</name>
<cardinality>845963</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>242770</v>
</e>
<e>
<k>parent</k>
<v>845963</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>61149</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>77254</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>38584</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>21392</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>20913</v>
</b>
<b>
<a>7</a>
<b>15</b>
<v>18418</v>
</b>
<b>
<a>15</a>
<b>318</b>
<v>5058</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>845963</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_absolute_names</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>module</k>
<v>100</v>
</e>
<e>
<k>relname</k>
<v>100</v>
</e>
<e>
<k>absname</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>module</src>
<trg>relname</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>module</src>
<trg>absname</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>relname</src>
<trg>module</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>relname</src>
<trg>absname</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>absname</src>
<trg>module</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>absname</src>
<trg>relname</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_exports</name>
<cardinality>19755</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1138</v>
</e>
<e>
<k>name</k>
<v>16813</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>141</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>164</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>109</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>112</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>103</v>
</b>
<b>
<a>7</a>
<b>10</b>
<v>91</v>
</b>
<b>
<a>10</a>
<b>14</b>
<v>88</v>
</b>
<b>
<a>14</a>
<b>20</b>
<v>90</v>
</b>
<b>
<a>20</a>
<b>33</b>
<v>94</v>
</b>
<b>
<a>33</a>
<b>53</b>
<v>90</v>
</b>
<b>
<a>53</a>
<b>2260</b>
<v>52</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>16070</v>
</b>
<b>
<a>2</a>
<b>143</b>
<v>742</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_successors</name>
<cardinality>2366367</cardinality>
<columnsizes>
<e>
<k>predecessor</k>
<v>2270167</v>
</e>
<e>
<k>successor</k>
<v>2275369</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>predecessor</src>
<trg>successor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2177926</v>
</b>
<b>
<a>2</a>
<b>9</b>
<v>92240</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>successor</src>
<trg>predecessor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2225590</v>
</b>
<b>
<a>2</a>
<b>173</b>
<v>49778</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_true_successors</name>
<cardinality>70315</cardinality>
<columnsizes>
<e>
<k>predecessor</k>
<v>70315</v>
</e>
<e>
<k>successor</k>
<v>67897</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>predecessor</src>
<trg>successor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>70315</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>successor</src>
<trg>predecessor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>65747</v>
</b>
<b>
<a>2</a>
<b>7</b>
<v>2150</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_exception_successors</name>
<cardinality>43951</cardinality>
<columnsizes>
<e>
<k>predecessor</k>
<v>39261</v>
</e>
<e>
<k>successor</k>
<v>6911</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>predecessor</src>
<trg>successor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>35379</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>3448</v>
</b>
<b>
<a>3</a>
<b>7</b>
<v>433</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>successor</src>
<trg>predecessor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1045</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1497</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1271</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>760</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>463</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>519</v>
</b>
<b>
<a>8</a>
<b>12</b>
<v>525</v>
</b>
<b>
<a>12</a>
<b>27</b>
<v>534</v>
</b>
<b>
<a>27</a>
<b>173</b>
<v>294</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_false_successors</name>
<cardinality>69439</cardinality>
<columnsizes>
<e>
<k>predecessor</k>
<v>69439</v>
</e>
<e>
<k>successor</k>
<v>59260</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>predecessor</src>
<trg>successor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>69439</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>successor</src>
<trg>predecessor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>51296</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>6510</v>
</b>
<b>
<a>3</a>
<b>13</b>
<v>1452</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_flow_bb_node</name>
<cardinality>2323431</cardinality>
<columnsizes>
<e>
<k>flownode</k>
<v>2323431</v>
</e>
<e>
<k>realnode</k>
<v>2208164</v>
</e>
<e>
<k>basicblock</k>
<v>215280</v>
</e>
<e>
<k>index</k>
<v>23948</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>flownode</src>
<trg>realnode</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2323431</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>flownode</src>
<trg>basicblock</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2323431</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>flownode</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2323431</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>realnode</src>
<trg>flownode</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2102771</v>
</b>
<b>
<a>2</a>
<b>9</b>
<v>105392</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>realnode</src>
<trg>basicblock</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2135213</v>
</b>
<b>
<a>2</a>
<b>7</b>
<v>72950</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>realnode</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2155174</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>52989</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>basicblock</src>
<trg>flownode</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>37515</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>17987</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>19072</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>17365</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>17931</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>13664</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>10900</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>16975</v>
</b>
<b>
<a>10</a>
<b>13</b>
<v>17232</v>
</b>
<b>
<a>13</a>
<b>19</b>
<v>17763</v>
</b>
<b>
<a>19</a>
<b>26</b>
<v>16605</v>
</b>
<b>
<a>26</a>
<b>17296</b>
<v>12265</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>basicblock</src>
<trg>realnode</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>37832</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>17905</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>19216</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>18823</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>16929</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>13644</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>11703</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>16817</v>
</b>
<b>
<a>10</a>
<b>13</b>
<v>16741</v>
</b>
<b>
<a>13</a>
<b>19</b>
<v>17322</v>
</b>
<b>
<a>19</a>
<b>26</b>
<v>16368</v>
</b>
<b>
<a>26</a>
<b>17295</b>
<v>11973</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>basicblock</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>37515</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>17987</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>19072</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>17365</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>17931</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>13664</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>10900</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>16975</v>
</b>
<b>
<a>10</a>
<b>13</b>
<v>17232</v>
</b>
<b>
<a>13</a>
<b>19</b>
<v>17763</v>
</b>
<b>
<a>19</a>
<b>26</b>
<v>16605</v>
</b>
<b>
<a>26</a>
<b>17296</b>
<v>12265</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>flownode</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4957</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4220</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1805</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>1253</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>1750</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2240</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>2678</v>
</b>
<b>
<a>10</a>
<b>19</b>
<v>1819</v>
</b>
<b>
<a>19</a>
<b>60</b>
<v>1815</v>
</b>
<b>
<a>60</a>
<b>155471</b>
<v>1408</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>realnode</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4957</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4220</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1805</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>1253</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>1750</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2240</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>2678</v>
</b>
<b>
<a>10</a>
<b>19</b>
<v>1819</v>
</b>
<b>
<a>19</a>
<b>60</b>
<v>1815</v>
</b>
<b>
<a>60</a>
<b>141411</b>
<v>1408</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>basicblock</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4957</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4220</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1805</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>1253</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>1750</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2240</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>2678</v>
</b>
<b>
<a>10</a>
<b>19</b>
<v>1819</v>
</b>
<b>
<a>19</a>
<b>60</b>
<v>1815</v>
</b>
<b>
<a>60</a>
<b>155471</b>
<v>1408</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_scope_flow</name>
<cardinality>405895</cardinality>
<columnsizes>
<e>
<k>flow</k>
<v>405895</v>
</e>
<e>
<k>scope</k>
<v>56616</v>
</e>
<e>
<k>kind</k>
<v>4</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>flow</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>405895</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>flow</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>405895</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>flow</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>15663</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>8677</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>7135</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>4823</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>3426</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>4807</v>
</b>
<b>
<a>9</a>
<b>13</b>
<v>5102</v>
</b>
<b>
<a>13</a>
<b>23</b>
<v>4277</v>
</b>
<b>
<a>23</a>
<b>767</b>
<v>2706</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>16115</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>39685</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>816</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>flow</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>18869</a>
<b>18870</b>
<v>1</v>
</b>
<b>
<a>37919</a>
<b>37920</b>
<v>1</v>
</b>
<b>
<a>56616</a>
<b>56617</b>
<v>1</v>
</b>
<b>
<a>292491</a>
<b>292492</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>18869</a>
<b>18870</b>
<v>1</v>
</b>
<b>
<a>37919</a>
<b>37920</b>
<v>1</v>
</b>
<b>
<a>41145</a>
<b>41146</b>
<v>1</v>
</b>
<b>
<a>56616</a>
<b>56617</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_idoms</name>
<cardinality>2275369</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>2275369</v>
</e>
<e>
<k>immediate_dominator</k>
<v>2207166</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>immediate_dominator</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2275369</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>immediate_dominator</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2153132</v>
</b>
<b>
<a>2</a>
<b>11</b>
<v>54033</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_ssa_phi</name>
<cardinality>46687</cardinality>
<columnsizes>
<e>
<k>phi</k>
<v>21496</v>
</e>
<e>
<k>arg</k>
<v>44830</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>phi</src>
<trg>arg</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1782</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>16149</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2560</v>
</b>
<b>
<a>4</a>
<b>23</b>
<v>1003</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>arg</src>
<trg>phi</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>43208</v>
</b>
<b>
<a>2</a>
<b>8</b>
<v>1621</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_ssa_var</name>
<cardinality>272292</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>272292</v>
</e>
<e>
<k>var</k>
<v>217265</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>var</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>272292</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>var</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>194518</v>
</b>
<b>
<a>2</a>
<b>4</b>
<v>16728</v>
</b>
<b>
<a>4</a>
<b>35</b>
<v>6017</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_ssa_use</name>
<cardinality>487906</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>421169</v>
</e>
<e>
<k>var</k>
<v>239604</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>var</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>416004</v>
</b>
<b>
<a>2</a>
<b>185</b>
<v>5165</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>var</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>151110</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>42380</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>18095</v>
</b>
<b>
<a>4</a>
<b>7</b>
<v>18656</v>
</b>
<b>
<a>7</a>
<b>203</b>
<v>9362</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_ssa_defn</name>
<cardinality>267795</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>267795</v>
</e>
<e>
<k>node</k>
<v>261828</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>267795</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>node</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>258774</v>
</b>
<b>
<a>2</a>
<b>81</b>
<v>3053</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_scopes</name>
<cardinality>2056674</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>2056674</v>
</e>
<e>
<k>scope</k>
<v>51911</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2056674</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>5</b>
<v>3923</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>3611</v>
</b>
<b>
<a>7</a>
<b>9</b>
<v>3715</v>
</b>
<b>
<a>9</a>
<b>11</b>
<v>3941</v>
</b>
<b>
<a>11</a>
<b>14</b>
<v>4776</v>
</b>
<b>
<a>14</a>
<b>17</b>
<v>3965</v>
</b>
<b>
<a>17</a>
<b>22</b>
<v>4491</v>
</b>
<b>
<a>22</a>
<b>28</b>
<v>4078</v>
</b>
<b>
<a>28</a>
<b>37</b>
<v>4161</v>
</b>
<b>
<a>37</a>
<b>50</b>
<v>3938</v>
</b>
<b>
<a>50</a>
<b>72</b>
<v>3914</v>
</b>
<b>
<a>72</a>
<b>118</b>
<v>3953</v>
</b>
<b>
<a>118</a>
<b>5003</b>
<v>3439</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_scope_location</name>
<cardinality>56618</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>56618</v>
</e>
<e>
<k>scope</k>
<v>56618</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>scope</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>56618</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scope</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>56618</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_flags_versioned</name>
<cardinality>136</cardinality>
<columnsizes>
<e>
<k>name</k>
<v>136</v>
</e>
<e>
<k>value</k>
<v>83</v>
</e>
<e>
<k>version</k>
<v>2</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>name</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>136</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>68</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>11</v>
</b>
<b>
<a>15</a>
<b>16</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_syntax_error_versioned</name>
<cardinality>30</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>30</v>
</e>
<e>
<k>message</k>
<v>4</v>
</e>
<e>
<k>version</k>
<v>2</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>30</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>17</a>
<b>18</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_comments</name>
<cardinality>77830</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>77830</v>
</e>
<e>
<k>text</k>
<v>61555</v>
</e>
<e>
<k>location</k>
<v>77830</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>77830</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>77830</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>56275</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>4845</v>
</b>
<b>
<a>5</a>
<b>942</b>
<v>434</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>56275</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>4845</v>
</b>
<b>
<a>5</a>
<b>942</b>
<v>434</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>77830</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>77830</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_cobjects</name>
<cardinality>112856</cardinality>
<columnsizes>
<e>
<k>obj</k>
<v>112856</v>
</e>
</columnsizes>
<dependencies/>
</relation>
<relation>
<name>py_cobjecttypes</name>
<cardinality>111600</cardinality>
<columnsizes>
<e>
<k>obj</k>
<v>111600</v>
</e>
<e>
<k>typeof</k>
<v>65</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>obj</src>
<trg>typeof</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>111600</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>typeof</src>
<trg>obj</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>27</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4</v>
</b>
<b>
<a>3</a>
<b>5</b>
<v>5</v>
</b>
<b>
<a>6</a>
<b>19</b>
<v>5</v>
</b>
<b>
<a>19</a>
<b>54</b>
<v>5</v>
</b>
<b>
<a>58</a>
<b>295</b>
<v>5</v>
</b>
<b>
<a>325</a>
<b>857</b>
<v>5</v>
</b>
<b>
<a>923</a>
<b>73625</b>
<v>5</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_cobjectnames</name>
<cardinality>111600</cardinality>
<columnsizes>
<e>
<k>obj</k>
<v>111600</v>
</e>
<e>
<k>name</k>
<v>106332</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>obj</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>111600</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>obj</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>105898</v>
</b>
<b>
<a>2</a>
<b>413</b>
<v>434</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_cobject_sources</name>
<cardinality>114955</cardinality>
<columnsizes>
<e>
<k>obj</k>
<v>112856</v>
</e>
<e>
<k>kind</k>
<v>2</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>obj</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>110757</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2099</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>obj</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2423</a>
<b>2424</b>
<v>1</v>
</b>
<b>
<a>80595</a>
<b>80596</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_cmembers_versioned</name>
<cardinality>21362</cardinality>
<columnsizes>
<e>
<k>object</k>
<v>1681</v>
</e>
<e>
<k>name</k>
<v>8322</v>
</e>
<e>
<k>member</k>
<v>15501</v>
</e>
<e>
<k>version</k>
<v>2</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>object</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3</a>
<b>4</b>
<v>59</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>448</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>118</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>582</v>
</b>
<b>
<a>9</a>
<b>12</b>
<v>154</v>
</b>
<b>
<a>12</a>
<b>20</b>
<v>133</v>
</b>
<b>
<a>20</a>
<b>50</b>
<v>127</v>
</b>
<b>
<a>58</a>
<b>312</b>
<v>56</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>object</src>
<trg>member</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>3</a>
<b>4</b>
<v>59</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>448</v>
</b>
<b>
<a>5</a>
<b>8</b>
<v>118</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>591</v>
</b>
<b>
<a>9</a>
<b>12</b>
<v>154</v>
</b>
<b>
<a>12</a>
<b>20</b>
<v>133</v>
</b>
<b>
<a>21</a>
<b>59</b>
<v>127</v>
</b>
<b>
<a>60</a>
<b>206</b>
<v>47</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>object</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7390</v>
</b>
<b>
<a>2</a>
<b>6</b>
<v>656</v>
</b>
<b>
<a>6</a>
<b>567</b>
<v>276</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>member</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>7407</v>
</b>
<b>
<a>2</a>
<b>6</b>
<v>647</v>
</b>
<b>
<a>6</a>
<b>280</b>
<v>267</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>member</src>
<trg>object</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>14765</v>
</b>
<b>
<a>2</a>
<b>249</b>
<v>736</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>member</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>14803</v>
</b>
<b>
<a>2</a>
<b>84</b>
<v>698</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_citems</name>
<cardinality>3959</cardinality>
<columnsizes>
<e>
<k>object</k>
<v>213</v>
</e>
<e>
<k>index</k>
<v>593</v>
</e>
<e>
<k>member</k>
<v>1906</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>object</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>41</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>37</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>37</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>7</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>29</v>
</b>
<b>
<a>6</a>
<b>12</b>
<v>16</v>
</b>
<b>
<a>12</a>
<b>22</b>
<v>16</v>
</b>
<b>
<a>24</a>
<b>42</b>
<v>16</v>
</b>
<b>
<a>42</a>
<b>594</b>
<v>14</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>object</src>
<trg>member</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>41</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>40</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>34</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>20</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>16</v>
</b>
<b>
<a>6</a>
<b>12</b>
<v>16</v>
</b>
<b>
<a>12</a>
<b>22</b>
<v>16</v>
</b>
<b>
<a>24</a>
<b>42</b>
<v>16</v>
</b>
<b>
<a>42</a>
<b>546</b>
<v>14</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>object</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>186</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>62</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>89</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>44</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>41</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>83</v>
</b>
<b>
<a>9</a>
<b>14</b>
<v>46</v>
</b>
<b>
<a>14</a>
<b>214</b>
<v>42</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>index</src>
<trg>member</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>186</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>62</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>89</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>44</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>41</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>83</v>
</b>
<b>
<a>9</a>
<b>14</b>
<v>46</v>
</b>
<b>
<a>14</a>
<b>158</b>
<v>42</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>member</src>
<trg>object</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1112</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>215</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>303</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>101</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>166</v>
</b>
<b>
<a>7</a>
<b>21</b>
<v>9</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>member</src>
<trg>index</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1139</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>212</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>298</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>92</v>
</b>
<b>
<a>5</a>
<b>9</b>
<v>165</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>ext_argtype</name>
<cardinality>6320</cardinality>
<columnsizes>
<e>
<k>funcid</k>
<v>4069</v>
</e>
<e>
<k>arg</k>
<v>50</v>
</e>
<e>
<k>typeid</k>
<v>466</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>funcid</src>
<trg>arg</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2726</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>932</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>329</v>
</b>
<b>
<a>4</a>
<b>18</b>
<v>80</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>funcid</src>
<trg>typeid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2694</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1149</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>225</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>arg</src>
<trg>funcid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>23</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>5</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>2</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>2</v>
</b>
<b>
<a>31</a>
<b>32</b>
<v>2</v>
</b>
<b>
<a>141</a>
<b>142</b>
<v>2</v>
</b>
<b>
<a>449</a>
<b>450</b>
<v>2</v>
</b>
<b>
<a>1365</a>
<b>1366</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>arg</src>
<trg>typeid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>8</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>2</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2</v>
</b>
<b>
<a>12</a>
<b>13</b>
<v>2</v>
</b>
<b>
<a>157</a>
<b>158</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>typeid</src>
<trg>funcid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>68</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>86</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>68</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>38</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>26</v>
</b>
<b>
<a>6</a>
<b>8</b>
<v>29</v>
</b>
<b>
<a>8</a>
<b>10</b>
<v>35</v>
</b>
<b>
<a>10</a>
<b>16</b>
<v>41</v>
</b>
<b>
<a>16</a>
<b>22</b>
<v>35</v>
</b>
<b>
<a>24</a>
<b>505</b>
<v>35</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>typeid</src>
<trg>arg</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>424</v>
</b>
<b>
<a>2</a>
<b>5</b>
<v>35</v>
</b>
<b>
<a>9</a>
<b>17</b>
<v>5</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>ext_rettype</name>
<cardinality>4719</cardinality>
<columnsizes>
<e>
<k>funcid</k>
<v>4321</v>
</e>
<e>
<k>typeid</k>
<v>154</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>funcid</src>
<trg>typeid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>4042</v>
</b>
<b>
<a>2</a>
<b>11</b>
<v>279</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>typeid</src>
<trg>funcid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>59</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>14</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>23</v>
</b>
<b>
<a>4</a>
<b>6</b>
<v>8</v>
</b>
<b>
<a>8</a>
<b>14</b>
<v>11</v>
</b>
<b>
<a>22</a>
<b>40</b>
<v>11</v>
</b>
<b>
<a>43</a>
<b>115</b>
<v>11</v>
</b>
<b>
<a>116</a>
<b>454</b>
<v>11</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>ext_proptype</name>
<cardinality>398</cardinality>
<columnsizes>
<e>
<k>propid</k>
<v>386</v>
</e>
<e>
<k>typeid</k>
<v>32</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>propid</src>
<trg>typeid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>374</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>11</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>typeid</src>
<trg>propid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>5</v>
</b>
<b>
<a>8</a>
<b>9</b>
<v>2</v>
</b>
<b>
<a>19</a>
<b>20</b>
<v>2</v>
</b>
<b>
<a>35</a>
<b>36</b>
<v>2</v>
</b>
<b>
<a>52</a>
<b>53</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>ext_argreturn</name>
<cardinality>26</cardinality>
<columnsizes>
<e>
<k>funcid</k>
<v>26</v>
</e>
<e>
<k>arg</k>
<v>5</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>funcid</src>
<trg>arg</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>26</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>arg</src>
<trg>funcid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_special_objects</name>
<cardinality>40</cardinality>
<columnsizes>
<e>
<k>obj</k>
<v>40</v>
</e>
<e>
<k>name</k>
<v>40</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>obj</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>40</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>obj</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>40</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>py_decorated_object</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>object</k>
<v>100</v>
</e>
<e>
<k>level</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>object</src>
<trg>level</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>level</src>
<trg>object</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlEncoding</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>encoding</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>encoding</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>encoding</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlDTDs</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>root</k>
<v>100</v>
</e>
<e>
<k>publicId</k>
<v>100</v>
</e>
<e>
<k>systemId</k>
<v>100</v>
</e>
<e>
<k>fileid</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>root</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>publicId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>systemId</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>root</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>root</src>
<trg>publicId</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>root</src>
<trg>systemId</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>root</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>publicId</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>publicId</src>
<trg>root</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>publicId</src>
<trg>systemId</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>publicId</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>systemId</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>systemId</src>
<trg>root</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>systemId</src>
<trg>publicId</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>systemId</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>root</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>publicId</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>systemId</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlElements</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>name</k>
<v>100</v>
</e>
<e>
<k>parentid</k>
<v>100</v>
</e>
<e>
<k>idx</k>
<v>100</v>
</e>
<e>
<k>fileid</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlAttrs</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>elementid</k>
<v>100</v>
</e>
<e>
<k>name</k>
<v>100</v>
</e>
<e>
<k>value</k>
<v>100</v>
</e>
<e>
<k>idx</k>
<v>100</v>
</e>
<e>
<k>fileid</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>elementid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>elementid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>elementid</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>elementid</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>elementid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>elementid</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>elementid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>name</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>elementid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>elementid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>elementid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>name</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlNs</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>prefixName</k>
<v>100</v>
</e>
<e>
<k>URI</k>
<v>100</v>
</e>
<e>
<k>fileid</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>prefixName</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>URI</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>prefixName</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>prefixName</src>
<trg>URI</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>prefixName</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>URI</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>URI</src>
<trg>prefixName</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>URI</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>prefixName</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>URI</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlHasNs</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>elementId</k>
<v>100</v>
</e>
<e>
<k>nsId</k>
<v>100</v>
</e>
<e>
<k>fileid</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>elementId</src>
<trg>nsId</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>elementId</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>nsId</src>
<trg>elementId</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>nsId</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>elementId</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>nsId</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlComments</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>text</k>
<v>100</v>
</e>
<e>
<k>parentid</k>
<v>100</v>
</e>
<e>
<k>fileid</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmlChars</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>100</v>
</e>
<e>
<k>text</k>
<v>100</v>
</e>
<e>
<k>parentid</k>
<v>100</v>
</e>
<e>
<k>idx</k>
<v>100</v>
</e>
<e>
<k>isCDATA</k>
<v>100</v>
</e>
<e>
<k>fileid</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>isCDATA</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>isCDATA</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>text</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>isCDATA</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>parentid</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>isCDATA</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>isCDATA</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>isCDATA</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>isCDATA</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>isCDATA</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>isCDATA</src>
<trg>fileid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>text</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>parentid</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>fileid</src>
<trg>isCDATA</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>xmllocations</name>
<cardinality>100</cardinality>
<columnsizes>
<e>
<k>xmlElement</k>
<v>100</v>
</e>
<e>
<k>location</k>
<v>100</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>xmlElement</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>xmlElement</trg>
<val>
<hist>
<budget>12</budget>
<bs/>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml</name>
<key>id</key>
<cardinality>885</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>885</v>
</e>
<e>
<k>kind</k>
<v>4</v>
</e>
<e>
<k>parent</k>
<v>204</v>
</e>
<e>
<k>idx</k>
<v>25</v>
</e>
<e>
<k>tag</k>
<v>8</v>
</e>
<e>
<k>tostring</k>
<v>318</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>885</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>885</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>885</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>885</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>id</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>885</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>35</a>
<b>36</b>
<v>1</v>
</b>
<b>
<a>149</a>
<b>150</b>
<v>1</v>
</b>
<b>
<a>700</a>
<b>701</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>33</a>
<b>34</b>
<v>1</v>
</b>
<b>
<a>90</a>
<b>91</b>
<v>1</v>
</b>
<b>
<a>183</a>
<b>184</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>1</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>25</a>
<b>26</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>kind</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>1</v>
</b>
<b>
<a>67</a>
<b>68</b>
<v>1</v>
</b>
<b>
<a>240</a>
<b>241</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>72</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>35</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>29</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>14</v>
</b>
<b>
<a>12</a>
<b>21</b>
<v>17</v>
</b>
<b>
<a>22</a>
<b>25</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>131</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>43</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>30</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>72</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>35</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>29</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>14</v>
</b>
<b>
<a>12</a>
<b>21</b>
<v>17</v>
</b>
<b>
<a>22</a>
<b>25</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>120</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>41</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>36</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>7</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>parent</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>33</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>72</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>35</v>
</b>
<b>
<a>5</a>
<b>6</b>
<v>5</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>24</v>
</b>
<b>
<a>8</a>
<b>11</b>
<v>14</v>
</b>
<b>
<a>12</a>
<b>14</b>
<v>16</v>
</b>
<b>
<a>16</a>
<b>23</b>
<v>3</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>7</v>
</b>
<b>
<a>5</a>
<b>20</b>
<v>2</v>
</b>
<b>
<a>20</a>
<b>25</b>
<v>2</v>
</b>
<b>
<a>25</a>
<b>33</b>
<v>2</v>
</b>
<b>
<a>33</a>
<b>56</b>
<v>2</v>
</b>
<b>
<a>61</a>
<b>64</b>
<v>2</v>
</b>
<b>
<a>95</a>
<b>100</b>
<v>2</v>
</b>
<b>
<a>149</a>
<b>172</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>14</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>4</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>6</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>7</v>
</b>
<b>
<a>5</a>
<b>20</b>
<v>2</v>
</b>
<b>
<a>20</a>
<b>25</b>
<v>2</v>
</b>
<b>
<a>25</a>
<b>33</b>
<v>2</v>
</b>
<b>
<a>33</a>
<b>56</b>
<v>2</v>
</b>
<b>
<a>61</a>
<b>64</b>
<v>2</v>
</b>
<b>
<a>95</a>
<b>100</b>
<v>2</v>
</b>
<b>
<a>149</a>
<b>172</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>11</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>5</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>4</v>
</b>
<b>
<a>6</a>
<b>7</b>
<v>2</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>idx</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>3</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>4</v>
</b>
<b>
<a>5</a>
<b>7</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>11</b>
<v>2</v>
</b>
<b>
<a>12</a>
<b>15</b>
<v>2</v>
</b>
<b>
<a>15</a>
<b>16</b>
<v>1</v>
</b>
<b>
<a>18</a>
<b>19</b>
<v>2</v>
</b>
<b>
<a>28</a>
<b>31</b>
<v>2</v>
</b>
<b>
<a>52</a>
<b>56</b>
<v>2</v>
</b>
<b>
<a>87</a>
<b>88</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>4</a>
<b>5</b>
<v>1</v>
</b>
<b>
<a>15</a>
<b>16</b>
<v>1</v>
</b>
<b>
<a>26</a>
<b>27</b>
<v>1</v>
</b>
<b>
<a>35</a>
<b>36</b>
<v>1</v>
</b>
<b>
<a>149</a>
<b>150</b>
<v>1</v>
</b>
<b>
<a>654</a>
<b>655</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>8</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>1</v>
</b>
<b>
<a>25</a>
<b>26</b>
<v>1</v>
</b>
<b>
<a>33</a>
<b>34</b>
<v>1</v>
</b>
<b>
<a>90</a>
<b>91</b>
<v>1</v>
</b>
<b>
<a>183</a>
<b>184</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>2</v>
</b>
<b>
<a>3</a>
<b>4</b>
<v>2</v>
</b>
<b>
<a>7</a>
<b>8</b>
<v>1</v>
</b>
<b>
<a>9</a>
<b>10</b>
<v>1</v>
</b>
<b>
<a>11</a>
<b>12</b>
<v>1</v>
</b>
<b>
<a>23</a>
<b>24</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tag</src>
<trg>tostring</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>3</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
<b>
<a>10</a>
<b>11</b>
<v>1</v>
</b>
<b>
<a>13</a>
<b>14</b>
<v>1</v>
</b>
<b>
<a>67</a>
<b>68</b>
<v>1</v>
</b>
<b>
<a>223</a>
<b>224</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>209</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>42</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>29</v>
</b>
<b>
<a>6</a>
<b>15</b>
<v>25</v>
</b>
<b>
<a>15</a>
<b>18</b>
<v>13</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>kind</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>318</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>parent</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>213</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>41</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>27</v>
</b>
<b>
<a>6</a>
<b>15</b>
<v>25</v>
</b>
<b>
<a>15</a>
<b>18</b>
<v>12</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>idx</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>272</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>34</v>
</b>
<b>
<a>3</a>
<b>10</b>
<v>12</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>tostring</src>
<trg>tag</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>318</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml_anchors</name>
<cardinality>1</cardinality>
<columnsizes>
<e>
<k>node</k>
<v>1</v>
</e>
<e>
<k>anchor</k>
<v>1</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>node</src>
<trg>anchor</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>anchor</src>
<trg>node</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml_aliases</name>
<cardinality>1</cardinality>
<columnsizes>
<e>
<k>alias</k>
<v>1</v>
</e>
<e>
<k>target</k>
<v>1</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>alias</src>
<trg>target</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>target</src>
<trg>alias</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml_scalars</name>
<cardinality>700</cardinality>
<columnsizes>
<e>
<k>scalar</k>
<v>700</v>
</e>
<e>
<k>style</k>
<v>3</v>
</e>
<e>
<k>value</k>
<v>241</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>scalar</src>
<trg>style</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>700</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>scalar</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>700</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>style</src>
<trg>scalar</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>14</a>
<b>15</b>
<v>1</v>
</b>
<b>
<a>97</a>
<b>98</b>
<v>1</v>
</b>
<b>
<a>589</a>
<b>590</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>style</src>
<trg>value</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>12</a>
<b>13</b>
<v>1</v>
</b>
<b>
<a>47</a>
<b>48</b>
<v>1</v>
</b>
<b>
<a>183</a>
<b>184</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>scalar</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>158</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>32</v>
</b>
<b>
<a>3</a>
<b>6</b>
<v>19</v>
</b>
<b>
<a>6</a>
<b>15</b>
<v>20</v>
</b>
<b>
<a>15</a>
<b>18</b>
<v>12</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>value</src>
<trg>style</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>240</v>
</b>
<b>
<a>2</a>
<b>3</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml_errors</name>
<key>id</key>
<cardinality>1</cardinality>
<columnsizes>
<e>
<k>id</k>
<v>1</v>
</e>
<e>
<k>message</k>
<v>1</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>id</src>
<trg>message</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>message</src>
<trg>id</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>1</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
<relation>
<name>yaml_locations</name>
<cardinality>71</cardinality>
<columnsizes>
<e>
<k>locatable</k>
<v>71</v>
</e>
<e>
<k>location</k>
<v>71</v>
</e>
</columnsizes>
<dependencies>
<dep>
<src>locatable</src>
<trg>location</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>71</v>
</b>
</bs>
</hist>
</val>
</dep>
<dep>
<src>location</src>
<trg>locatable</trg>
<val>
<hist>
<budget>12</budget>
<bs>
<b>
<a>1</a>
<b>2</b>
<v>71</v>
</b>
</bs>
</hist>
</val>
</dep>
</dependencies>
</relation>
</stats>
</dbstats>
