[2025-06-03 11:04:37] This is codeql execute queries -J-Xmx1374M --verbosity=progress --logdir=E:\advance_javascript\codeQL\7\python-db-updated\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\7\python-db-updated\results -- E:\advance_javascript\codeQL\7\python-db-updated\db-python queries/python-password-vulnerability.ql
[2025-06-03 11:04:37] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --format=json -- queries/python-password-vulnerability.ql
[2025-06-03 11:04:37] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 11:04:37] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\queries\\python-password-vulnerability.ql"
                      ]
[2025-06-03 11:04:37] Refusing fancy output: The terminal is not an xterm: 
[2025-06-03 11:04:37] Creating executor with 1 threads.
[2025-06-03 11:04:38] Calling plumbing command: codeql resolve extensions --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations queries/python-password-vulnerability.ql
[2025-06-03 11:04:38] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --allow-library-packs --format startingpacks -- queries/python-password-vulnerability.ql
[2025-06-03 11:04:38] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 11:04:38] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7"
                      ]
[2025-06-03 11:04:38] Calling plumbing command: codeql resolve extensions-by-pack --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\7
[2025-06-03 11:04:38] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}
[2025-06-03 11:04:38] [SPAMMY] resolve extensions-by-pack> [DERIVATION] python-security-queries: 1.0.0 {python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 1] python-security-queries: 1.0.0
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/python-all: * [*] {python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/python-all: 4.0.7
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 11:04:40] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 11:04:40] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\ext\default-threat-models-fixup.model.yml.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 11:04:40] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Asyncpg.model.yml.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 5 tuples.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 6 tuples.
[2025-06-03 11:04:40] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Stdlib.model.yml.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 12 tuples.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 1 tuples.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 66 tuples.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 11:04:40] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\empty.model.yml.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 0 tuples.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 0 tuples.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 0 tuples.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 11:04:40] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 11:04:41] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\subclass-capture\ALL.model.yml.
[2025-06-03 11:04:41] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 58275 tuples.
[2025-06-03 11:04:41] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-03 11:04:41] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 11:04:41] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-03 11:04:41] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-03 11:04:41] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-03 11:04:41] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-03 11:04:41] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-03 11:04:41] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=7+1+2+1+2",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=20+4+1*2+2+1",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4+2+1+2+1*2+4+2",
                                "columnNumbers" : "A=9*12"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=29",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=37+1+2+4+2*2+4+2*3+1+2+1+2+1+2+4+2+4+2*2+3+2*2+3+1+2*4+4+1+4+1+4+1*5+2*4+4+1+2*11+3+2+3+4+1+2*2+1+2",
                                "columnNumbers" : "A=9*66"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+3*58274",
                                "columnNumbers" : "A=5*58275"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 11:04:41] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=7+1+2+1+2",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=20+4+1*2+2+1",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4+2+1+2+1*2+4+2",
                                "columnNumbers" : "A=9*12"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=29",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=37+1+2+4+2*2+4+2*3+1+2+1+2+1+2+4+2+4+2*2+3+2*2+3+1+2*4+4+1+4+1+4+1*5+2*4+4+1+2*11+3+2+3+4+1+2*2+1+2",
                                "columnNumbers" : "A=9*66"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+3*58274",
                                "columnNumbers" : "A=5*58275"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 11:04:42] Calling plumbing command: codeql resolve library-path --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --query=E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql --format=json
[2025-06-03 11:04:42] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql.
[2025-06-03 11:04:42] [DETAILS] resolve library-path> Found enclosing pack 'python-security-queries' at E:\advance_javascript\codeQL\7.
[2025-06-03 11:04:42] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-03 11:04:42] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\7\qlpack.yml.
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] python-security-queries: 1.0.0 {python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 1] python-security-queries: 1.0.0
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] codeql/python-all: * [*] {python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 2] codeql/python-all: 4.0.7
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] codeql/yaml: * [*] {codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] codeql/xml: * [*] {codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] codeql/util: * [*] {codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] codeql/tutorial: * [*] {codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] codeql/threat-models: * [*] {codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] codeql/regex: * [*] {codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] codeql/mad: * [*] {codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] codeql/dataflow: * [*] {codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] codeql/typetracking: * [*] {codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DERIVATION] codeql/ssa: * [*] {codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 11:04:42] [SPAMMY] resolve library-path> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 11:04:42] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\7 resolved OK.
[2025-06-03 11:04:42] [DETAILS] resolve library-path> Found dbscheme through QL packs: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmlecode.python.dbscheme.
[2025-06-03 11:04:42] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\dataflow\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\mad\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\regex\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\ssa\\1.1.2",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\tutorial\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\typetracking\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\xml\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\yaml\\1.0.23"
                        ],
                        "dbscheme" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmlecode.python.dbscheme",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "python-security-queries\\queries\\python-password-vulnerability.ql",
                        "qlPackName" : "python-security-queries"
                      }
[2025-06-03 11:04:42] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql.
[2025-06-03 11:04:42] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql.
[2025-06-03 11:04:43] Resolved file set for E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql hashes to 6724735d6011ba5ecb5ff1f2411c231a.
[2025-06-03 11:04:43] [DETAILS] execute queries> Checking QL for E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql.
[2025-06-03 11:04:43] Stale frontend caches are invalidated based on import graph reachability.
[2025-06-03 11:04:43] ExternalModuleBindingPass ...
[2025-06-03 11:04:44] ExternalModuleBindingPass time: 00:00.951
[2025-06-03 11:04:44] CollectInstantiationsPass ...
[2025-06-03 11:04:45] CollectInstantiationsPass time: 00:00.276
[2025-06-03 11:04:45] Ql checks ...
[2025-06-03 11:04:48] Ql checks time: 00:03.322
[2025-06-03 11:04:55] Compilation pipeline
[2025-06-03 11:04:56] Type Inference ...
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: Best number of iterations,Maximum number of runs,Number of BDD variables,Size of BDD for typefacts
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,96,97
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,326,384
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,24,24
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,48,72
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,5,5
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,7,7
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,561,15752
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,12,13
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,8,10
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,7,7
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,5,5
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,10,11
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,10,10
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,13,17
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,8,10
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,52,62
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,18,18
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,41,52
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:04:56] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:04:57] Type Inference time: 00:01.422
[2025-06-03 11:04:57] [DETAILS] execute queries> Optimizing E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql.
[2025-06-03 11:04:58] Processing compilation pipeline
[2025-06-03 11:04:58] Started compiling a query
[2025-06-03 11:04:58] Initialising compiler...
[2025-06-03 11:04:58] 	 ... compiler initialised
[2025-06-03 11:04:58] About to start query optimisation
[2025-06-03 11:04:58] Compilation cache hit - skipping compilation.
[2025-06-03 11:04:58] Compilation cache hit - skipping compilation.
[2025-06-03 11:04:58] Compilation cache miss for c516872d320f253a999cc014c4047db9.
[2025-06-03 11:04:59] Stored compiled program for c516872d320f253a999cc014c4047db9.
[2025-06-03 11:04:59] CSV_COMPILATION: NONE,MISC,RA_TRANSLATION,OPTIMISATIONS
[2025-06-03 11:04:59] CSV_COMPILATION: 159,360,326,899
[2025-06-03 11:05:00] [SPAMMY] execute queries> No database upgrade/downgrade needed for E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql
[2025-06-03 11:05:00] [PROGRESS] execute queries> [1/1 comp 17.8s] Compiled E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql.
[2025-06-03 11:05:00] [PROGRESS] execute queries> Starting evaluation of python-security-queries\queries\python-password-vulnerability.ql.
[2025-06-03 11:05:00] Starting evaluation of E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql
[2025-06-03 11:05:00] (0s) Start query execution
[2025-06-03 11:05:00] (0s) Beginning execution of E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql
[2025-06-03 11:05:00] (0s)  >>> Created relation py_exprs/4@62fb455g with 86 rows and digest 476bbb9bpd54bfhhl3b277dvi93.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_expr_lists/3@1b5e391t with 27 rows and digest c28eb8cpfv32pdrjrv9jjkt6o1c.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_expr_lists_120#join_rhs/3@3304a2u1
[2025-06-03 11:05:00] (0s)  >>> Created relation py_expr_lists_120#join_rhs/3@3304a2u1 with 27 rows and digest 7d37e3skhhddakh4trmkoosn29a.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_stmts/4@93720efi with 27 rows and digest ce9b86tatnb2dmhmci78er5qqf8.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_stmts_10#join_rhs/2@97ec97cm
[2025-06-03 11:05:00] (0s)  >>> Created relation py_stmts_10#join_rhs/2@97ec97cm with 27 rows and digest acaec0mjjbssf7gvsdkslhopcv1.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_exprs_10#join_rhs/2@073ac12u
[2025-06-03 11:05:00] (0s)  >>> Created relation py_exprs_10#join_rhs/2@073ac12u with 86 rows and digest 5cb25bh65v882cbcgs7ktbjlra0.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_strs/3@35cd6b34 with 83 rows and digest 473c56049jkfnie29f6s5t9remb.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_strs_120#join_rhs/3@bec877pd
[2025-06-03 11:05:00] (0s)  >>> Created relation py_strs_120#join_rhs/3@bec877pd with 83 rows and digest 5f8da0i5n99qiq8i5of4fpnl5t3.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_locations/2@d222f4l2 with 124 rows and digest 1708daoeqjn73e3dd8krpu4got4.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_scope_location/2@9810bb7v with 5 rows and digest 19b259klf7ehbtpohub8ijpmbv3.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_scope_location_1#antijoin_rhs/1@8ca5b9j1
[2025-06-03 11:05:00] (0s)  >>> Created relation py_scope_location_1#antijoin_rhs/1@8ca5b9j1 with 5 rows and digest 02de80n4di8hr38mqovcu5vedg8.
[2025-06-03 11:05:00] (0s)  >>> Created relation locations_ast/6@aba204da with 140 rows and digest acf902vjm0jpiucb5cthhe02kqa.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate locations_ast_234501#join_rhs/6@6e5a5d36
[2025-06-03 11:05:00] (0s)  >>> Created relation locations_ast_234501#join_rhs/6@6e5a5d36 with 140 rows and digest 605c30ku6ajfqtg0r2neom48g0c.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_type_parameters/4@a1fcadgi with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that py_type_parameters_10#join_rhs/2@dc8dd6s9 is empty, due to py_type_parameters/4@a1fcadgi.
[2025-06-03 11:05:00] (0s) Inferred that py_type_parameters_20#join_rhs/2@e9503ao7 is empty, due to py_type_parameters/4@a1fcadgi.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_comprehensions/3@5a5a332m with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that AstExtended::Comprehension.getASubExpression/0#dispred#63af4970/2@8f912bbl is empty, due to py_comprehensions/3@5a5a332m.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::ListComp_.getAGenerator/0#dispred#0726485f/2@776433m6 is empty, due to py_comprehensions/3@5a5a332m.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_StringParts/3@40e65fbh with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that py_StringParts_10#join_rhs/2@09edc2s7 is empty, due to py_StringParts/3@40e65fbh.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Str_.getAnImplicitlyConcatenatedPart/0#dispred#08057c4a/2@b548ab4a is empty, due to py_StringParts_10#join_rhs/2@09edc2s7.
[2025-06-03 11:05:00] (0s) Inferred that project#AstGenerated::Str_.getAnImplicitlyConcatenatedPart/0#dispred#08057c4a/1@fbc996q5 is empty, due to AstGenerated::Str_.getAnImplicitlyConcatenatedPart/0#dispred#08057c4a/2@b548ab4a.
[2025-06-03 11:05:00] (0s) Inferred that Exprs::StringLiteral.isUnicode/0#dispred#64a81fc8#b/1@7bc781ti is empty, due to project#AstGenerated::Str_.getAnImplicitlyConcatenatedPart/0#dispred#08057c4a/1@fbc996q5.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_patterns/4@5b268b2a with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that py_patterns_20#join_rhs/2@063912q6 is empty, due to py_patterns/4@5b268b2a.
[2025-06-03 11:05:00] (0s) Inferred that py_patterns_10#join_rhs/2@626962h6 is empty, due to py_patterns/4@5b268b2a.
[2025-06-03 11:05:00] (0s) Inferred that py_patterns_230#join_rhs/3@54219et2 is empty, due to py_patterns/4@5b268b2a.
[2025-06-03 11:05:00] (0s) Inferred that #AstExtended::AstNode.getAChildNode/0#dispred#a130356dPlus#fb#flipped/2@f3d5f3hl is empty, due to py_patterns/4@5b268b2a.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::PatternList_.getAnItem/0#dispred#3c725bc9/2@4e0113eh is empty, due to py_patterns_20#join_rhs/2@063912q6.
[2025-06-03 11:05:00] (0s) Inferred that Patterns::MatchAsPattern.getAlias/0#dispred#766c9d3f/2@e01a224v is empty, due to py_patterns_10#join_rhs/2@626962h6.
[2025-06-03 11:05:00] (0s) Inferred that Patterns::MatchCapturePattern.getVariable/0#dispred#1afb04d9/2@fac7a9lg is empty, due to py_patterns_10#join_rhs/2@626962h6.
[2025-06-03 11:05:00] (0s) Inferred that #AstExtended::AstNode.getAChildNode/0#dispred#a130356dPlus#fb#flipped_10#join_rhs/2@bf08f8qt is empty, due to #AstExtended::AstNode.getAChildNode/0#dispred#a130356dPlus#fb#flipped/2@f3d5f3hl.
[2025-06-03 11:05:00] (0s) Inferred that SsaDefinitions::SsaSource::pattern_alias_definition/2#e55586af/2@f8ca01uf is empty, due to Patterns::MatchAsPattern.getAlias/0#dispred#766c9d3f/2@e01a224v.
[2025-06-03 11:05:00] (0s) Inferred that SsaDefinitions::SsaSource::pattern_capture_definition/2#77b3ae42/2@330db0d6 is empty, due to Patterns::MatchCapturePattern.getVariable/0#dispred#1afb04d9/2@fac7a9lg.
[2025-06-03 11:05:00] (0s) Inferred that Patterns::Pattern.getCase/0#72a79c75/2@c7fb3fgs is empty, due to #AstExtended::AstNode.getAChildNode/0#dispred#a130356dPlus#fb#flipped_10#join_rhs/2@bf08f8qt.
[2025-06-03 11:05:00] (0s) Inferred that cached_SsaDefinitions::SsaSource::pattern_alias_definition/2#e55586af/2@f91af3ha is empty, due to SsaDefinitions::SsaSource::pattern_alias_definition/2#e55586af/2@f8ca01uf.
[2025-06-03 11:05:00] (0s) Inferred that cached_SsaDefinitions::SsaSource::pattern_capture_definition/2#77b3ae42/2@6e282c28 is empty, due to SsaDefinitions::SsaSource::pattern_capture_definition/2#77b3ae42/2@330db0d6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_locations_10#join_rhs/2@cad24bnc
[2025-06-03 11:05:00] (0s)  >>> Created relation py_locations_10#join_rhs/2@cad24bnc with 124 rows and digest 56b5b7iks3ev6iouv9k9saassfb.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_dict_items/4@f807ab0r with 11 rows and digest e7f31dnae9n5463dj372lsmp48a.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_dict_items_10#join_rhs/2@1abaaa6d
[2025-06-03 11:05:00] (0s)  >>> Created relation py_dict_items_10#join_rhs/2@1abaaa6d with 11 rows and digest cb4086t30dfmjnkpga0adc3gk95.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_Functions/2@dc6e589f with 3 rows and digest b453cdg6rk1u5tkucqusp1ub0o8.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_exprs_203#join_rhs/3@0e8aa5mo
[2025-06-03 11:05:00] (0s)  >>> Created relation py_exprs_203#join_rhs/3@0e8aa5mo with 86 rows and digest add76dsd6fkuqjdv7esrobrejh8.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_Classes/2@2777c63f with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Class::Class.toString/0#dispred#19bc00cc/2@ef38470h is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:05:00] (0s) Inferred that py_Classes_10#join_rhs/2@132b13h6 is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:05:00] (0s) Inferred that ImportTime::ImportTimeScope.entryEdge/2#dispred#d2e3d051/3@14e4aflr is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:05:00] (0s) Inferred that Definitions::class_with_global_metaclass/2#f9fedc22/2@2f3991ea is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:05:00] (0s) Inferred that Definitions::ClassLocalVariable#75f27010/1@f553a8ab is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:05:00] (0s) Inferred that ImportTime::class_var_scope/3#59eab847/3@8ff9bdsa is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:05:00] (0s) Inferred that Variables::Variable.isSelf/0#dispred#7f6832b2/1@05d48c1b is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:05:00] (0s) Inferred that Class::Class.getScope/0#dispred#a9980f24/2@cbc60c1m is empty, due to py_Classes_10#join_rhs/2@132b13h6.
[2025-06-03 11:05:00] (0s) Inferred that project#ImportTime::ImportTimeScope.entryEdge/2#dispred#d2e3d051/2@f4a2dbiu is empty, due to ImportTime::ImportTimeScope.entryEdge/2#dispred#d2e3d051/3@14e4aflr.
[2025-06-03 11:05:00] (0s) Inferred that ImportTime::ImportTimeScope.getOuterVariable/1#dispred#2509ef70/3@31cc1fjt is empty, due to ImportTime::class_var_scope/3#59eab847/3@8ff9bdsa.
[2025-06-03 11:05:00] (0s) Inferred that project#ImportTime::ImportTimeScope.getOuterVariable/1#dispred#2509ef70/2@1cbf43a0 is empty, due to ImportTime::ImportTimeScope.getOuterVariable/1#dispred#2509ef70/3@31cc1fjt.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_Modules/1@3e58c00b with 2 rows and digest 1758e8s2mdeq4m767kljah7j4r6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_scope_location_10#join_rhs/2@9c0b7etf
[2025-06-03 11:05:00] (0s)  >>> Created relation py_scope_location_10#join_rhs/2@9c0b7etf with 5 rows and digest c7b5c7i4jfqn6ldjclkndfnvs2f.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Stmts::Stmt.getLocation/0#dispred#18f9d034/2@d3cb24lu
[2025-06-03 11:05:00] (0s)  >>> Created relation Stmts::Stmt.getLocation/0#dispred#18f9d034/2@d3cb24lu with 27 rows and digest 4866fbvmp483jnqe0l669693ql4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Expr.getLocation/0#dispred#c128155b/2@d10f96ct
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Expr.getLocation/0#dispred#c128155b/2@d10f96ct with 86 rows and digest b641020141ummd70evtn2k8uh21.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstExtended::AstNode.getLocation/0#dispred#6b4dcb62/2@ccf6dfeh
[2025-06-03 11:05:00] (0s)  >>> Created relation AstExtended::AstNode.getLocation/0#dispred#6b4dcb62/2@ccf6dfeh with 129 rows and digest 230d52r3fgbfc6mbinshdjrdvee.
[2025-06-03 11:05:00] (0s) No need to promote strings for predicate AstExtended::AstNode.getLocation/0#dispred#6b4dcb62  as it does not contain computed strings.
[2025-06-03 11:05:00] (0s)  >>> Created relation cached_AstExtended::AstNode.getLocation/0#dispred#6b4dcb62/2@389a43u0 with 129 rows and digest 230d52r3fgbfc6mbinshdjrdvee.
[2025-06-03 11:05:00] (0s)  >>> Created relation folders/2@bad7d9u0 with 7 rows and digest 504939bjsummg163iumr35cn6fc.
[2025-06-03 11:05:00] (0s)  >>> Created relation files/2@ec93749g with 7 rows and digest 25e58crktdgaufn1jel1vuc5rq9.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Input::ContainerBase.getAbsolutePath/0#dispred#bb5aca3b/2@966d67vo
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Input::ContainerBase.getAbsolutePath/0#dispred#bb5aca3b/2@966d67vo with 14 rows and digest da9ba69sf3j2f354p2stvjdrj72.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_module_path/2@7a0f7599 with 2 rows and digest 76820d8bcba222g98abm3vg7e07.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_flow_bb_node/4@f7fce6uq with 109 rows and digest bd8fa5o57ep1d9dgms6rdqu4ugc.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Scope_#cc08b056/1@78c72bkc
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Scope_#cc08b056/1@78c72bkc with 5 rows and digest 02de80n4di8hr38mqovcu5vedg8.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_scopes/2@a7df0e9a with 113 rows and digest ae12ae4nc0u99gi6hbk1ljll7t3.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Expr.getScope/0#dispred#71058b3b/2@6f18d7fb
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Expr.getScope/0#dispred#71058b3b/2@6f18d7fb with 86 rows and digest 8f80a93temlmskovfjhb5qmsis1.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_Functions_10#join_rhs/2@eb0da2ov
[2025-06-03 11:05:00] (0s)  >>> Created relation py_Functions_10#join_rhs/2@eb0da2ov with 3 rows and digest 38d0ff3i1f83uhmkf5cagisq91b.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Function::Function.getEnclosingScope/0#dispred#2073a78d/2@2bb168ta
[2025-06-03 11:05:00] (0s)  >>> Created relation Function::Function.getEnclosingScope/0#dispred#2073a78d/2@2bb168ta with 3 rows and digest 3d8634v8d46grf7q7k4arqnaege.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_exprs_230#join_rhs/3@bbf66b9g
[2025-06-03 11:05:00] (0s)  >>> Created relation py_exprs_230#join_rhs/3@bbf66b9g with 86 rows and digest 382ce5etin938dv4ov17bq4hc88.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::BinaryExpr_.getLeft/0#dispred#7495763a/2@746179qi
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::BinaryExpr_.getLeft/0#dispred#7495763a/2@746179qi with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Stmts::AugAssign.getTarget/0#dispred#5c61a8f3/2@084e7b26 is empty, due to AstGenerated::BinaryExpr_.getLeft/0#dispred#7495763a/2@746179qi.
[2025-06-03 11:05:00] (0s) Inferred that _Stmts::AugAssign.getTarget/0#dispred#5c61a8f3_py_flow_bb_node_10#join_rhs#shared/2@fd3c26dk is empty, due to Stmts::AugAssign.getTarget/0#dispred#5c61a8f3/2@084e7b26.
[2025-06-03 11:05:00] (0s) Inferred that _#Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ecPlus#sinkBound#5#3_Flow::ControlFlowNode.__#higher_order_body/1@72d72bd7 is empty, due to _Stmts::AugAssign.getTarget/0#dispred#5c61a8f3_py_flow_bb_node_10#join_rhs#shared/2@fd3c26dk.
[2025-06-03 11:05:00] (0s) Inferred that Flow::augstore/2#23690dbf/2@cd7820jb is empty, due to _Stmts::AugAssign.getTarget/0#dispred#5c61a8f3_py_flow_bb_node_10#join_rhs#shared/2@fd3c26dk.
[2025-06-03 11:05:00] (0s) Inferred that doublyBoundedFastTC:Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec:_#Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ecPlus#sinkBound#5#3_Flow::ControlFlowNode.__#higher_order_body:#Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ecPlus#sinkBound#5#3/2@777f4428 is empty, due to _#Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ecPlus#sinkBound#5#3_Flow::ControlFlowNode.__#higher_order_body/1@72d72bd7.
[2025-06-03 11:05:00] (0s) Inferred that Flow::augstore/2#23690dbf_1#antijoin_rhs/1@67fb979t is empty, due to Flow::augstore/2#23690dbf/2@cd7820jb.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_exprs_1203#join_rhs/4@f75e08cn
[2025-06-03 11:05:00] (0s)  >>> Created relation py_exprs_1203#join_rhs/4@f75e08cn with 86 rows and digest 796728epfhuc95f6iollfk0tok1.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::AugAssign_.getOperation/0#dispred#e84e4db7/2@2f1dc9pq
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::AugAssign_.getOperation/0#dispred#e84e4db7/2@2f1dc9pq with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#8/1@e7e871th
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#8/1@e7e871th with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that __py_exprs_10#join_rhs#antijoin_rhs#8_py_flow_bb_node_10#join_rhs#shared/1@82a42ajs is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#8/1@e7e871th.
[2025-06-03 11:05:00] (0s) Inferred that Flow::TupleNode#c15d0066/1@66ae848n is empty, due to __py_exprs_10#join_rhs#antijoin_rhs#8_py_flow_bb_node_10#join_rhs#shared/1@82a42ajs.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#4/1@d21604ou
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#4/1@d21604ou with 34 rows and digest 8536dad57baff9tetc9htmcc2k7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Parameter_#667c10c8/1@eae0c5ts
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Parameter_#667c10c8/1@eae0c5ts with 34 rows and digest 8536dad57baff9tetc9htmcc2k7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_exprs_20#join_rhs/2@b6605fup
[2025-06-03 11:05:00] (0s)  >>> Created relation py_exprs_20#join_rhs/2@b6605fup with 86 rows and digest d5f7c7kjm9ivbsd9s1i2tm7ccg8.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_parameter_lists/2@1e178err with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Function_.getAnArg/0#dispred#8d75a599/2@fa89d62t is empty, due to py_parameter_lists/2@1e178err.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Function_.getArg/1#dispred#0cfe9897/3@6faeb3du is empty, due to py_parameter_lists/2@1e178err.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::ExprList_.getItem/1#dispred#2a70324c/3@876795dq
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::ExprList_.getItem/1#dispred#2a70324c/3@876795dq with 29 rows and digest b7838a8dmr2781ffcrahtte69qb.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Function::Function.getKeywordOnlyArg/1#dispred#a9482c2f/3@421581tb
[2025-06-03 11:05:00] (0s)  >>> Created relation Function::Function.getKeywordOnlyArg/1#dispred#a9482c2f/3@421581tb with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Function_.getKwarg/0#dispred#bdc4eab3/2@b97aabko
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Function_.getKwarg/0#dispred#bdc4eab3/2@b97aabko with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Function_.getVararg/0#dispred#07f0fe61/2@cdaed89u
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Function_.getVararg/0#dispred#07f0fe61/2@cdaed89u with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Function::Parameter#352debb4/1@9be23465
[2025-06-03 11:05:00] (0s)  >>> Created relation Function::Parameter#352debb4/1@9be23465 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Function::Parameter.asName/0#dispred#07e8fb90/2@2f65d4du is empty, due to Function::Parameter#352debb4/1@9be23465.
[2025-06-03 11:05:00] (0s) Inferred that Function::Parameter.getDefault/0#dispred#b09fbf4b/2@abf7deok is empty, due to Function::Parameter#352debb4/1@9be23465.
[2025-06-03 11:05:00] (0s) Inferred that Variables::LocalVariable.isParameter/0#dispred#8d4969a3/1@9deae9d5 is empty, due to Function::Parameter#352debb4/1@9be23465.
[2025-06-03 11:05:00] (0s) Inferred that project#Function::Parameter.getDefault/0#dispred#b09fbf4b/1@6321df0d is empty, due to Function::Parameter.getDefault/0#dispred#b09fbf4b/2@abf7deok.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Stmts::Stmt.getScope/0#dispred#1debab62/2@01df4doa
[2025-06-03 11:05:00] (0s)  >>> Created relation Stmts::Stmt.getScope/0#dispred#1debab62/2@01df4doa with 27 rows and digest 74b1cbtd6p3u5p340th69kavui3.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_flow_bb_node_10#join_rhs/2@45b774t7
[2025-06-03 11:05:00] (0s)  >>> Created relation py_flow_bb_node_10#join_rhs/2@45b774t7 with 109 rows and digest 260af6fcpll0em4qao052h8j0q4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#5/1@687f043h
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#5/1@687f043h with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::PlaceHolder_.getVariable/0#dispred#708ee41c/2@df5b9dun is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#5/1@687f043h.
[2025-06-03 11:05:00] (0s) Inferred that Exprs::PlaceHolder.getId/0#dispred#9822eecb/2@bdd2efr6 is empty, due to AstGenerated::PlaceHolder_.getVariable/0#dispred#708ee41c/2@df5b9dun.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Flow::NameNode#b2c17c8a/1@9fa54cf5
[2025-06-03 11:05:00] (0s)  >>> Created relation Flow::NameNode#b2c17c8a/1@9fa54cf5 with 34 rows and digest cb490b5mjcm1vd7ggcni2du2r01.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_type_parameter_lists/2@cef51fnk with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_aliases/3@db779a0g with 2 rows and digest 33ec2166tdhih62q6t4h48osjg1.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Alias_.getAsname/0#dispred#be74675b/2@ca3a16t2
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Alias_.getAsname/0#dispred#be74675b/2@ca3a16t2 with 2 rows and digest 657610faj8othm52r0p3evjlpic.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_alias_lists/2@d8caf67f with 2 rows and digest c75f062aukn8ref9q316fu1u1n9.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Import_.getAName/0#dispred#7741064f/2@1184beuh
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Import_.getAName/0#dispred#7741064f/2@1184beuh with 2 rows and digest 1d71e38tuov8t38q37bflv2pvt0.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Import_.getAName/0#dispred#7741064f_10#join_rhs/2@34bb70pf
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Import_.getAName/0#dispred#7741064f_10#join_rhs/2@34bb70pf with 2 rows and digest 4bf46c2ru92n0o18qp3l1ihinu3.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Alias_.getValue/0#dispred#8d361153/2@439aa999
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Alias_.getValue/0#dispred#8d361153/2@439aa999 with 2 rows and digest 446eb8cshsjbc28k0iv3ln14fh7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Alias_.getValue/0#dispred#8d361153_10#join_rhs/2@e52080jn
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Alias_.getValue/0#dispred#8d361153_10#join_rhs/2@e52080jn with 2 rows and digest 66bc93e4dr57gjgru72v0i3g0rb.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::ImportMember_.getModule/0#dispred#6911d1ff/2@4412afvu
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::ImportMember_.getModule/0#dispred#6911d1ff/2@4412afvu with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::ImportMember_.getModule/0#dispred#6911d1ff_10#join_rhs/2@d03c004n is empty, due to AstGenerated::ImportMember_.getModule/0#dispred#6911d1ff/2@4412afvu.
[2025-06-03 11:05:00] (0s) Inferred that Module::Module.hasFromFuture/1#dispred#cf037b23#fb/2@2cc94c7b is empty, due to AstGenerated::ImportMember_.getModule/0#dispred#6911d1ff_10#join_rhs/2@d03c004n.
[2025-06-03 11:05:00] (0s) Inferred that Flow::ImportMemberNode.getModule/1#dispred#98e51eaa#ffb/3@e3a7330e is empty, due to AstGenerated::ImportMember_.getModule/0#dispred#6911d1ff_10#join_rhs/2@d03c004n.
[2025-06-03 11:05:00] (0s) Inferred that _Exprs::Expr.getEnclosingModule/0#dispred#e365231e#bf_Module::Module.hasFromFuture/1#dispred#cf037b2__#antijoin_rhs/1@6c4b5dau is empty, due to Module::Module.hasFromFuture/1#dispred#cf037b23#fb/2@2cc94c7b.
[2025-06-03 11:05:00] (0s) Inferred that Definitions::ModuleVariable.global_variable_import/0#d33e6f1c/2@8607e5oe is empty, due to Flow::ImportMemberNode.getModule/1#dispred#98e51eaa#ffb/3@e3a7330e.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Import::Import.getASubExpression/0#dispred#d4e8ee19/2@3ae2502a
[2025-06-03 11:05:00] (0s)  >>> Created relation Import::Import.getASubExpression/0#dispred#d4e8ee19/2@3ae2502a with 4 rows and digest 191c21c6293npptqi5au3advkpe.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82/2@6a07768l
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82/2@6a07768l with 2 rows and digest 007c19uj2l9l5ad3e2q9fa1em6b.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_pattern_lists/3@c2339etb with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that py_pattern_lists_201#join_rhs/3@1f47b0lk is empty, due to py_pattern_lists/3@c2339etb.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_stmt_lists/3@bef968k2 with 5 rows and digest fcb17bs8q34v7jsp7b2lhqi32g4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_stmt_lists_021#join_rhs/3@e85cc0kt
[2025-06-03 11:05:00] (0s)  >>> Created relation py_stmt_lists_021#join_rhs/3@e85cc0kt with 5 rows and digest 7277b4b12qcg36l0u5riskta4ge.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Stmts::ExceptionHandler#2d12ceec/1@66ebb9rh
[2025-06-03 11:05:00] (0s)  >>> Created relation Stmts::ExceptionHandler#2d12ceec/1@66ebb9rh with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Stmts::ExceptGroupStmt#d71d1244/1@0312883j is empty, due to Stmts::ExceptionHandler#2d12ceec/1@66ebb9rh.
[2025-06-03 11:05:00] (0s) Inferred that Stmts::ExceptStmt#b7ee7437/1@741f2ef0 is empty, due to Stmts::ExceptionHandler#2d12ceec/1@66ebb9rh.
[2025-06-03 11:05:00] (0s) Inferred that Stmts::ExceptGroupStmt.getName/0#dispred#6e8e2398/2@ced8ecpc is empty, due to Stmts::ExceptGroupStmt#d71d1244/1@0312883j.
[2025-06-03 11:05:00] (0s) Inferred that Stmts::ExceptStmt.getName/0#dispred#13f5307e/2@c40eb561 is empty, due to Stmts::ExceptStmt#b7ee7437/1@741f2ef0.
[2025-06-03 11:05:00] (0s) Inferred that Exceptions::ExceptGroupFlowNode.getName/0#dispred#c709eb39/2@b0af12tk is empty, due to Stmts::ExceptGroupStmt.getName/0#dispred#6e8e2398/2@ced8ecpc.
[2025-06-03 11:05:00] (0s) Inferred that Exceptions::ExceptFlowNode.getName/0#dispred#eedc34d6/2@ac983f07 is empty, due to Stmts::ExceptStmt.getName/0#dispred#13f5307e/2@c40eb561.
[2025-06-03 11:05:00] (0s) Inferred that SsaDefinitions::SsaSource::exception_group_capture/2#d53f492d/2@ba1f39e8 is empty, due to Exceptions::ExceptGroupFlowNode.getName/0#dispred#c709eb39/2@b0af12tk.
[2025-06-03 11:05:00] (0s) Inferred that SsaDefinitions::SsaSource::exception_capture/2#fd0779df/2@5691e8hg is empty, due to Exceptions::ExceptFlowNode.getName/0#dispred#eedc34d6/2@ac983f07.
[2025-06-03 11:05:00] (0s) Inferred that cached_SsaDefinitions::SsaSource::exception_group_capture/2#d53f492d/2@790fdeja is empty, due to SsaDefinitions::SsaSource::exception_group_capture/2#d53f492d/2@ba1f39e8.
[2025-06-03 11:05:00] (0s) Inferred that cached_SsaDefinitions::SsaSource::exception_capture/2#fd0779df/2@793700dj is empty, due to SsaDefinitions::SsaSource::exception_capture/2#fd0779df/2@5691e8hg.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::ExprList_.getAnItem/0#dispred#09559f69/2@76feea9p
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::ExprList_.getAnItem/0#dispred#09559f69/2@76feea9p with 29 rows and digest f0b83499mf25o9eoj150dt03fec.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_stmt_lists_120#join_rhs/3@0e183epv
[2025-06-03 11:05:00] (0s)  >>> Created relation py_stmt_lists_120#join_rhs/3@0e183epv with 5 rows and digest a531c37k1q4gni2aejq09dojrm8.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::ExceptGroupStmt_.getType/0#eb73b264/2@1a11ec01
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::ExceptGroupStmt_.getType/0#eb73b264/2@1a11ec01 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Tuple_.getElts/0#dispred#df30ffae/2@4112754c
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Tuple_.getElts/0#dispred#df30ffae/2@4112754c with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Tuple_.getAnElt/0#dispred#dfceea61/2@9b2dda9q is empty, due to AstGenerated::Tuple_.getElts/0#dispred#df30ffae/2@4112754c.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Tuple_.getElt/1#dispred#ba261ee0/3@09213fgb is empty, due to AstGenerated::Tuple_.getElts/0#dispred#df30ffae/2@4112754c.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Tuple_.getAnElt/0#dispred#dfceea61_10#join_rhs/2@515ef3mm is empty, due to AstGenerated::Tuple_.getAnElt/0#dispred#dfceea61/2@9b2dda9q.
[2025-06-03 11:05:00] (0s) Inferred that project#AstGenerated::Tuple_.getElt/1#dispred#ba261ee0/1@b0f9d02h is empty, due to AstGenerated::Tuple_.getElt/1#dispred#ba261ee0/3@09213fgb.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Tuple_.getElt/1#dispred#ba261ee0_201#join_rhs/3@552d09pr is empty, due to AstGenerated::Tuple_.getElt/1#dispred#ba261ee0/3@09213fgb.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Tuple_.getElt/1#dispred#ba261ee0_120#join_rhs/3@9a57558l is empty, due to AstGenerated::Tuple_.getElt/1#dispred#ba261ee0/3@09213fgb.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::ExceptStmt_.getType/0#552fde1b/2@9e1cc6u1
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::ExceptStmt_.getType/0#552fde1b/2@9e1cc6u1 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Function_.getBody/0#dispred#6a61cbfb/2@db9bc52p
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Function_.getBody/0#dispred#6a61cbfb/2@db9bc52p with 3 rows and digest 5b407b379ao57h8vptod6s1eqe4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Function_.getBody/0#dispred#6a61cbfb_10#join_rhs/2@3347b8uo
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Function_.getBody/0#dispred#6a61cbfb_10#join_rhs/2@3347b8uo with 3 rows and digest 196517p9fe1rfhostv9bqkejbfe.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_stmts_20#join_rhs/2@e780a92c
[2025-06-03 11:05:00] (0s)  >>> Created relation py_stmts_20#join_rhs/2@e780a92c with 27 rows and digest 980254l6dnr3k6rtc8v0p9v83a9.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::If_.getBody/0#dispred#2a3ec055/2@48f1ab4m
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::If_.getBody/0#dispred#2a3ec055/2@48f1ab4m with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::If_.getBody/0#dispred#2a3ec055_10#join_rhs/2@4b13e6i4 is empty, due to AstGenerated::If_.getBody/0#dispred#2a3ec055/2@48f1ab4m.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_dict_items_20#join_rhs/2@8d60b0p4
[2025-06-03 11:05:00] (0s)  >>> Created relation py_dict_items_20#join_rhs/2@8d60b0p4 with 11 rows and digest d912ba4g1chohtkb29fq9l41fi3.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_dict_item_lists/2@cc0300gb with 4 rows and digest 5571a2n8f9uhmhfjl2rfm0rqsf5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Call_.getNamedArgs/0#dispred#1e143173/2@e37cfbta
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Call_.getNamedArgs/0#dispred#1e143173/2@e37cfbta with 2 rows and digest 1119fallil22p186doqu1m554e5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Call_.getNamedArgs/0#dispred#1e143173_10#join_rhs/2@2eef1ana
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Call_.getNamedArgs/0#dispred#1e143173_10#join_rhs/2@2eef1ana with 2 rows and digest 45ff1a5efdd1av8cr1mlh5dc3ib.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#16/1@a85b3cn4
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#16/1@a85b3cn4 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#15/1@3c36657n
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#15/1@3c36657n with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Function::Lambda.getArgs/0#dispred#6c207427/2@4643cbgc is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#15/1@3c36657n.
[2025-06-03 11:05:00] (0s) Inferred that Function::Lambda.getASubExpression/0#dispred#a8882ebd/2@3a0bd610 is empty, due to Function::Lambda.getArgs/0#dispred#6c207427/2@4643cbgc.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#14/1@a95d27eh
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#14/1@a95d27eh with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#13/1@c751f579
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#13/1@c751f579 with 3 rows and digest df87f4paa7bbtbt4p6si9mi4jna.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#12/1@260003qk
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#12/1@260003qk with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#9/1@482d23k2
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#9/1@482d23k2 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Comprehensions::ListComp.getFunction/0#dispred#635f9f84/2@22ec95mf is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#9/1@482d23k2.
[2025-06-03 11:05:00] (0s) Inferred that Comprehensions::ListComp.getFunction/0#dispred#635f9f84_10#join_rhs/2@59538du3 is empty, due to Comprehensions::ListComp.getFunction/0#dispred#635f9f84/2@22ec95mf.
[2025-06-03 11:05:00] (0s) Inferred that Comprehensions::Comp.getNthInnerLoop/1#4beb1f8c/3@3a89f4ds is empty, due to Comprehensions::ListComp.getFunction/0#dispred#635f9f84_10#join_rhs/2@59538du3.
[2025-06-03 11:05:00] (0s) Inferred that project#Comprehensions::Comp.getNthInnerLoop/1#4beb1f8c/2@1b9331pm is empty, due to Comprehensions::Comp.getNthInnerLoop/1#4beb1f8c/3@3a89f4ds.
[2025-06-03 11:05:00] (0s) Inferred that project#Comprehensions::Comp.getNthInnerLoop/1#4beb1f8c_10#join_rhs/2@29c265cc is empty, due to project#Comprehensions::Comp.getNthInnerLoop/1#4beb1f8c/2@1b9331pm.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::For_.getBody/0#dispred#166335e5/2@34a3647m
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::For_.getBody/0#dispred#166335e5/2@34a3647m with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::For_.getAStmt/0#dispred#cec2a04c/2@d1941b5c is empty, due to AstGenerated::For_.getBody/0#dispred#166335e5/2@34a3647m.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::For_.getAStmt/0#dispred#cec2a04c_10#join_rhs/2@104a79re is empty, due to AstGenerated::For_.getAStmt/0#dispred#cec2a04c/2@d1941b5c.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_stmts_032#join_rhs/3@ef43dca7
[2025-06-03 11:05:00] (0s)  >>> Created relation py_stmts_032#join_rhs/3@ef43dca7 with 27 rows and digest d8eb624n0m6d8k4j5djptjhefa1.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::ExprStmt_.getValue/0#dispred#5b34bb96/2@1e2508an
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::ExprStmt_.getValue/0#dispred#5b34bb96/2@1e2508an with 2 rows and digest 6bb698apbhdda25ldqcla53ltgb.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Keywords::KeyValuePair.getValue/0#dispred#93775546/2@adc4906v
[2025-06-03 11:05:00] (0s)  >>> Created relation Keywords::KeyValuePair.getValue/0#dispred#93775546/2@adc4906v with 6 rows and digest 815bafanecdg7ui447idc1hid5c.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Keywords::DictUnpacking.getValue/0#dispred#f8e7301b/2@11db1cso
[2025-06-03 11:05:00] (0s)  >>> Created relation Keywords::DictUnpacking.getValue/0#dispred#f8e7301b/2@11db1cso with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Keywords::DictDisplayItem.getValue/0#dispred#307ba8c4/2@aaef9f57
[2025-06-03 11:05:00] (0s)  >>> Created relation Keywords::DictDisplayItem.getValue/0#dispred#307ba8c4/2@aaef9f57 with 6 rows and digest 815bafanecdg7ui447idc1hid5c.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Keywords::Keyword.getValue/0#dispred#bb5e46e3/2@9779f54e
[2025-06-03 11:05:00] (0s)  >>> Created relation Keywords::Keyword.getValue/0#dispred#bb5e46e3/2@9779f54e with 5 rows and digest 4f5c8f0a1fsrul08uto65a62dg7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Keywords::DictUnpackingOrKeyword#749f6a53/1@d3d2841c
[2025-06-03 11:05:00] (0s)  >>> Created relation Keywords::DictUnpackingOrKeyword#749f6a53/1@d3d2841c with 5 rows and digest bd1aceqipsp3i8v197rjl84k64c.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb/3@ba700brb
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb/3@ba700brb with 5 rows and digest ad988esgnfjt0obss9bmfp4ura4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs/3@06c0e34c
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs/3@06c0e34c with 5 rows and digest 39b94chfg17qcqbka7kbsdhjd59.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Compare_.getComparators/0#dispred#50661f3c/2@1b3416kd
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Compare_.getComparators/0#dispred#50661f3c/2@1b3416kd with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Compare_.getComparator/1#dispred#210faf25#ffb/3@c696dcuo is empty, due to AstGenerated::Compare_.getComparators/0#dispred#50661f3c/2@1b3416kd.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Compare_.getComparator/1#dispred#210faf25#bff/3@a05249h6 is empty, due to AstGenerated::Compare_.getComparators/0#dispred#50661f3c/2@1b3416kd.
[2025-06-03 11:05:00] (0s) Inferred that Operations::Compare.compares/3#dispred#57048f38#fbff/4@71a430or is empty, due to AstGenerated::Compare_.getComparator/1#dispred#210faf25#bff/3@a05249h6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::KeyValuePair_.getKey/0#dispred#f9941800/2@79ba2del
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::KeyValuePair_.getKey/0#dispred#f9941800/2@79ba2del with 6 rows and digest 40fb13skr9hm8c99tnpk0dupba9.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#7/1@f0f822vn
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#7/1@f0f822vn with 2 rows and digest 0e9afek00frhinbouj34v4o09s3.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_dict_item_lists_10#join_rhs/2@70e6f16k
[2025-06-03 11:05:00] (0s)  >>> Created relation py_dict_item_lists_10#join_rhs/2@70e6f16k with 4 rows and digest 9b49f0l3ppb3p64hdcqdjt4qh73.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Dict_.getAnItem/0#dispred#c0c5c5fb/2@2e9526ji
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Dict_.getAnItem/0#dispred#c0c5c5fb/2@2e9526ji with 6 rows and digest aee2f2k9beuqsv6f90pftrs3uv9.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Keywords::DictUnpackingOrKeyword.getValue/0#dispred#f5fe382f/2@7c4cfaeh
[2025-06-03 11:05:00] (0s)  >>> Created relation Keywords::DictUnpackingOrKeyword.getValue/0#dispred#f5fe382f/2@7c4cfaeh with 5 rows and digest 4f5c8f0a1fsrul08uto65a62dg7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Class::ClassExpr.getAKeyword/0#dispred#a84ede11/2@f990e6sg
[2025-06-03 11:05:00] (0s)  >>> Created relation Class::ClassExpr.getAKeyword/0#dispred#a84ede11/2@f990e6sg with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Starred_.getValue/0#dispred#3c0230c4/2@390813gl
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Starred_.getValue/0#dispred#3c0230c4/2@390813gl with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Flow::StarredNode.getValue/0#dispred#77106d16/2@ecb8631a is empty, due to AstGenerated::Starred_.getValue/0#dispred#3c0230c4/2@390813gl.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::ClassExpr_.getABase/0#dispred#7af4e693/2@d775edts
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::ClassExpr_.getABase/0#dispred#7af4e693/2@d775edts with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Class::ClassExpr.getASubExpression/0#dispred#93e12f13/2@0a58c9kk
[2025-06-03 11:05:00] (0s)  >>> Created relation Class::ClassExpr.getASubExpression/0#dispred#93e12f13/2@0a58c9kk with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_arguments/2@2b937e7s with 3 rows and digest 55ec7da99osvpjk5g7q17c0o6td.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Arguments_.getDefaults/0#dispred#583dac8a/2@e06885ht
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Arguments_.getDefaults/0#dispred#583dac8a/2@e06885ht with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Arguments_.getKwDefaults/0#dispred#d6eb64ab/2@f69dbd3u
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Arguments_.getKwDefaults/0#dispred#d6eb64ab/2@f69dbd3u with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Function::Arguments.getASubExpression/0#dispred#f1c80c65/2@85fa0ajq
[2025-06-03 11:05:00] (0s)  >>> Created relation Function::Arguments.getASubExpression/0#dispred#f1c80c65/2@85fa0ajq with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_arguments_10#join_rhs/2@9dbccce2
[2025-06-03 11:05:00] (0s)  >>> Created relation py_arguments_10#join_rhs/2@9dbccce2 with 3 rows and digest ebc91b8am2ng7i8bk0kvseq8u30.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Function::FunctionExpr.getArgs/0#dispred#85f47c51/2@26e76bfu
[2025-06-03 11:05:00] (0s)  >>> Created relation Function::FunctionExpr.getArgs/0#dispred#85f47c51/2@26e76bfu with 3 rows and digest ebc91b8am2ng7i8bk0kvseq8u30.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Function::FunctionExpr.getASubExpression/0#dispred#fa96f232/2@81dc0ev7
[2025-06-03 11:05:00] (0s)  >>> Created relation Function::FunctionExpr.getASubExpression/0#dispred#fa96f232/2@81dc0ev7 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::AssignExpr_.getTarget/0#dispred#56e0edd1/2@3d0138b8
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::AssignExpr_.getTarget/0#dispred#56e0edd1/2@3d0138b8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::AssignExpr_.getValue/0#dispred#dd4f3283/2@887262id
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::AssignExpr_.getValue/0#dispred#dd4f3283/2@887262id with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Yield_.getValue/0#dispred#b8040756/2@f05607r9
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Yield_.getValue/0#dispred#b8040756/2@f05607r9 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Comprehensions::SetComp.getASubExpression/0#dispred#a437fb1f/2@4cdad73k
[2025-06-03 11:05:00] (0s)  >>> Created relation Comprehensions::SetComp.getASubExpression/0#dispred#a437fb1f/2@4cdad73k with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Comprehensions::ListComp.getIterable/0#dispred#57173e62/2@16a42etf
[2025-06-03 11:05:00] (0s)  >>> Created relation Comprehensions::ListComp.getIterable/0#dispred#57173e62/2@16a42etf with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Comprehensions::GeneratorExp.getASubExpression/0#dispred#daf0ddfb/2@1416e42j
[2025-06-03 11:05:00] (0s)  >>> Created relation Comprehensions::GeneratorExp.getASubExpression/0#dispred#daf0ddfb/2@1416e42j with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Comprehensions::DictComp.getASubExpression/0#dispred#bda1a79a/2@bb81efm9
[2025-06-03 11:05:00] (0s)  >>> Created relation Comprehensions::DictComp.getASubExpression/0#dispred#bda1a79a/2@bb81efm9 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Compare_.getLeft/0#dispred#02e6fc6e/2@401a4bvm
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Compare_.getLeft/0#dispred#02e6fc6e/2@401a4bvm with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Call_.getFunc/0#dispred#9f0e5cfd/2@3f4718o2
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Call_.getFunc/0#dispred#9f0e5cfd/2@3f4718o2 with 7 rows and digest 957a05chnve6pbje71bi4fhlho6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Attribute.getObject/0#dispred#c761f38d/2@d92a98ne
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Attribute.getObject/0#dispred#c761f38d/2@d92a98ne with 4 rows and digest 0b5128p5k8jan3svgua8mgrcqi1.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::List_.getElts/0#dispred#697e305c/2@fc9c7br4
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::List_.getElts/0#dispred#697e305c/2@fc9c7br4 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::List_.getAnElt/0#dispred#fd62fa74/2@939b1dej is empty, due to AstGenerated::List_.getElts/0#dispred#697e305c/2@fc9c7br4.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::List_.getElt/1#dispred#a189dc04/3@c356fa2h is empty, due to AstGenerated::List_.getElts/0#dispred#697e305c/2@fc9c7br4.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::List_.getAnElt/0#dispred#fd62fa74_10#join_rhs/2@c8394963 is empty, due to AstGenerated::List_.getAnElt/0#dispred#fd62fa74/2@939b1dej.
[2025-06-03 11:05:00] (0s) Inferred that project#AstGenerated::List_.getElt/1#dispred#a189dc04/1@768cc0sg is empty, due to AstGenerated::List_.getElt/1#dispred#a189dc04/3@c356fa2h.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::List_.getElt/1#dispred#a189dc04_201#join_rhs/3@20bb3drf is empty, due to AstGenerated::List_.getElt/1#dispred#a189dc04/3@c356fa2h.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::List_.getElt/1#dispred#a189dc04_120#join_rhs/3@5cb75bps is empty, due to AstGenerated::List_.getElt/1#dispred#a189dc04/3@c356fa2h.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Call_.getPositionalArgs/0#dispred#2e9514f5/2@82bcd5pp
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Call_.getPositionalArgs/0#dispred#2e9514f5/2@82bcd5pp with 5 rows and digest 77a39ei3uu117lvemvms1ecpnr6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Call_.getAPositionalArg/0#dispred#b66be902/2@1be1d3hs
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Call_.getAPositionalArg/0#dispred#b66be902/2@1be1d3hs with 5 rows and digest 9f2bedpjb4df9efjtcove0pb8a1.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#17/1@1879fd7t
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#17/1@1879fd7t with 33 rows and digest 42d71aj2v1o215fsrrrc7g2n7p7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#11/1@64a70adk
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#11/1@64a70adk with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Class::ClassDef#83097204/1@6686b2aa is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#11/1@64a70adk.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#1/1@9af1048t
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#1/1@9af1048t with 7 rows and digest 670d48nlaeibjp396u2rtkje7t8.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Expr_not_Exprs::Call_Class::ClassExpr_Exprs::Dict_Comprehensions::DictComp_Function::FunctionExpr_Comprehensions::GeneratorExp_Function::Lambda_Comprehensions::ListComp_Comprehensions::SetComp_Exprs::StringLiteral#3ae9c825/1@cfe2cct2
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Expr_not_Exprs::Call_Class::ClassExpr_Exprs::Dict_Comprehensions::DictComp_Function::FunctionExpr_Comprehensions::GeneratorExp_Function::Lambda_Comprehensions::ListComp_Comprehensions::SetComp_Exprs::StringLiteral#3ae9c825/1@cfe2cct2 with 41 rows and digest 0e2294mrq7ofosmf1iakdp881ka.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Assign_.getATarget/0#dispred#8e860934/2@66f4634i
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Assign_.getATarget/0#dispred#8e860934/2@66f4634i with 21 rows and digest bf93863b5rdtnfa6mkm73p9ubt7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::AnnAssign_.getTarget/0#dispred#ac7debc8/2@f373f3nh
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::AnnAssign_.getTarget/0#dispred#ac7debc8/2@f373f3nh with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::AnnAssign_.getValue/0#dispred#436806a5/2@62c0b7r6
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::AnnAssign_.getValue/0#dispred#436806a5/2@62c0b7r6 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::With_.getOptionalVars/0#dispred#ed12867c/2@ded75ebf
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::With_.getOptionalVars/0#dispred#ed12867c/2@ded75ebf with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that SsaDefinitions::SsaSource::with_definition/2#e1888ceb/2@50ae490u is empty, due to AstGenerated::With_.getOptionalVars/0#dispred#ed12867c/2@ded75ebf.
[2025-06-03 11:05:00] (0s) Inferred that cached_SsaDefinitions::SsaSource::with_definition/2#e1888ceb/2@e0bd2902 is empty, due to SsaDefinitions::SsaSource::with_definition/2#e1888ceb/2@50ae490u.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::ImportStar_.getModule/0#dispred#f7687244/2@4fac27bk
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::ImportStar_.getModule/0#dispred#f7687244/2@4fac27bk with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Import::ImportStar.getModuleExpr/0#dispred#8ab9ce73/2@ab8dbec8 is empty, due to AstGenerated::ImportStar_.getModule/0#dispred#f7687244/2@4fac27bk.
[2025-06-03 11:05:00] (0s) Inferred that cached_Import::ImportStar.getModuleExpr/0#dispred#8ab9ce73/2@9212cahk is empty, due to Import::ImportStar.getModuleExpr/0#dispred#8ab9ce73/2@ab8dbec8.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::If_.getTest/0#dispred#c582b403/2@4ac151mr
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::If_.getTest/0#dispred#c582b403/2@4ac151mr with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::For_.getTarget/0#dispred#740b9eed/2@119c7901
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::For_.getTarget/0#dispred#740b9eed/2@119c7901 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Assign_.getValue/0#dispred#53d00b56/2@2d1650hr
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Assign_.getValue/0#dispred#53d00b56/2@2d1650hr with 21 rows and digest ce4c98r7t7h495pjolsopbljla1.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstExtended::AstNode.getScope/0#dispred#9b09b826/2@e6c71f9s
[2025-06-03 11:05:00] (0s)  >>> Created relation AstExtended::AstNode.getScope/0#dispred#9b09b826/2@e6c71f9s with 127 rows and digest 0ff003utmn1r571p3qqg9bkklc7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _AstGenerated::Scope_#cc08b056_py_flow_bb_node#antijoin_rhs/2@ef4d35dd
[2025-06-03 11:05:00] (0s)  >>> Created relation _AstGenerated::Scope_#cc08b056_py_flow_bb_node#antijoin_rhs/2@ef4d35dd with 10 rows and digest 56177887q0d9hg17ioejnmtvl27.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Flow::ControlFlowNode.getScope/0#dispred#b061daac/2@0ac6c94f
[2025-06-03 11:05:00] (0s)  >>> Created relation Flow::ControlFlowNode.getScope/0#dispred#b061daac/2@0ac6c94f with 109 rows and digest 24903dlh7adrl0pf81ntttrn0rd.
[2025-06-03 11:05:00] (0s)  >>> Created relation variable/3@5e1d40kq with 33 rows and digest 6a3b4b14bgnblis0klnmukgbrr8.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_variables/2@62cd450b with 34 rows and digest 05c4e5cs4vkc2t3m8v34ndch22c.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Variables::Variable.getId/0#dispred#33796d42/2@7155f3jh
[2025-06-03 11:05:00] (0s)  >>> Created relation Variables::Variable.getId/0#dispred#33796d42/2@7155f3jh with 31 rows and digest 03d22et53gpp5eacteksvgo0583.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Variables::Variable.getId/0#dispred#33796d42_10#join_rhs/2@e85cdfnl
[2025-06-03 11:05:00] (0s)  >>> Created relation Variables::Variable.getId/0#dispred#33796d42_10#join_rhs/2@e85cdfnl with 31 rows and digest 9d0dd7qegmhuolvqot9s7dtvvac.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::name_consts/2#5b93d161/2@26923fa8
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::name_consts/2#5b93d161/2@26923fa8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Exprs::name_consts/2#5b93d161_10#join_rhs/2@ec1e5cqq is empty, due to Exprs::name_consts/2#5b93d161/2@26923fa8.
[2025-06-03 11:05:00] (0s) Inferred that Exprs::NameConstant#42a68058/1@066eb7n4 is empty, due to Exprs::name_consts/2#5b93d161_10#join_rhs/2@ec1e5cqq.
[2025-06-03 11:05:00] (0s) Inferred that _Exprs::Name.uses/1#dispred#b73448f6_10#join_rhs_Exprs::NameConstant#42a68058_variable#antijoin_rhs/1@8a6161rb is empty, due to Exprs::NameConstant#42a68058/1@066eb7n4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Name_.getVariable/0#dispred#d8133d45/2@e04d1f44
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Name_.getVariable/0#dispred#d8133d45/2@e04d1f44 with 34 rows and digest fd623eeq57q0ir4cbap3k4ne50e.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.SsaSourceVariable#35012962/1@a502d3nu
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::SsaSourceVariable.SsaSourceVariable#35012962/1@a502d3nu with 33 rows and digest 479e17hf9q88f3k4s5ihvtv3ql5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate project#py_flow_bb_node/1@fad6886l
[2025-06-03 11:05:00] (0s)  >>> Created relation project#py_flow_bb_node/1@fad6886l with 5 rows and digest ca89e5otauikcode2p0lu2dar2d.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Flow::BasicBlock.getNode/1#dispred#4a129537/3@dcd623tr
[2025-06-03 11:05:00] (0s)  >>> Created relation Flow::BasicBlock.getNode/1#dispred#4a129537/3@dcd623tr with 109 rows and digest 4b155902lobudmkmg2osjk3t99a.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate variable_02#join_rhs/2@f27b5amb
[2025-06-03 11:05:00] (0s)  >>> Created relation variable_02#join_rhs/2@f27b5amb with 33 rows and digest 293cafs1297qroars0ihh1674b8.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::SpecialSsaSourceVariable#59f2e69e/1@0fd25fha
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::SpecialSsaSourceVariable#59f2e69e/1@0fd25fha with 2 rows and digest 1222c03gt4ra4un7vm7hd6sjlr4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52/2@3d8c107i
[2025-06-03 11:05:00] (0s)  >>> Created relation Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52/2@3d8c107i with 3 rows and digest 3d8634v8d46grf7q7k4arqnaege.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate boundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:AstGenerated::Scope_#cc08b056/2@b3c2bcsh = HOP boundedFastTC(3,5)
[2025-06-03 11:05:00] (0s)  >>> Relation boundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:AstGenerated::Scope_#cc08b056: 3 rows using 0 MB
[2025-06-03 11:05:00] (0s)  >>> Created relation boundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:AstGenerated::Scope_#cc08b056/2@b3c2bcsh with 3 rows and digest 3d8634v8d46grf7q7k4arqnaege.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _AstGenerated::Scope_#cc08b056_boundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:AstGe__#antijoin_rhs/1@6c8a07la
[2025-06-03 11:05:00] (0s)  >>> Created relation _AstGenerated::Scope_#cc08b056_boundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:AstGe__#antijoin_rhs/1@6c8a07la with 3 rows and digest 240b48hk108k08n1k98fcki5sf5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate ImportTime::ImportTimeScope#0b3564f8/1@75de987k
[2025-06-03 11:05:00] (0s)  >>> Created relation ImportTime::ImportTimeScope#0b3564f8/1@75de987k with 2 rows and digest 1758e8s2mdeq4m767kljah7j4r6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Variables::Variable.getScope/0#dispred#e3b1c704/2@daec9568
[2025-06-03 11:05:00] (0s)  >>> Created relation Variables::Variable.getScope/0#dispred#e3b1c704/2@daec9568 with 31 rows and digest e654095ecj30bfsnva68n8fppf7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Variables::Variable.getScope/0#dispred#e3b1c704_10#join_rhs/2@8d77a92p
[2025-06-03 11:05:00] (0s)  >>> Created relation Variables::Variable.getScope/0#dispred#e3b1c704_10#join_rhs/2@8d77a92p with 31 rows and digest e124fckv5ljrscjj2eo5uuha1s9.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Variables::LocalVariable#4f360e4a/1@a1d768r7
[2025-06-03 11:05:00] (0s)  >>> Created relation Variables::LocalVariable#4f360e4a/1@a1d768r7 with 3 rows and digest 3ebb01o8jllfm4dvo2ng24ieovb.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_expr_contexts/3@3945b8md with 38 rows and digest d112db55tn6b52hr0rka2sqkm42.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_expr_contexts_12#join_rhs/2@145e04os
[2025-06-03 11:05:00] (0s)  >>> Created relation py_expr_contexts_12#join_rhs/2@145e04os with 38 rows and digest 022b2etmrf2u2o5rc2lmftnni8f.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Name.isDefinition/0#dispred#35fcd409/1@6959a59b
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Name.isDefinition/0#dispred#35fcd409/1@6959a59b with 23 rows and digest 7375ednbtsh28j6v7u8l6gc46s5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Name.defines/1#dispred#729b8d19/2@946b571l
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Name.defines/1#dispred#729b8d19/2@946b571l with 23 rows and digest a9792bqfcbqs90hj2eot5ed2tf4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Name.defines/1#dispred#729b8d19_10#join_rhs/2@e4338b50
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Name.defines/1#dispred#729b8d19_10#join_rhs/2@e4338b50 with 23 rows and digest a7ceacugo6322romamimfv5h9v2.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::NonLocalVariable#8768a412/1@9d3a53sp
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::NonLocalVariable#8768a412/1@9d3a53sp with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20/2@b0b438rr is empty, due to Definitions::NonLocalVariable#8768a412/1@9d3a53sp.
[2025-06-03 11:05:00] (0s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#4#3/1@b45264ar is empty, due to Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20/2@b0b438rr.
[2025-06-03 11:05:00] (0s) Inferred that Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20_10#join_rhs/2@01f3424g is empty, due to Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20/2@b0b438rr.
[2025-06-03 11:05:00] (0s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#bounded#2/2@f91f6d2l is empty, due to #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#4#3/1@b45264ar.
[2025-06-03 11:05:00] (0s) Inferred that _Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20_10#join_rhs__Flow::CallNode#c5d1dc__#shared/2@e160d2qb is empty, due to Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20_10#join_rhs/2@01f3424g.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::FunctionLocalVariable#df373784/1@013c44lh
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::FunctionLocalVariable#df373784/1@013c44lh with 3 rows and digest 3ebb01o8jllfm4dvo2ng24ieovb.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Variables::GlobalVariable#28045801/1@3c116fdi
[2025-06-03 11:05:00] (0s)  >>> Created relation Variables::GlobalVariable#28045801/1@3c116fdi with 28 rows and digest 8f0351d9gk2ro4t53dt2g4rinl0.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate project#Exprs::Name.defines/1#dispred#729b8d19/1@52051fpj
[2025-06-03 11:05:00] (0s)  >>> Created relation project#Exprs::Name.defines/1#dispred#729b8d19/1@52051fpj with 23 rows and digest 5b9910vfo0ia4l4duaemepsshd1.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _Stmts::Stmt.getScope/0#dispred#1debab62_Variables::Variable.getScope/0#dispred#e3b1c704_10#join_rhs__#shared/1@d740d35m
[2025-06-03 11:05:00] (0s)  >>> Created relation _Stmts::Stmt.getScope/0#dispred#1debab62_Variables::Variable.getScope/0#dispred#e3b1c704_10#join_rhs__#shared/1@d740d35m with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _Definitions::SsaSourceVariable.SsaSourceVariable#35012962_Variables::GlobalVariable#28045801#shared/1@82c018pj
[2025-06-03 11:05:00] (0s)  >>> Created relation _Definitions::SsaSourceVariable.SsaSourceVariable#35012962_Variables::GlobalVariable#28045801#shared/1@82c018pj with 28 rows and digest 8f0351d9gk2ro4t53dt2g4rinl0.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::ModuleVariable#27049d81/1@243bffti
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::ModuleVariable#27049d81/1@243bffti with 24 rows and digest 572bc7ufe66b905pfd7lohim0s4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Name.uses/1#dispred#b73448f6/2@4b69b9i6
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Name.uses/1#dispred#b73448f6/2@4b69b9i6 with 11 rows and digest a314df2l7mc0gr5glgvmpkriko5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Name.getId/0#dispred#4fa5460e/2@431259f7
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Name.getId/0#dispred#4fa5460e/2@431259f7 with 34 rows and digest 52e800lsal1tbvs2vop4h4jq09f.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs/1@eb95435e
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs/1@eb95435e with 4 rows and digest ba34a52a2met9koludkvhecmoh0.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Flow::AttrNode#0f9cc644/1@3a2488v2
[2025-06-03 11:05:00] (0s)  >>> Created relation Flow::AttrNode#0f9cc644/1@3a2488v2 with 4 rows and digest 685aab9poajmo0ds0a45e8b33o5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3/1@471cf87v
[2025-06-03 11:05:00] (0s)  >>> Created relation Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3/1@471cf87v with 15 rows and digest 9e3c9f90ovn056tt122v6kqo33d.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Flow::NameNode.defines/1#dispred#000af72d/2@b5969db9
[2025-06-03 11:05:00] (0s)  >>> Created relation Flow::NameNode.defines/1#dispred#000af72d/2@b5969db9 with 23 rows and digest bd3286dfj3qh20vtje7acihvm87.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _Definitions::ModuleVariable#27049d81_Flow::NameNode.defines/1#dispred#000af72d#shared/2@b4b667at
[2025-06-03 11:05:00] (0s)  >>> Created relation _Definitions::ModuleVariable#27049d81_Flow::NameNode.defines/1#dispred#000af72d#shared/2@b4b667at with 20 rows and digest 232aa2ipvq0fj782t8e33v261m6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Variables::Variable.getScope/0#dispred#e3b1c704____#antijoin_rhs#1/2@24c29c0i
[2025-06-03 11:05:00] (0s)  >>> Created relation _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Variables::Variable.getScope/0#dispred#e3b1c704____#antijoin_rhs#1/2@24c29c0i with 20 rows and digest 232aa2ipvq0fj782t8e33v261m6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Flow::CallNode#c5d1dc47/1@5509a280
[2025-06-03 11:05:00] (0s)  >>> Created relation Flow::CallNode#c5d1dc47/1@5509a280 with 7 rows and digest 8945e8kh4ioc2i2vtghdlf973q9.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_scope_flow/3@3a9c65tj with 20 rows and digest 90e0df6daqhnsk6a6al73s0bvo4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_scope_flow_201#join_rhs/3@f0a7617n
[2025-06-03 11:05:00] (0s)  >>> Created relation py_scope_flow_201#join_rhs/3@f0a7617n with 20 rows and digest 5a6a8ei9h5apbh0svteia3amvs7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Scope::Scope.getANormalExit/0#dispred#1288c0a8/2@95b0a3la
[2025-06-03 11:05:00] (0s)  >>> Created relation Scope::Scope.getANormalExit/0#dispred#1288c0a8/2@95b0a3la with 5 rows and digest 53f6ebf3l2qqhoue30usgo308ma.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _Variables::Variable.getId/0#dispred#33796d42__Definitions::SsaSourceVariable.SsaSourceVariable#3501__#shared/1@477f3733
[2025-06-03 11:05:00] (0s)  >>> Created relation _Variables::Variable.getId/0#dispred#33796d42__Definitions::SsaSourceVariable.SsaSourceVariable#3501__#shared/1@477f3733 with 4 rows and digest 576d6ac9p48jr50eqmcsoaqi4r2.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs/2@d7176491
[2025-06-03 11:05:00] (0s)  >>> Created relation Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs/2@d7176491 with 27 rows and digest 27edfed1gblmoemt32q4tglq6t9.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs_Variables::Variable.getScope/0#dispred#e3b1c704__#antijoin_rhs/1@ea1035ad
[2025-06-03 11:05:00] (0s)  >>> Created relation _Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs_Variables::Variable.getScope/0#dispred#e3b1c704__#antijoin_rhs/1@ea1035ad with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable#e2234121/1@67ab17fk
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::SsaSourceVariable#e2234121/1@67ab17fk with 33 rows and digest 479e17hf9q88f3k4s5ihvtv3ql5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getScope/0#dispred#eef6cd59/2@220968be
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::SsaSourceVariable.getScope/0#dispred#eef6cd59/2@220968be with 33 rows and digest 5c94ae21tp1k7gp3nh56e803jjb.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getScope/0#dispred#eef6cd59_10#join_rhs/2@f536c8ii
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::SsaSourceVariable.getScope/0#dispred#eef6cd59_10#join_rhs/2@f536c8ii with 33 rows and digest 5f0fd2rqcm63ov53k255cp1f0u3.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Flow::ControlFlowNode.getScope/0#dispred#b061daac_10#join_rhs/2@b78f7dap
[2025-06-03 11:05:00] (0s)  >>> Created relation Flow::ControlFlowNode.getScope/0#dispred#b061daac_10#join_rhs/2@b78f7dap with 109 rows and digest 7514dfb66phi9mooi5sekog7i0a.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::implicit_definition/1#10d63d77/1@19a9f4tf
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::implicit_definition/1#10d63d77/1@19a9f4tf with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_scope_flow_120#join_rhs/3@c0dfe07u
[2025-06-03 11:05:00] (0s)  >>> Created relation py_scope_flow_120#join_rhs/3@c0dfe07u with 20 rows and digest dbb89bt8f3bi2p0rfp6jco6rdm6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::ModuleVariable.scope_as_global_variable/0#51a92cfd/2@249fd6l2
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::ModuleVariable.scope_as_global_variable/0#51a92cfd/2@249fd6l2 with 24 rows and digest 6c9c4fqdistrnhqn6dl9komj6ld.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::ModuleVariable.scope_as_global_variable/0#51a92cfd_10#join_rhs/2@31f16bbr
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::ModuleVariable.scope_as_global_variable/0#51a92cfd_10#join_rhs/2@31f16bbr with 24 rows and digest 1dfe57uhtdgicf6lupjfcgntl69.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _Flow::CallNode#c5d1dc47_Flow::ControlFlowNode.getScope/0#dispred#b061daac#shared/2@f358463l
[2025-06-03 11:05:00] (0s)  >>> Created relation _Flow::CallNode#c5d1dc47_Flow::ControlFlowNode.getScope/0#dispred#b061daac#shared/2@f358463l with 7 rows and digest 88b175pv6mlfabea3udkinj8gse.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#3/1@948b37j5
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#3/1@948b37j5 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Flow::ImportMemberNode#7f288801/1@c3fd6bqv is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#3/1@948b37j5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Module_.getName/0#2ccd625a/2@38f91ee0
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Module_.getName/0#2ccd625a/2@38f91ee0 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Module_.getName/0#2ccd625a_0#antijoin_rhs/1@a8f41596 is empty, due to AstGenerated::Module_.getName/0#2ccd625a/2@38f91ee0.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985/3@e59cbcdu
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985/3@e59cbcdu with 35 rows and digest 1cb23dnt3nmevu3157k9j89fo26.
[2025-06-03 11:05:00] (0s) Promoting strings for predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985
[2025-06-03 11:05:00] (0s) Promoted strings in predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985 in memory, took 4ms
[2025-06-03 11:05:00] (0s) Saving stringpool to save strings from predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985
[2025-06-03 11:05:00] (0s) Saved stringpool to save strings from predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985, took 0ms
[2025-06-03 11:05:00] (0s)  >>> Created relation cached_Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985/3@c992f399 with 35 rows and digest 1cb23dnt3nmevu3157k9j89fo26.
[2025-06-03 11:05:00] (0s)  >>> Created relation containerparent/2@c7062ak0 with 13 rows and digest b4aba8pkt07gfgu56i2q1v0u93c.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_flags_versioned/3@ee57f1af with 71 rows and digest f0d343d0kcen52kk3pa2j910761.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_flags_versioned#antijoin_rhs/2@699620a1
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_flags_versioned#antijoin_rhs/2@699620a1 with 1 rows and digest 28c0f2dev35sqneakgllt3e37m4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_flags_versioned#count_range/2@229366h4
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_flags_versioned#count_range/2@229366h4 with 1 rows and digest 1cc36eu4rl3qvm0fh8i0c5ubooa.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate __py_flags_versioned#count_range#join_rhs/2@14e24109
[2025-06-03 11:05:00] (0s)  >>> Created relation __py_flags_versioned#count_range#join_rhs/2@14e24109 with 1 rows and digest b1c316s93erje3ogkgk1d3n8rm5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::import_path_element/1#dd8f0854/2@dee48ej4
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::import_path_element/1#dd8f0854/2@dee48ej4 with 5 rows and digest 26ffe2rmvghtdib6qvc0kavltc9.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::import_path_element/1#dd8f0854_10#join_rhs/2@daa5d9bb
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::import_path_element/1#dd8f0854_10#join_rhs/2@daa5d9bb with 5 rows and digest f7b9744cb4rot0cga16l3c37sqc.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Container.isImportRoot/1#dispred#c81b5504/2@122714vb
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Container.isImportRoot/1#dispred#c81b5504/2@122714vb with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Files::Container.getImportRoot/1#dispred#d481489f#bff/3@e62cbc8a is empty, due to Files::Container.isImportRoot/1#dispred#c81b5504/2@122714vb.
[2025-06-03 11:05:00] (0s) Inferred that project#Files::Container.isImportRoot/1#dispred#c81b5504/1@2f20cab0 is empty, due to Files::Container.isImportRoot/1#dispred#c81b5504/2@122714vb.
[2025-06-03 11:05:00] (0s) Inferred that project#Files::Container.getImportRoot/1#dispred#d481489f#bff/2@49b53109 is empty, due to Files::Container.getImportRoot/1#dispred#d481489f#bff/3@e62cbc8a.
[2025-06-03 11:05:00] (0s) Inferred that _Files::Container.getImportRoot/1#dispred#d481489f#bff_Files::Impl::Container.splitAbsolutePath/2#di__#shared/3@44ba06cf is empty, due to Files::Container.getImportRoot/1#dispred#d481489f#bff/3@e62cbc8a.
[2025-06-03 11:05:00] (0s) Inferred that #Files::Container.getParent/0#dispred#c9586936Plus#fb#flipped/2@2503cc9v is empty, due to project#Files::Container.isImportRoot/1#dispred#c81b5504/1@2f20cab0.
[2025-06-03 11:05:00] (0s) Inferred that _#Files::Container.getParent/0#dispred#c9586936Plus#fb#flipped_10#join_rhs_folders_project#Files::Co__#antijoin_rhs/1@151961am is empty, due to project#Files::Container.isImportRoot/1#dispred#c81b5504/1@2f20cab0.
[2025-06-03 11:05:00] (0s) Inferred that __Files::Container.getImportRoot/1#dispred#d481489f#bff_Files::Impl::Container.splitAbsolutePath/2#d__#antijoin_rhs/3@6f65f4ib is empty, due to _Files::Container.getImportRoot/1#dispred#d481489f#bff_Files::Impl::Container.splitAbsolutePath/2#di__#shared/3@44ba06cf.
[2025-06-03 11:05:00] (0s) Inferred that Files::Container.getImportRoot/0#dispred#b07a8632#bf/2@178c02a0 is empty, due to _Files::Container.getImportRoot/1#dispred#d481489f#bff_Files::Impl::Container.splitAbsolutePath/2#di__#shared/3@44ba06cf.
[2025-06-03 11:05:00] (0s) Inferred that #Files::Container.getParent/0#dispred#c9586936Plus#fb#flipped_10#join_rhs/2@25ee87d6 is empty, due to #Files::Container.getParent/0#dispred#c9586936Plus#fb#flipped/2@2503cc9v.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Module::isStubRoot/1#400a3be9/1@4b7698pj
[2025-06-03 11:05:00] (0s)  >>> Created relation Module::isStubRoot/1#400a3be9/1@4b7698pj with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_021#join_rhs/3@9a865792
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_021#join_rhs/3@9a865792 with 35 rows and digest fa90c2e6selu8hj7o91vie2uju8.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_201#join_rhs/3@117d3emi
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_201#join_rhs/3@117d3emi with 35 rows and digest 6cae28ml8tja0d80r5q0jjsgrr5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Module::moduleNameFromBase/1#adf5634f/2@f22257b3
[2025-06-03 11:05:00] (0s)  >>> Created relation Module::moduleNameFromBase/1#adf5634f/2@f22257b3 with 21 rows and digest bf93b8r84ehpbls89tfo88hqf8c.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate containerparent_10#join_rhs/2@e6fa8emb
[2025-06-03 11:05:00] (0s)  >>> Created relation containerparent_10#join_rhs/2@e6fa8emb with 13 rows and digest 71915citgmr4t10cd1m3mc8ca1f.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate CachedStages::Stages::AST::backref/0#dddb3000/0@dab1424s
[2025-06-03 11:05:00] (0s)  >>> Created relation CachedStages::Stages::AST::backref/0#dddb3000/0@dab1424s with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::File.getShortName/0#dispred#1a9d2ead/2@f8191cuc
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::File.getShortName/0#dispred#1a9d2ead/2@f8191cuc with 7 rows and digest c098b5vub34hi52aj3nkb8c34t5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_module_path_10#join_rhs/2@5a2bd62f
[2025-06-03 11:05:00] (0s)  >>> Created relation py_module_path_10#join_rhs/2@5a2bd62f with 2 rows and digest df4c0apcvb0vdnh2jnd5gf27lne.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate m#Files::File.isPossibleEntryPoint/0#dispred#4586fa29#b/1@15bbe8h4
[2025-06-03 11:05:00] (0s)  >>> Created relation m#Files::File.isPossibleEntryPoint/0#dispred#4586fa29#b/1@15bbe8h4 with 5 rows and digest d6c8d9t41qnqkdr5069pol1u9a0.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate m#Module::Module.getFile/0#dispred#53eb9b1b#fb/1@c908a2n6
[2025-06-03 11:05:00] (0s)  >>> Created relation m#Module::Module.getFile/0#dispred#53eb9b1b#fb/1@c908a2n6 with 9 rows and digest 94b3a31nrgo9j2mtinh7j4vumj9.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Module::Module.getFile/0#dispred#53eb9b1b#fb/2@eafe587d
[2025-06-03 11:05:00] (0s)  >>> Created relation Module::Module.getFile/0#dispred#53eb9b1b#fb/2@eafe587d with 2 rows and digest 76820d8bcba222g98abm3vg7e07.
[2025-06-03 11:05:00] (0s)  >>> Created relation py_comments/3@57ede7m5 with 11 rows and digest b587cdhecki6h3hbj1ss1fqu7ib.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate m#Files::Location.getStartLine/0#dispred#a15d2f9d#bf/1@c9d012qg
[2025-06-03 11:05:00] (0s)  >>> Created relation m#Files::Location.getStartLine/0#dispred#a15d2f9d#bf/1@c9d012qg with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Files::Location.getStartLine/0#dispred#a15d2f9d#bf/2@0c1241r4 is empty, due to m#Files::Location.getStartLine/0#dispred#a15d2f9d#bf/1@c9d012qg.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82_0#antijoin_rhs/1@c9d75c81
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82_0#antijoin_rhs/1@c9d75c81 with 2 rows and digest c5e60d619qglu3ss57jrk3f7qq7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62_py_stmts#shared/1@833bb7gv
[2025-06-03 11:05:00] (0s)  >>> Created relation _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62_py_stmts#shared/1@833bb7gv with 27 rows and digest de92dbubjgqnqumstd6cnff4415.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _Stmts::Stmt.getLocation/0#dispred#18f9d034_py_stmts#shared/1@650c248h
[2025-06-03 11:05:00] (0s)  >>> Created relation _Stmts::Stmt.getLocation/0#dispred#18f9d034_py_stmts#shared/1@650c248h with 27 rows and digest de92dbubjgqnqumstd6cnff4415.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate m#Files::Location.getFile/0#dispred#fab46add#bf/1@73c6bb27
[2025-06-03 11:05:00] (0s)  >>> Created relation m#Files::Location.getFile/0#dispred#fab46add#bf/1@73c6bb27 with 29 rows and digest bbed402fgopsftmo927ulf461j1.
[2025-06-03 11:05:00] (0s)  >>> Created relation locations_default/6@2b5cc3pr with 574 rows and digest e548d7crqpjq6b0dj0bibvtc344.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Location.getPath/0#dispred#4f81638b#bf/2@56f2312l
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Location.getPath/0#dispred#4f81638b#bf/2@56f2312l with 29 rows and digest ab98afui91708fvbmcikb7gl923.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Location.getPath/0#dispred#4f81638b#bf_10#join_rhs/2@aa7bb0qf
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Location.getPath/0#dispred#4f81638b#bf_10#join_rhs/2@aa7bb0qf with 29 rows and digest f351efv0cakjoolojuhosjh5u09.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Location.getFile/0#dispred#fab46add#bf/2@2b4a057s
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Location.getFile/0#dispred#fab46add#bf/2@2b4a057s with 29 rows and digest ab98afui91708fvbmcikb7gl923.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Module::moduleImportedInPackage/1#90099bf3/2@22fd5di2
[2025-06-03 11:05:00] (0s)  >>> Created relation Module::moduleImportedInPackage/1#90099bf3/2@22fd5di2 with 2 rows and digest b44c3a33c30nk8hulaavn9p9j97.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_120#join_rhs/3@a583e3e5
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_120#join_rhs/3@a583e3e5 with 35 rows and digest b22242udif5o0t3n6ll5iqlvuq9.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_20#join_rhs/2@896ce886
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_20#join_rhs/2@896ce886 with 35 rows and digest 2e8a392te4v0ovqi8as9q94hh81.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Module::isPotentialModuleFile/2#f93ea5ba/2@c2e6083m
[2025-06-03 11:05:00] (0s)  >>> Created relation Module::isPotentialModuleFile/2#f93ea5ba/2@c2e6083m with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that Module::isPotentialModuleFile/2#f93ea5ba_10#join_rhs/2@8f9b7dr9 is empty, due to Module::isPotentialModuleFile/2#f93ea5ba/2@c2e6083m.
[2025-06-03 11:05:00] (0s) Inferred that _Module::isPackage/2#0f4cbd4a_Module::isPotentialModuleFile/2#f93ea5ba_containerparent_10#join_rhs#antijoin_rhs/2@3ffb05ar is empty, due to Module::isPotentialModuleFile/2#f93ea5ba/2@c2e6083m.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate m#Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb/1@e69a47jq
[2025-06-03 11:05:00] (0s)  >>> Created relation m#Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb/1@e69a47jq with 14 rows and digest 7836fbj6g3l99qbu8dshshsfcla.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb/2@f469e8en
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb/2@f469e8en with 7 rows and digest c1df0fcbvpt91sgmmlacfn17p2e.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb_10#join_rhs/2@bc9afava
[2025-06-03 11:05:00] (0s)  >>> Created relation Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb_10#join_rhs/2@bc9afava with 7 rows and digest 34f43b275pgh4t0nqul22hpgmu4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Module::isRegularPackage/2#c6677254/2@d08fb5sh
[2025-06-03 11:05:00] (0s)  >>> Created relation Module::isRegularPackage/2#c6677254/2@d08fb5sh with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_Module::isRegularPackage/2#c6677254_Mod__#shared/2@4e30f3ku
[2025-06-03 11:05:00] (0s)  >>> Created relation _Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_Module::isRegularPackage/2#c6677254_Mod__#shared/2@4e30f3ku with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that _Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb_10#join_rhs_Files::Impl::Container.getFolder/__#antijoin_rhs/2@bdbac54b is empty, due to _Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_Module::isRegularPackage/2#c6677254_Mod__#shared/2@4e30f3ku.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Module::isPackage/2#0f4cbd4a/2@76e0a6r5
[2025-06-03 11:05:00] (0s)  >>> Created relation Module::isPackage/2#0f4cbd4a/2@76e0a6r5 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that _Module::isPackage/2#0f4cbd4a_containerparent_10#join_rhs#antijoin_rhs/2@2760b4b8 is empty, due to Module::isPackage/2#0f4cbd4a/2@76e0a6r5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Definitions::ModuleVariable.global_variable_callnode/0#0a973b2a/2@19ac6bth
[2025-06-03 11:05:00] (0s)  >>> Created relation Definitions::ModuleVariable.global_variable_callnode/0#0a973b2a/2@19ac6bth with 28 rows and digest d890barb75ci1s42dai5fvigfga.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Flow::NameNode.deletes/1#dispred#2c693720/2@82a0a0b4
[2025-06-03 11:05:00] (0s)  >>> Created relation Flow::NameNode.deletes/1#dispred#2c693720/2@82a0a0b4 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that SsaDefinitions::SsaSource::deletion_definition/2#e74a17e3/2@4dc9d10j is empty, due to Flow::NameNode.deletes/1#dispred#2c693720/2@82a0a0b4.
[2025-06-03 11:05:00] (0s) Inferred that cached_SsaDefinitions::SsaSource::deletion_definition/2#e74a17e3/2@a934fde8 is empty, due to SsaDefinitions::SsaSource::deletion_definition/2#e74a17e3/2@4dc9d10j.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Module::Module.isPackage/0#dispred#a8679d5f/1@9131c737
[2025-06-03 11:05:00] (0s)  >>> Created relation Module::Module.isPackage/0#dispred#a8679d5f/1@9131c737 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate py_flow_bb_node_1023#join_rhs/4@a7f552tj
[2025-06-03 11:05:00] (0s)  >>> Created relation py_flow_bb_node_1023#join_rhs/4@a7f552tj with 109 rows and digest b98ec3g8207ervckvg4n8tkuah6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstExtended::AstNode#8b357598/1@8ce7eav4
[2025-06-03 11:05:00] (0s)  >>> Created relation AstExtended::AstNode#8b357598/1@8ce7eav4 with 129 rows and digest f222f83rvfolqup2k53j6e0dea6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#6/1@708131a3
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#6/1@708131a3 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#2/1@5223c9t1
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#2/1@5223c9t1 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstExtended::AstNode_not_Exprs::Attribute_Exprs::Call_Exprs::IfExp_Import::ImportMember_Exprs::Name_Exprs::NameConstant_Exprs::PlaceHolder_Exprs::Subscript#9990a81b/1@bdc6fc0u
[2025-06-03 11:05:00] (0s)  >>> Created relation AstExtended::AstNode_not_Exprs::Attribute_Exprs::Call_Exprs::IfExp_Import::ImportMember_Exprs::Name_Exprs::NameConstant_Exprs::PlaceHolder_Exprs::Subscript#9990a81b/1@bdc6fc0u with 84 rows and digest caee64q4blr1b4rglho65mfouc4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Name.getAFlowNode/0#d7472f1f/2@c5cefarl
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Name.getAFlowNode/0#d7472f1f/2@c5cefarl with 34 rows and digest f9a400ieso9e56ea7i3dhfuqkl7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Name_not_Exprs::NameConstant#8eac0b8c/1@b35272ig
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Name_not_Exprs::NameConstant#8eac0b8c/1@b35272ig with 34 rows and digest 8536dad57baff9tetc9htmcc2k7.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee/2@1996e76t
[2025-06-03 11:05:00] (0s)  >>> Created relation AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee/2@1996e76t with 109 rows and digest 260af6fcpll0em4qao052h8j0q4.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Expr.getScope/0#dispred#71058b3b_10#join_rhs/2@244cddr1
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Expr.getScope/0#dispred#71058b3b_10#join_rhs/2@244cddr1 with 86 rows and digest fb9626kom9q443qf79u5oligjda.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Flow::list_or_tuple_nested_element/1#8389faf7/2@i1#4d325enu (iteration 1)
[2025-06-03 11:05:00] (0s) Empty delta for Flow::list_or_tuple_nested_element/1#8389faf7_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:00] (0s) Accumulating deltas
[2025-06-03 11:05:00] (0s)  >>> Created relation Flow::list_or_tuple_nested_element/1#8389faf7/2@4d325enu with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Flow::DefinitionNode#e8809c3b/1@8e449cle
[2025-06-03 11:05:00] (0s)  >>> Created relation Flow::DefinitionNode#e8809c3b/1@8e449cle with 23 rows and digest 5f6dcbtbrphb5cuovedd0066220.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate m#Flow::DefinitionNode.getValue/0#dispred#e8c0c58e#bf/1@e65adeet
[2025-06-03 11:05:00] (0s)  >>> Created relation m#Flow::DefinitionNode.getValue/0#dispred#e8c0c58e#bf/1@e65adeet with 23 rows and digest 5f6dcbtbrphb5cuovedd0066220.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate m#Flow::assigned_value/1#97af2d4b#bf/1@6303b6t9
[2025-06-03 11:05:00] (0s)  >>> Created relation m#Flow::assigned_value/1#97af2d4b#bf/1@6303b6t9 with 23 rows and digest 7375ednbtsh28j6v7u8l6gc46s5.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _AstGenerated::Assign_.getATarget/0#dispred#8e860934_AstGenerated::Assign_.getValue/0#dispred#53d00b__#shared/2@6a6f9cjr
[2025-06-03 11:05:00] (0s)  >>> Created relation _AstGenerated::Assign_.getATarget/0#dispred#8e860934_AstGenerated::Assign_.getValue/0#dispred#53d00b__#shared/2@6a6f9cjr with 21 rows and digest a41adb22j9hoegvhjifvfstvvh6.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Assign_.getATarget/0#dispred#8e860934_10#join_rhs/2@3f9060gb
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Assign_.getATarget/0#dispred#8e860934_10#join_rhs/2@3f9060gb with 21 rows and digest 2d241cvpra4b7ips540ov2augo9.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate SsaDefinitions::SsaSource::parameter_definition/2#40b2fede/2@80e70ess
[2025-06-03 11:05:00] (0s)  >>> Created relation SsaDefinitions::SsaSource::parameter_definition/2#40b2fede/2@80e70ess with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that cached_SsaDefinitions::SsaSource::parameter_definition/2#40b2fede/2@9409b5ba is empty, due to SsaDefinitions::SsaSource::parameter_definition/2#40b2fede/2@80e70ess.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate CachedStages::Stages::AST::ref/0#e52d92b5/0@72b8f244
[2025-06-03 11:05:00] (0s)  >>> Created relation CachedStages::Stages::AST::ref/0#e52d92b5/0@72b8f244 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 11:05:00] (0s) No need to promote strings for predicate CachedStages::Stages::AST::ref/0#e52d92b5  as it does not contain computed strings.
[2025-06-03 11:05:00] (0s)  >>> Created relation cached_CachedStages::Stages::AST::ref/0#e52d92b5/0@c876115v with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 11:05:00] (0s)  >>> Created relation cached_CachedStages::Stages::AST::backref/0#dddb3000/0@a80d27a0 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 11:05:00] (0s) No need to promote strings for predicate Flow::DefinitionNode#e8809c3b  as it does not contain computed strings.
[2025-06-03 11:05:00] (0s)  >>> Created relation cached_Flow::DefinitionNode#e8809c3b/1@a95b912i with 23 rows and digest 5f6dcbtbrphb5cuovedd0066220.
[2025-06-03 11:05:00] (0s) No need to promote strings for predicate AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee  as it does not contain computed strings.
[2025-06-03 11:05:00] (0s)  >>> Created relation cached_AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee/2@89ae53vo with 109 rows and digest 260af6fcpll0em4qao052h8j0q4.
[2025-06-03 11:05:00] (0s) No need to promote strings for predicate Flow::ControlFlowNode.getScope/0#dispred#b061daac  as it does not contain computed strings.
[2025-06-03 11:05:00] (0s)  >>> Created relation cached_Flow::ControlFlowNode.getScope/0#dispred#b061daac/2@afe6a2k8 with 109 rows and digest 24903dlh7adrl0pf81ntttrn0rd.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Assign_.getValue/0#dispred#53d00b56_10#join_rhs/2@e905adi5
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Assign_.getValue/0#dispred#53d00b56_10#join_rhs/2@e905adi5 with 21 rows and digest ed4870593i4be61e0dct7rcjoh0.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7/3@21a3884h
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7/3@21a3884h with 5 rows and digest 8c20941p5ee97qhgismerp23g96.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#10/1@8754f207
[2025-06-03 11:05:00] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#10/1@8754f207 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate _AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7_py_exprs#antijoin_rhs/3@492ad1u9
[2025-06-03 11:05:00] (0s)  >>> Created relation _AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7_py_exprs#antijoin_rhs/3@492ad1u9 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Call.getArg/1#dispred#50c53eea/3@63b78cee
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Call.getArg/1#dispred#50c53eea/3@63b78cee with 5 rows and digest 8c20941p5ee97qhgismerp23g96.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Exprs::Call.getArg/1#dispred#50c53eea_120#join_rhs/3@38981eo1
[2025-06-03 11:05:00] (0s)  >>> Created relation Exprs::Call.getArg/1#dispred#50c53eea_120#join_rhs/3@38981eo1 with 5 rows and digest ef71fcea1d792bq1k3aila2b18a.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Function::FunctionExpr.getADecoratorCall/0#062a6e2a/2@i1#800dedr3 (iteration 1)
[2025-06-03 11:05:00] (0s) Empty delta for Function::FunctionExpr.getADecoratorCall/0#062a6e2a_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:00] (0s) Accumulating deltas
[2025-06-03 11:05:00] (0s)  >>> Created relation Function::FunctionExpr.getADecoratorCall/0#062a6e2a/2@800dedr3 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Function::FunctionDef#ffa22159/2@28dc4916
[2025-06-03 11:05:00] (0s)  >>> Created relation Function::FunctionDef#ffa22159/2@28dc4916 with 3 rows and digest ee4888dp68puidtt9rgbbvl534d.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate project#Function::FunctionDef#ffa22159/1@14296568
[2025-06-03 11:05:00] (0s)  >>> Created relation project#Function::FunctionDef#ffa22159/1@14296568 with 3 rows and digest d50492310js1lnqpg0ef31aqqta.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Stmts::AssignStmt#45f46a75/1@b012f16r
[2025-06-03 11:05:00] (0s)  >>> Created relation Stmts::AssignStmt#45f46a75/1@b012f16r with 18 rows and digest 78df83bjfr2c5p1t45jbfg4he37.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Assign__not_Stmts::AssignStmt_Class::ClassDef_Function::FunctionDef#9571075b/1@c32212q8
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Assign__not_Stmts::AssignStmt_Class::ClassDef_Function::FunctionDef#9571075b/1@c32212q8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate AstGenerated::Module_.getKind/0#dd90ac6f/2@95377982
[2025-06-03 11:05:00] (0s)  >>> Created relation AstGenerated::Module_.getKind/0#dd90ac6f/2@95377982 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:00] (0s) Inferred that AstGenerated::Module_.getKind/0#dd90ac6f_0#antijoin_rhs/1@a84c4ek9 is empty, due to AstGenerated::Module_.getKind/0#dd90ac6f/2@95377982.
[2025-06-03 11:05:00] (0s) Starting to evaluate predicate Module::Module.getKind/0#dispred#ccc4de3e/2@2d81416l
[2025-06-03 11:05:00] (0s)  >>> Created relation Module::Module.getKind/0#dispred#ccc4de3e/2@2d81416l with 2 rows and digest b14910mh2gsibs8hp4c04vubao8.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Function::Function.toString/0#dispred#fa659a0b/2@be7086fo
[2025-06-03 11:05:01] (0s)  >>> Created relation Function::Function.toString/0#dispred#fa659a0b/2@be7086fo with 3 rows and digest 48a938mgeb9jgaa6lm0i9hg9tsa.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::Call_.getFunc/0#dispred#9f0e5cfd_10#join_rhs/2@1cfa8c7b
[2025-06-03 11:05:01] (0s)  >>> Created relation AstGenerated::Call_.getFunc/0#dispred#9f0e5cfd_10#join_rhs/2@1cfa8c7b with 7 rows and digest 9987b686blqjfjjkogj93rf8ps3.
[2025-06-03 11:05:01] (0s)  >>> Created relation py_numbers/3@fdd7bbbb with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that py_numbers_201#join_rhs/3@1c498f0c is empty, due to py_numbers/3@fdd7bbbb.
[2025-06-03 11:05:01] (0s) Inferred that Exprs::ImaginaryLiteral#d2b980a7/2@d3f538r2 is empty, due to py_numbers_201#join_rhs/3@1c498f0c.
[2025-06-03 11:05:01] (0s) Inferred that Exprs::FloatLiteral#eb01d209/1@b7b0a64j is empty, due to py_numbers_201#join_rhs/3@1c498f0c.
[2025-06-03 11:05:01] (0s) Inferred that Exprs::ImaginaryLiteral#d2b980a7_0#antijoin_rhs/1@fd0e28cj is empty, due to Exprs::ImaginaryLiteral#d2b980a7/2@d3f538r2.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Exprs::IntegerLiteral#51a23263/1@13583e4r
[2025-06-03 11:05:01] (0s)  >>> Created relation Exprs::IntegerLiteral#51a23263/1@13583e4r with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Exprs::Num#4a336806/1@1e48f0e6
[2025-06-03 11:05:01] (0s)  >>> Created relation Exprs::Num#4a336806/1@1e48f0e6 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate py_scope_flow_02#join_rhs/2@687f8fu4
[2025-06-03 11:05:01] (0s)  >>> Created relation py_scope_flow_02#join_rhs/2@687f8fu4 with 20 rows and digest dabd576cbk5it6t77qvqumn3re2.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate project#Scope::Scope.getANormalExit/0#dispred#1288c0a8/1@78a541r8
[2025-06-03 11:05:01] (0s)  >>> Created relation project#Scope::Scope.getANormalExit/0#dispred#1288c0a8/1@78a541r8 with 5 rows and digest 133f71tdk50amvi28b2ema274uf.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _project#Scope::Scope.getANormalExit/0#dispred#1288c0a8_py_flow_bb_node_py_scope_flow_02#join_rhs#antijoin_rhs/2@67e3d421
[2025-06-03 11:05:01] (0s)  >>> Created relation _project#Scope::Scope.getANormalExit/0#dispred#1288c0a8_py_flow_bb_node_py_scope_flow_02#join_rhs#antijoin_rhs/2@67e3d421 with 10 rows and digest 56177887q0d9hg17ioejnmtvl27.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Exprs::StringLiteral.getText/0#dispred#8c21c5a0/2@7c4c2dkg
[2025-06-03 11:05:01] (0s)  >>> Created relation Exprs::StringLiteral.getText/0#dispred#8c21c5a0/2@7c4c2dkg with 33 rows and digest c274ccm8l4b39fd39aumogujh41.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7#ffb/3@a4dc08h1
[2025-06-03 11:05:01] (0s)  >>> Created relation AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7#ffb/3@a4dc08h1 with 5 rows and digest 8c20941p5ee97qhgismerp23g96.
[2025-06-03 11:05:01] (0s)  >>> Created relation _AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7#ffb_py_exprs#antijoin_rhs/3@45e6d618 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s)  >>> Created relation Exprs::Call.getArg/1#dispred#50c53eea#ffb/3@1505fcts with 5 rows and digest 8c20941p5ee97qhgismerp23g96.
[2025-06-03 11:05:01] (0s)  >>> Created relation Exprs::Call.getArg/1#dispred#50c53eea#ffb_120#join_rhs/3@3628ddph with 5 rows and digest ef71fcea1d792bq1k3aila2b18a.
[2025-06-03 11:05:01] (0s)  >>> Created relation Function::FunctionExpr.getADecoratorCall/0#062a6e2a/2@0345bbbi with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that project#Function::FunctionExpr.getADecoratorCall/0#062a6e2a/1@d0a3dcds is empty, due to Function::FunctionExpr.getADecoratorCall/0#062a6e2a/2@0345bbbi.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Class::ClassExpr.getADecoratorCall/0#54df5696/2@i1#a74da0oo (iteration 1)
[2025-06-03 11:05:01] (0s) Empty delta for Class::ClassExpr.getADecoratorCall/0#54df5696_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Accumulating deltas
[2025-06-03 11:05:01] (0s)  >>> Created relation Class::ClassExpr.getADecoratorCall/0#54df5696/2@a74da0oo with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that project#Class::ClassExpr.getADecoratorCall/0#54df5696/1@a676207g is empty, due to Class::ClassExpr.getADecoratorCall/0#54df5696/2@a74da0oo.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate m#AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb/1@401b0cbv
[2025-06-03 11:05:01] (0s)  >>> Created relation m#AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb/1@401b0cbv with 36 rows and digest f419fcln5ls5pmh02lmdqsglhv3.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate py_exprs_032#join_rhs/3@dc21c4iv
[2025-06-03 11:05:01] (0s)  >>> Created relation py_exprs_032#join_rhs/3@dc21c4iv with 86 rows and digest be84dc6fojrfanlpc0s7kg5t146.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb/2@07bf1fqf
[2025-06-03 11:05:01] (0s)  >>> Created relation AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb/2@07bf1fqf with 15 rows and digest 2c34b4ds38arvh8rtlaktlm1ifc.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb_10#join_rhs/2@507d7798
[2025-06-03 11:05:01] (0s)  >>> Created relation AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb_10#join_rhs/2@507d7798 with 15 rows and digest f1086cgntibv1e5hsri52799ird.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::Assign_.getTarget/1#dispred#6ad44d47/3@b7f147lv
[2025-06-03 11:05:01] (0s)  >>> Created relation AstGenerated::Assign_.getTarget/1#dispred#6ad44d47/3@b7f147lv with 21 rows and digest 3afd64ju6tlmhqf0ncantatbv3a.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Exprs::Name.getId/0#dispred#4fa5460e/2@1c667ebq
[2025-06-03 11:05:01] (0s)  >>> Created relation Exprs::Name.getId/0#dispred#4fa5460e/2@1c667ebq with 34 rows and digest 52e800lsal1tbvs2vop4h4jq09f.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#4/1@7c43e84t
[2025-06-03 11:05:01] (0s)  >>> Created relation _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#4/1@7c43e84t with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#3/1@24b57beu
[2025-06-03 11:05:01] (0s)  >>> Created relation _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#3/1@24b57beu with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#2/1@fa83b530
[2025-06-03 11:05:01] (0s)  >>> Created relation _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#2/1@fa83b530 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#1/1@3b4debiv
[2025-06-03 11:05:01] (0s)  >>> Created relation _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#1/1@3b4debiv with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs/1@5c282dme
[2025-06-03 11:05:01] (0s)  >>> Created relation _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs/1@5c282dme with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate #select/2@f16fab3v
[2025-06-03 11:05:01] (0s)  >>> Created relation #select/2@f16fab3v with 11 rows and digest 31d14fnicp684o739qi5bjhqifd.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Function::FunctionDef#ffa22159/2@fc4e36sm
[2025-06-03 11:05:01] (0s)  >>> Created relation Function::FunctionDef#ffa22159/2@fc4e36sm with 3 rows and digest ee4888dp68puidtt9rgbbvl534d.
[2025-06-03 11:05:01] (0s)  >>> Created relation project#Function::FunctionDef#ffa22159/1@0c0f5b8n with 3 rows and digest d50492310js1lnqpg0ef31aqqta.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Class::ClassDef#83097204/1@4d2446ee
[2025-06-03 11:05:01] (0s)  >>> Created relation Class::ClassDef#83097204/1@4d2446ee with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s)  >>> Created relation Stmts::AssignStmt#45f46a75/1@8cc50es5 with 18 rows and digest 78df83bjfr2c5p1t45jbfg4he37.
[2025-06-03 11:05:01] (0s)  >>> Created relation AstGenerated::Assign__not_Stmts::AssignStmt_Class::ClassDef_Function::FunctionDef#9571075b/1@670cb7di with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::Assign_.toString/0#dispred#782906e5/2@926fbbjs
[2025-06-03 11:05:01] (0s)  >>> Created relation AstGenerated::Assign_.toString/0#dispred#782906e5/2@926fbbjs with 21 rows and digest d51e8egpd4u1ktj29dkmi7v0js1.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate project#AstGenerated::Assign_.toString/0#dispred#782906e5/1@98168d1j
[2025-06-03 11:05:01] (0s)  >>> Created relation project#AstGenerated::Assign_.toString/0#dispred#782906e5/1@98168d1j with 21 rows and digest febace78imdtv5m9a7plcm5lh29.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate project##select/1@88f9dba5
[2025-06-03 11:05:01] (0s)  >>> Created relation project##select/1@88f9dba5 with 11 rows and digest b2caa6rj5eoa5t5h17jju7qh85c.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/2@d12518j9
[2025-06-03 11:05:01] (0s)  >>> Created relation AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/2@d12518j9 with 11 rows and digest d00685npgc6rco3s1rdv6iak21a.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate project#AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/1@867aef77
[2025-06-03 11:05:01] (0s)  >>> Created relation project#AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/1@867aef77 with 11 rows and digest b2caa6rj5eoa5t5h17jju7qh85c.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _#select_project#AstGenerated::Assign_.toString/0#dispred#782906e5_project#AstGenerated::AstNode_.to__#antijoin_rhs/2@e6c6ddvh
[2025-06-03 11:05:01] (0s)  >>> Created relation _#select_project#AstGenerated::Assign_.toString/0#dispred#782906e5_project#AstGenerated::AstNode_.to__#antijoin_rhs/2@e6c6ddvh with 11 rows and digest 31d14fnicp684o739qi5bjhqifd.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _#select_AstGenerated::Assign_.toString/0#dispred#782906e5_AstGenerated::AstNode_.toString/0#dispred__#shared/3@da7a36a7
[2025-06-03 11:05:01] (0s)  >>> Created relation _#select_AstGenerated::Assign_.toString/0#dispred#782906e5_AstGenerated::AstNode_.toString/0#dispred__#shared/3@da7a36a7 with 11 rows and digest 2ee96c59ed1i17jiu5knkassa2e.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::Stmt_.getLocation/0#dispred#de1f1018#bf/2@c66903vg
[2025-06-03 11:05:01] (0s)  >>> Created relation AstGenerated::Stmt_.getLocation/0#dispred#de1f1018#bf/2@c66903vg with 11 rows and digest 2020e92fu0hfkgfsu0ss3vu1kd5.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate m#Files::Location.hasLocationInfo/5#dispred#143bb906#bfffff/1@6ab556h4
[2025-06-03 11:05:01] (0s)  >>> Created relation m#Files::Location.hasLocationInfo/5#dispred#143bb906#bfffff/1@6ab556h4 with 11 rows and digest b181f5dol9eda5mks5llt3c3afb.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Module::Module.getFile/0#dispred#53eb9b1b/2@648ff7sb
[2025-06-03 11:05:01] (0s)  >>> Created relation Module::Module.getFile/0#dispred#53eb9b1b/2@648ff7sb with 2 rows and digest 76820d8bcba222g98abm3vg7e07.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Module::Module.getFile/0#dispred#53eb9b1b_0#antijoin_rhs/1@b54f01qv
[2025-06-03 11:05:01] (0s)  >>> Created relation Module::Module.getFile/0#dispred#53eb9b1b_0#antijoin_rhs/1@b54f01qv with 2 rows and digest 1758e8s2mdeq4m767kljah7j4r6.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate locations_ast_102345#join_rhs/6@ba5e2d5g
[2025-06-03 11:05:01] (0s)  >>> Created relation locations_ast_102345#join_rhs/6@ba5e2d5g with 140 rows and digest 117827sltp60vgg3jp4m5mfbh1b.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Files::Location.hasLocationInfo/5#dispred#143bb906#bfffff/6@ef8355mj
[2025-06-03 11:05:01] (0s)  >>> Created relation Files::Location.hasLocationInfo/5#dispred#143bb906#bfffff/6@ef8355mj with 11 rows and digest e07124lv2j39gcfqgqfp5228l69.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62__#select_AstGenerated::Assign_.toString/0#dispr__#shared/4@7cf6c3fn
[2025-06-03 11:05:01] (0s)  >>> Created relation _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62__#select_AstGenerated::Assign_.toString/0#dispr__#shared/4@7cf6c3fn with 11 rows and digest 75d6a9me7fi97bk27e7egagq8u4.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _AstGenerated::Stmt_.getLocation/0#dispred#de1f1018#bf__#select_AstGenerated::Assign_.toString/0#dis__#shared/4@1a58d0qg
[2025-06-03 11:05:01] (0s)  >>> Created relation _AstGenerated::Stmt_.getLocation/0#dispred#de1f1018#bf__#select_AstGenerated::Assign_.toString/0#dis__#shared/4@1a58d0qg with 11 rows and digest 75d6a9me7fi97bk27e7egagq8u4.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate project#Files::Location.hasLocationInfo/5#dispred#143bb906#bfffff/1@0d9c29us
[2025-06-03 11:05:01] (0s)  >>> Created relation project#Files::Location.hasLocationInfo/5#dispred#143bb906#bfffff/1@0d9c29us with 11 rows and digest b181f5dol9eda5mks5llt3c3afb.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate __AstExtended::AstNode.getLocation/0#dispred#6b4dcb62__#select_AstGenerated::Assign_.toString/0#disp__#antijoin_rhs/3@6bca02me
[2025-06-03 11:05:01] (0s)  >>> Created relation __AstExtended::AstNode.getLocation/0#dispred#6b4dcb62__#select_AstGenerated::Assign_.toString/0#disp__#antijoin_rhs/3@6bca02me with 11 rows and digest 2ee96c59ed1i17jiu5knkassa2e.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate #select#query/8@68cbdbbv
[2025-06-03 11:05:01] (0s)  >>> Created relation #select#query/8@68cbdbbv with 11 rows and digest aa50aek5n20ac9cqk0pgv8qfs22.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs_py_dict_items_10#join_rhs#min_range/2@9982b6ju
[2025-06-03 11:05:01] (0s)  >>> Created relation _AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs_py_dict_items_10#join_rhs#min_range/2@9982b6ju with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that Exprs::Call.getMinimumUnpackingIndex/0#dispred#8e2a7e5f/2@d254eaa4 is empty, due to _AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs_py_dict_items_10#join_rhs#min_range/2@9982b6ju.
[2025-06-03 11:05:01] (0s) Inferred that Exprs::Call.getMinimumUnpackingIndex/0#dispred#8e2a7e5f_0#antijoin_rhs/1@997f86hg is empty, due to Exprs::Call.getMinimumUnpackingIndex/0#dispred#8e2a7e5f/2@d254eaa4.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Exprs::Call.getAKeyword/0#dispred#5e56df84/2@89a240tb
[2025-06-03 11:05:01] (0s)  >>> Created relation Exprs::Call.getAKeyword/0#dispred#5e56df84/2@89a240tb with 5 rows and digest 054dde13cbic9panpf7fa1dja41.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Exprs::Expr.getASubExpression/0#dispred#443395f9/2@9d4f8cs5
[2025-06-03 11:05:01] (0s)  >>> Created relation Exprs::Expr.getASubExpression/0#dispred#443395f9/2@9d4f8cs5 with 36 rows and digest 931faeskf24pbv8c4u34l60u0j3.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstExtended::AstNode.getAChildNode/0#dispred#a130356d/2@e22edc72
[2025-06-03 11:05:01] (0s)  >>> Created relation AstExtended::AstNode.getAChildNode/0#dispred#a130356d/2@e22edc72 with 127 rows and digest fced70t0gh1cundc5s9va72gab4.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstExtended::AstNode.getParentNode/0#dispred#c159e3ee/2@decd0bj1
[2025-06-03 11:05:01] (0s)  >>> Created relation AstExtended::AstNode.getParentNode/0#dispred#c159e3ee/2@decd0bj1 with 127 rows and digest 76d33di2qp08hg947gac2givg43.
[2025-06-03 11:05:01] (0s) No need to promote strings for predicate AstExtended::AstNode.getAChildNode/0#dispred#a130356d  as it does not contain computed strings.
[2025-06-03 11:05:01] (0s)  >>> Created relation cached_AstExtended::AstNode.getAChildNode/0#dispred#a130356d/2@4a4f6brg with 127 rows and digest fced70t0gh1cundc5s9va72gab4.
[2025-06-03 11:05:01] (0s) No need to promote strings for predicate AstExtended::AstNode.getParentNode/0#dispred#c159e3ee  as it does not contain computed strings.
[2025-06-03 11:05:01] (0s)  >>> Created relation cached_AstExtended::AstNode.getParentNode/0#dispred#c159e3ee/2@699751d5 with 127 rows and digest 76d33di2qp08hg947gac2givg43.
[2025-06-03 11:05:01] (0s)  >>> Created relation py_idoms/2@410143e0 with 104 rows and digest e7b7cahp2ck2omh98fcq8cq7q76.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::ControlFlowNode.getBasicBlock/0#dispred#32502ca1/2@7bb6d8k0
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::ControlFlowNode.getBasicBlock/0#dispred#32502ca1/2@7bb6d8k0 with 109 rows and digest 7c9fbcli5h89audnfjibkbqoaqd.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@0669956t
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@0669956t with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that #Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ecPlus/2@efd62daj is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@0669956t.
[2025-06-03 11:05:01] (0s) Inferred that Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec_10#higher_order_body/2@7dc1daln is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@0669956t.
[2025-06-03 11:05:01] (0s) Inferred that cached_Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@408ca88v is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@0669956t.
[2025-06-03 11:05:01] (0s) Inferred that Flow::BasicBlock.strictlyDominates/1#dispred#65ae2ca3/2@b57cb342 is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec_10#higher_order_body/2@7dc1daln.
[2025-06-03 11:05:01] (0s) Inferred that boundedFastTC:Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec_10#higher_order_body:project#py_flow_bb_node/2@fb072d0h is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec_10#higher_order_body/2@7dc1daln.
[2025-06-03 11:05:01] (0s) Inferred that SsaCompute::SsaDefinitions::reachesEndOfBlockRec/4#63bb2cd4/4@7f2bex0h is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec_10#higher_order_body/2@7dc1daln.
[2025-06-03 11:05:01] (0s) Inferred that cached_Flow::BasicBlock.strictlyDominates/1#dispred#65ae2ca3/2@40c1c68m is empty, due to Flow::BasicBlock.strictlyDominates/1#dispred#65ae2ca3/2@b57cb342.
[2025-06-03 11:05:01] (0s)  >>> Created relation py_successors/2@6aebe9dg with 104 rows and digest b3d653gdicc9cjlrpt3m1gqk07d.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate py_successors_10#join_rhs/2@3d6d1bpm
[2025-06-03 11:05:01] (0s)  >>> Created relation py_successors_10#join_rhs/2@3d6d1bpm with 104 rows and digest e7b7cahp2ck2omh98fcq8cq7q76.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate py_flow_bb_node_23#max_range/2@58ea4f9h
[2025-06-03 11:05:01] (0s)  >>> Created relation py_flow_bb_node_23#max_range/2@58ea4f9h with 109 rows and digest 6864eeltqchgevfr12edlpc8dsf.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate py_flow_bb_node_233#max_term/3@38e43dst
[2025-06-03 11:05:01] (0s)  >>> Created relation py_flow_bb_node_233#max_term/3@38e43dst with 109 rows and digest c32c7cmohqplds4e6fp9re60hec.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::BasicBlock.getLastNode/0#dispred#6e185cc5/2@70ffa0i0
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::BasicBlock.getLastNode/0#dispred#6e185cc5/2@70ffa0i0 with 5 rows and digest 6539adhjs3rvfka9n4ahbbueroa.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::BasicBlock.getLastNode/0#dispred#6e185cc5_10#join_rhs/2@1540a3i5
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::BasicBlock.getLastNode/0#dispred#6e185cc5_10#join_rhs/2@1540a3i5 with 5 rows and digest 131e26urrm01b8ilvracoubqupa.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that #Flow::BasicBlock.getASuccessor/0#dispred#7249dd96Plus/2@aa36c8a7 is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:05:01] (0s) Inferred that Flow::BasicBlock.dominanceFrontier/1#309d1dd6/2@b6351eam is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:05:01] (0s) Inferred that project#Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/1@616cb54h is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:05:01] (0s) Inferred that Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs/2@b268c00t is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:05:01] (0s) Inferred that SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@9473awuj is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:05:01] (0s) Inferred that cached_Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@debdc7ao is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:05:01] (0s) Inferred that cached_#Flow::BasicBlock.getASuccessor/0#dispred#7249dd96Plus/2@4ae642b8 is empty, due to #Flow::BasicBlock.getASuccessor/0#dispred#7249dd96Plus/2@aa36c8a7.
[2025-06-03 11:05:01] (0s) Inferred that SsaCompute::AdjacentUses::blockPrecedesVar/2#b4e71f74#fb/2@77197fkc is empty, due to project#Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/1@616cb54h.
[2025-06-03 11:05:01] (0s) Inferred that _Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs_Flow::ControlFlowNode.getBasicBlock/0__#shared/2@8e989ea9 is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs/2@b268c00t.
[2025-06-03 11:05:01] (0s) Inferred that SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@34c6exe2 is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs/2@b268c00t.
[2025-06-03 11:05:01] (0s) Inferred that Essa::PhiFunction.pred_var/1#da33d336/3@3b88044n is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs/2@b268c00t.
[2025-06-03 11:05:01] (0s) Inferred that SsaCompute::AdjacentUses::varBlockReaches/3#1824ad86/3@259e0cno is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs/2@b268c00t.
[2025-06-03 11:05:01] (0s) Inferred that Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp is empty, due to SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@9473awuj.
[2025-06-03 11:05:01] (0s) Inferred that cached_SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@ce8e8e2f is empty, due to SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@9473awuj.
[2025-06-03 11:05:01] (0s) Inferred that SsaCompute::AdjacentUses::blockPrecedesVar/2#b4e71f74#fb_10#join_rhs/2@4c76a4dd is empty, due to SsaCompute::AdjacentUses::blockPrecedesVar/2#b4e71f74#fb/2@77197fkc.
[2025-06-03 11:05:01] (0s) Inferred that _Flow::BasicBlock.dominates/1#dispred#47f3be21_Flow::ControlFlowNode.getBasicBlock/0#dispred#32502ca__#antijoin_rhs/2@1f8868kr is empty, due to _Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs_Flow::ControlFlowNode.getBasicBlock/0__#shared/2@8e989ea9.
[2025-06-03 11:05:01] (0s) Inferred that SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@7f2bew0h is empty, due to SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@34c6exe2.
[2025-06-03 11:05:01] (0s) Inferred that cached_SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@00f3ba04 is empty, due to SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@34c6exe2.
[2025-06-03 11:05:01] (0s) Inferred that Essa::PhiFunction.pred_var/1#da33d336_120#join_rhs/3@d1fdaft4 is empty, due to Essa::PhiFunction.pred_var/1#da33d336/3@3b88044n.
[2025-06-03 11:05:01] (0s) Inferred that Essa::PhiFunction.getScope/0#dispred#2fe03abc/2@0cda4bmj is empty, due to Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp.
[2025-06-03 11:05:01] (0s) Inferred that Essa::PhiFunction.inputEdgeRefinement/1#dispred#c0feabdb/3@db6addbv is empty, due to Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp.
[2025-06-03 11:05:01] (0s) Inferred that Essa::TPhiFunction#dfb6fe3b_2#join_rhs/1@b12267pb is empty, due to Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp.
[2025-06-03 11:05:01] (0s) Inferred that Essa::PhiFunction.getInput/1#dispred#f797e1b9/3@76e3f3vc is empty, due to Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp.
[2025-06-03 11:05:01] (0s) Inferred that cached_Essa::TPhiFunction#dfb6fe3b/3@2bce46ul is empty, due to Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp.
[2025-06-03 11:05:01] (0s) Inferred that Essa::EssaDefinition.reachesEndOfBlock/1#dispred#b258d4ae/2@2759b57c is empty, due to SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@7f2bew0h.
[2025-06-03 11:05:01] (0s) Inferred that project#SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/2@570bf0jq is empty, due to SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@7f2bew0h.
[2025-06-03 11:05:01] (0s) Inferred that SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902_0312#join_rhs/4@21d3baee is empty, due to SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@7f2bew0h.
[2025-06-03 11:05:01] (0s) Inferred that cached_SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@8d3536mv is empty, due to SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@7f2bew0h.
[2025-06-03 11:05:01] (0s) Inferred that Essa::PhiFunction.inputEdgeRefinement/1#dispred#c0feabdb_01#antijoin_rhs/2@9056d0ou is empty, due to Essa::PhiFunction.inputEdgeRefinement/1#dispred#c0feabdb/3@db6addbv.
[2025-06-03 11:05:01] (0s) Inferred that cached_Essa::PhiFunction.getInput/1#dispred#f797e1b9/3@8f4ad7c4 is empty, due to Essa::PhiFunction.getInput/1#dispred#f797e1b9/3@76e3f3vc.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::NameNode.getId/0#dispred#330d952b/2@21c76cvl
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::NameNode.getId/0#dispred#330d952b/2@21c76cvl with 34 rows and digest e404ffdmdjhg4v83t0gi4voueb8.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Exprs::Name.uses/1#dispred#b73448f6_10#join_rhs/2@08517cqn
[2025-06-03 11:05:01] (0s)  >>> Created relation Exprs::Name.uses/1#dispred#b73448f6_10#join_rhs/2@08517cqn with 11 rows and digest cb6c34kk1aolltnarjam3ok82d8.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs_py_Functions_py_stmts#antijoin_rhs/1@fdcaa239
[2025-06-03 11:05:01] (0s)  >>> Created relation _Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs_py_Functions_py_stmts#antijoin_rhs/1@fdcaa239 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate __Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs_py_Functions_py_stmts#antijoin_rhs_py_Function__#shared/1@b608ab59
[2025-06-03 11:05:01] (0s)  >>> Created relation __Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs_py_Functions_py_stmts#antijoin_rhs_py_Function__#shared/1@b608ab59 with 3 rows and digest 240b48hk108k08n1k98fcki5sf5.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs___Stmts::Stmt.getScope/0#dispred#1debab62_10#jo__#antijoin_rhs/1@a509c20n
[2025-06-03 11:05:01] (0s)  >>> Created relation _Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs___Stmts::Stmt.getScope/0#dispred#1debab62_10#jo__#antijoin_rhs/1@a509c20n with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Function::FastLocalsFunction#401c041e/1@09b036af
[2025-06-03 11:05:01] (0s)  >>> Created relation Function::FastLocalsFunction#401c041e/1@09b036af with 3 rows and digest 240b48hk108k08n1k98fcki5sf5.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Variables::FastLocalVariable#8c848f80/1@3e18b5od
[2025-06-03 11:05:01] (0s)  >>> Created relation Variables::FastLocalVariable#8c848f80/1@3e18b5od with 3 rows and digest 3ebb01o8jllfm4dvo2ng24ieovb.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Exprs::Name.uses/1#dispred#b73448f6_10#join_rhs_Flow::NameNode#b2c17c8a_Variables::FastLocalVariabl__#shared/2@f4aaf5is
[2025-06-03 11:05:01] (0s)  >>> Created relation _Exprs::Name.uses/1#dispred#b73448f6_10#join_rhs_Flow::NameNode#b2c17c8a_Variables::FastLocalVariabl__#shared/2@f4aaf5is with 3 rows and digest 906299a2070r0s9hhli60n3op0c.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Variables::Variable.getScope/0#dispred#e3b1c704____#antijoin_rhs/2@0c00a1la
[2025-06-03 11:05:01] (0s)  >>> Created relation _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Variables::Variable.getScope/0#dispred#e3b1c704____#antijoin_rhs/2@0c00a1la with 3 rows and digest 906299a2070r0s9hhli60n3op0c.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::Scopes::non_local/1#5e3b85d8/1@4d5d2cs2
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::Scopes::non_local/1#5e3b85d8/1@4d5d2cs2 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3_Flow::NameNode.getId/0#dispred#330d952b_Flow::Scope__#shared/2@f0a715ke
[2025-06-03 11:05:01] (0s)  >>> Created relation _Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3_Flow::NameNode.getId/0#dispred#330d952b_Flow::Scope__#shared/2@f0a715ke with 11 rows and digest b3ed2eb4nh1ra6ud6se3l3s9tpe.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3_Flow::Scopes::non_local/1#5e3b85d8#shared/1@ca65d1o3
[2025-06-03 11:05:01] (0s)  >>> Created relation _Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3_Flow::Scopes::non_local/1#5e3b85d8#shared/1@ca65d1o3 with 15 rows and digest 9e3c9f90ovn056tt122v6kqo33d.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs_py_flow_bb_node_10#join_rhs#shared#1/1@f244abi2
[2025-06-03 11:05:01] (0s)  >>> Created relation _py_exprs_10#join_rhs_py_flow_bb_node_10#join_rhs#shared#1/1@f244abi2 with 2 rows and digest b26a127vepn91mll8akuk9m8f1b.
[2025-06-03 11:05:01] (0s)  >>> Created relation py_ssa_use/2@75fd04ej with 27 rows and digest dfbeecqrqb8t33il3nnj6dohg51.
[2025-06-03 11:05:01] (0s)  >>> Created relation py_ssa_var/2@ff6793u4 with 26 rows and digest bdd73e72mtbi222odn0l9p7gpk4.
[2025-06-03 11:05:01] (0s)  >>> Created relation py_ssa_phi/2@6444c77f with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that SSA::SsaVariable.getAPhiInput/0#dispred#de01a47b/2@bedcdfct is empty, due to py_ssa_phi/2@6444c77f.
[2025-06-03 11:05:01] (0s) Inferred that project#py_ssa_phi/1@2c8f17nc is empty, due to py_ssa_phi/2@6444c77f.
[2025-06-03 11:05:01] (0s) Inferred that project#SSA::SsaVariable.getAPhiInput/0#dispred#de01a47b/1@eeeb222m is empty, due to SSA::SsaVariable.getAPhiInput/0#dispred#de01a47b/2@bedcdfct.
[2025-06-03 11:05:01] (0s) Inferred that SSA::SsaVariable.getAPhiInput/0#dispred#de01a47b_10#join_rhs/2@d9d87cid is empty, due to SSA::SsaVariable.getAPhiInput/0#dispred#de01a47b/2@bedcdfct.
[2025-06-03 11:05:01] (0s)  >>> Created relation py_ssa_defn/2@8908c5g8 with 23 rows and digest a7e46fgshvkq70sj0o9qqgiar80.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate SSA::SsaVariable.getDefinition/0#dispred#0643efdb/2@5cb908cb
[2025-06-03 11:05:01] (0s)  >>> Created relation SSA::SsaVariable.getDefinition/0#dispred#0643efdb/2@5cb908cb with 23 rows and digest a7e46fgshvkq70sj0o9qqgiar80.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate SSA::SsaVariable.getDefinition/0#dispred#0643efdb_0#antijoin_rhs/1@f32b0c5v
[2025-06-03 11:05:01] (0s)  >>> Created relation SSA::SsaVariable.getDefinition/0#dispred#0643efdb_0#antijoin_rhs/1@f32b0c5v with 23 rows and digest 976ebf79s3gtjnsphunf8fqiiq7.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::Scopes::maybe_undefined/1#564dc356/1@i1#9336ebcg (iteration 1)
[2025-06-03 11:05:01] (0s) 			 - Flow::Scopes::maybe_undefined/1#564dc356_delta has 3 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::Scopes::maybe_undefined/1#564dc356/1@i2#9336ebcg (iteration 2)
[2025-06-03 11:05:01] (0s) Empty delta for Flow::Scopes::maybe_undefined/1#564dc356_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Accumulating deltas
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::Scopes::maybe_undefined/1#564dc356/1@9336ebcg with 3 rows and digest df9d36k5u5gm3goq0h398o88mda.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate py_ssa_var_10#join_rhs/2@1a66c0th
[2025-06-03 11:05:01] (0s)  >>> Created relation py_ssa_var_10#join_rhs/2@1a66c0th with 26 rows and digest c05152138mbt3qae97a3d1c04k2.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate SSA::SsaVariable.getVariable/0#dispred#27f5054d/2@1be7adin
[2025-06-03 11:05:01] (0s)  >>> Created relation SSA::SsaVariable.getVariable/0#dispred#27f5054d/2@1be7adin with 26 rows and digest bdd73e72mtbi222odn0l9p7gpk4.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Flow::Scopes::maybe_undefined/1#564dc356_SSA::Ssa__#antijoin_rhs/1@89dc2bhf
[2025-06-03 11:05:01] (0s)  >>> Created relation _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Flow::Scopes::maybe_undefined/1#564dc356_SSA::Ssa__#antijoin_rhs/1@89dc2bhf with 3 rows and digest 0e4d32qmfj2o4589me9jq8hl3ue.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate m#Flow::ControlFlowNode.getEnclosingModule/0#dispred#8908be76#bf/1@c3129cdg
[2025-06-03 11:05:01] (0s)  >>> Created relation m#Flow::ControlFlowNode.getEnclosingModule/0#dispred#8908be76#bf/1@c3129cdg with 14 rows and digest ba49ef121vqtq8nvkf8gaacgi7e.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52_10#join_rhs/2@962a72kh
[2025-06-03 11:05:01] (0s)  >>> Created relation Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52_10#join_rhs/2@962a72kh with 3 rows and digest 3f07dbn480tq5tbkv2h8a4ao4f1.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce/2@i1#e3be43eh (iteration 1)
[2025-06-03 11:05:01] (0s) 			 - Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce_delta has 2 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce/2@i2#e3be43eh (iteration 2)
[2025-06-03 11:05:01] (0s) 			 - Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce_delta has 3 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce/2@i3#e3be43eh (iteration 3)
[2025-06-03 11:05:01] (0s) Empty delta for Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Accumulating deltas
[2025-06-03 11:05:01] (0s)  >>> Created relation Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce/2@e3be43eh with 5 rows and digest bbbbe70qd1pkp90q90lqrfg9ahf.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::ControlFlowNode.getEnclosingModule/0#dispred#8908be76#bf/2@1ad5c47n
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::ControlFlowNode.getEnclosingModule/0#dispred#8908be76#bf/2@1ad5c47n with 14 rows and digest d840be72fhrmssa02hakqfiu9l3.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Flow::Scopes::maybe_undefined/1#564dc356_SSA::Ssa__#antijoin_rhs#1/2@93c886rg
[2025-06-03 11:05:01] (0s)  >>> Created relation _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Flow::Scopes::maybe_undefined/1#564dc356_SSA::Ssa__#antijoin_rhs#1/2@93c886rg with 3 rows and digest 35a1a8un68hd565dd3k9qh3ug4f.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::Scopes::use_of_global_variable/3#a7f259dd/3@1bf1cdua
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::Scopes::use_of_global_variable/3#a7f259dd/3@1bf1cdua with 8 rows and digest 014ab0e1q4qi01dqtm9fhf858be.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::Scopes::use_of_global_variable/3#a7f259dd_120#join_rhs/3@7baf52g8
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::Scopes::use_of_global_variable/3#a7f259dd_120#join_rhs/3@7baf52g8 with 8 rows and digest a34010tfb700cmgjcv1pn5c3ag2.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::NameNode.uses/1#dispred#c58344eb/2@50caaf5s
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::NameNode.uses/1#dispred#c58344eb/2@50caaf5s with 11 rows and digest 73993c1gtsbaeua29n0gdu59og5.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::BasicBlock.dominates/1#dispred#47f3be21/2@5e4ac3fn
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::BasicBlock.dominates/1#dispred#47f3be21/2@5e4ac3fn with 5 rows and digest 7f5401bh6mkqqh43mpffq1s34n3.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::AttrNode.getObject/0#dispred#cba29223/2@1fce0566
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::AttrNode.getObject/0#dispred#cba29223/2@1fce0566 with 4 rows and digest e355afqtbkj3vgc6io0hlhti8m4.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate project#Flow::AttrNode.getObject/0#dispred#cba29223/1@dc476aoa
[2025-06-03 11:05:01] (0s)  >>> Created relation project#Flow::AttrNode.getObject/0#dispred#cba29223/1@dc476aoa with 4 rows and digest 685aab9poajmo0ds0a45e8b33o5.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::ControlFlowNode.isStore/0#dispred#bd620b20#b/1@45b5bdir
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::ControlFlowNode.isStore/0#dispred#bd620b20#b/1@45b5bdir with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that _Definitions::ModuleVariable#27049d81_Flow::AttrNode.getObject/0#dispred#cba29223_10#join_rhs_Flow::__#shared/2@c2b478lv is empty, due to Flow::ControlFlowNode.isStore/0#dispred#bd620b20#b/1@45b5bdir.
[2025-06-03 11:05:01] (0s) Inferred that SsaDefinitions::SsaSource::attribute_assignment_refinement/3#5f777592/3@b852f33i is empty, due to Flow::ControlFlowNode.isStore/0#dispred#bd620b20#b/1@45b5bdir.
[2025-06-03 11:05:01] (0s) Inferred that _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Variables::Variable.getScope/0#dispred#e3b1c704____#antijoin_rhs#2/2@cf55e9jv is empty, due to _Definitions::ModuleVariable#27049d81_Flow::AttrNode.getObject/0#dispred#cba29223_10#join_rhs_Flow::__#shared/2@c2b478lv.
[2025-06-03 11:05:01] (0s) Inferred that cached_SsaDefinitions::SsaSource::attribute_assignment_refinement/3#5f777592/3@f4c9688s is empty, due to SsaDefinitions::SsaSource::attribute_assignment_refinement/3#5f777592/3@b852f33i.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::NameNode.uses/1#dispred#c58344eb_10#join_rhs/2@643c36mi
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::NameNode.uses/1#dispred#c58344eb_10#join_rhs/2@643c36mi with 11 rows and digest 91f57crh9daiq42ra4qgrh0tf37.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Definitions::variable_or_attribute_defined_out_of_scope/1#fc011a99#b/1@61a058gi
[2025-06-03 11:05:01] (0s)  >>> Created relation Definitions::variable_or_attribute_defined_out_of_scope/1#fc011a99#b/1@61a058gi with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that Definitions::EscapingGlobalVariable#8f7a8d1d/1@a020c99i is empty, due to Definitions::variable_or_attribute_defined_out_of_scope/1#fc011a99#b/1@61a058gi.
[2025-06-03 11:05:01] (0s) Inferred that Definitions::EscapingGlobalVariable.scope_as_global_variable/0#2cfc0b40/2@d32219bj is empty, due to Definitions::EscapingGlobalVariable#8f7a8d1d/1@a020c99i.
[2025-06-03 11:05:01] (0s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#3#3/1@341d5031 is empty, due to Definitions::EscapingGlobalVariable.scope_as_global_variable/0#2cfc0b40/2@d32219bj.
[2025-06-03 11:05:01] (0s) Inferred that Definitions::EscapingGlobalVariable.scope_as_global_variable/0#2cfc0b40_10#join_rhs/2@908ae7qj is empty, due to Definitions::EscapingGlobalVariable.scope_as_global_variable/0#2cfc0b40/2@d32219bj.
[2025-06-03 11:05:01] (0s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#bounded/2@a4cbaaeb is empty, due to #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#3#3/1@341d5031.
[2025-06-03 11:05:01] (0s) Inferred that Definitions::EscapingGlobalVariable.innerScope/0#dispred#91670a16/2@59ab707m is empty, due to Definitions::EscapingGlobalVariable.scope_as_global_variable/0#2cfc0b40_10#join_rhs/2@908ae7qj.
[2025-06-03 11:05:01] (0s) Inferred that __Flow::CallNode#c5d1dc47_Flow::ControlFlowNode.getScope/0#dispred#b061daac#shared_doublyBoundedFast__#shared/2@6c0f1cul is empty, due to doublyBoundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:_#Scope::Scope.getScope/0#dispred#055e5112Plus#sourceBound#2#3#higher_order_body:#Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#3#3/2@a4cbaaeb.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate SsaDefinitions::SsaSource::import_star_refinement/3#1675f8e2/3@990b93tb
[2025-06-03 11:05:01] (0s)  >>> Created relation SsaDefinitions::SsaSource::import_star_refinement/3#1675f8e2/3@990b93tb with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that cached_SsaDefinitions::SsaSource::import_star_refinement/3#1675f8e2/3@39fa47h0 is empty, due to SsaDefinitions::SsaSource::import_star_refinement/3#1675f8e2/3@990b93tb.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Definitions::ModuleVariable.getAnImplicitUse/0#59874ece/2@ca93366q
[2025-06-03 11:05:01] (0s)  >>> Created relation Definitions::ModuleVariable.getAnImplicitUse/0#59874ece/2@ca93366q with 28 rows and digest d890barb75ci1s42dai5fvigfga.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Definitions::ModuleVariable_not_Definitions::EscapingGlobalVariable#1c8befd9/1@7fc1b5a3
[2025-06-03 11:05:01] (0s)  >>> Created relation Definitions::ModuleVariable_not_Definitions::EscapingGlobalVariable#1c8befd9/1@7fc1b5a3 with 24 rows and digest 572bc7ufe66b905pfd7lohim0s4.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getASourceUse/0#dispred#8daf149a/2@492bd0ae
[2025-06-03 11:05:01] (0s)  >>> Created relation Definitions::SsaSourceVariable.getASourceUse/0#dispred#8daf149a/2@492bd0ae with 11 rows and digest 91f57crh9daiq42ra4qgrh0tf37.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getAUse/0#dispred#84f49ef1/2@5962a68o
[2025-06-03 11:05:01] (0s)  >>> Created relation Definitions::SsaSourceVariable.getAUse/0#dispred#84f49ef1/2@5962a68o with 72 rows and digest f187363mlr598662ngv9el0kfgf.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getAUse/0#dispred#84f49ef1_10#join_rhs/2@5398fd51
[2025-06-03 11:05:01] (0s)  >>> Created relation Definitions::SsaSourceVariable.getAUse/0#dispred#84f49ef1_10#join_rhs/2@5398fd51 with 72 rows and digest 0d34800nv9a5q8ug90t8r00d753.
[2025-06-03 11:05:01] (0s)  >>> Created relation py_false_successors/2@470a4ecp with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that Flow::BasicBlock.getAFalseSuccessor/0#dispred#4022ff35/2@de4321i8 is empty, due to py_false_successors/2@470a4ecp.
[2025-06-03 11:05:01] (0s)  >>> Created relation py_true_successors/2@db6ceaic with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that Flow::BasicBlock.getATrueSuccessor/0#dispred#dce507ff/2@7b4d7dld is empty, due to py_true_successors/2@db6ceaic.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::ControlFlowNode.isBranch/0#dispred#7bdac72a/1@db5705km
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::ControlFlowNode.isBranch/0#dispred#7bdac72a/1@db5705km with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that #Flow::ControlFlowNode.getAChild/0#dispred#610e0803Plus#bf/2@0260a9a3 is empty, due to Flow::ControlFlowNode.isBranch/0#dispred#7bdac72a/1@db5705km.
[2025-06-03 11:05:01] (0s) Inferred that Base::test_contains/2#8f99225e/2@6b9502dt is empty, due to Flow::ControlFlowNode.isBranch/0#dispred#7bdac72a/1@db5705km.
[2025-06-03 11:05:01] (0s) Inferred that SsaDefinitions::SsaSource::test_refinement/3#2cbdf557/3@e584a6c6 is empty, due to Flow::ControlFlowNode.isBranch/0#dispred#7bdac72a/1@db5705km.
[2025-06-03 11:05:01] (0s) Inferred that project#Base::test_contains/2#8f99225e/1@58089flt is empty, due to Base::test_contains/2#8f99225e/2@6b9502dt.
[2025-06-03 11:05:01] (0s) Inferred that Base::test_contains/2#8f99225e_10#join_rhs/2@74835br1 is empty, due to Base::test_contains/2#8f99225e/2@6b9502dt.
[2025-06-03 11:05:01] (0s) Inferred that cached_SsaDefinitions::SsaSource::test_refinement/3#2cbdf557/3@c646bdum is empty, due to SsaDefinitions::SsaSource::test_refinement/3#2cbdf557/3@e584a6c6.
[2025-06-03 11:05:01] (0s) Inferred that Definitions::SsaSourceVariable.hasRefinementEdge/3#dispred#d08844b4/4@f74ef2bl is empty, due to Base::test_contains/2#8f99225e_10#join_rhs/2@74835br1.
[2025-06-03 11:05:01] (0s) Inferred that project#Definitions::SsaSourceVariable.hasRefinementEdge/3#dispred#d08844b4/3@4bc039v4 is empty, due to Definitions::SsaSourceVariable.hasRefinementEdge/3#dispred#d08844b4/4@f74ef2bl.
[2025-06-03 11:05:01] (0s) Inferred that SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@0cb54aqn is empty, due to project#Definitions::SsaSourceVariable.hasRefinementEdge/3#dispred#d08844b4/3@4bc039v4.
[2025-06-03 11:05:01] (0s) Inferred that Essa::TEssaEdgeDefinition#be4738bc/4@81064dgf is empty, due to SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@0cb54aqn.
[2025-06-03 11:05:01] (0s) Inferred that project#SsaCompute::EssaDefinitions::piNode/3#b1917f93/2@eacc13qp is empty, due to SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@0cb54aqn.
[2025-06-03 11:05:01] (0s) Inferred that cached_SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@3204beti is empty, due to SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@0cb54aqn.
[2025-06-03 11:05:01] (0s) Inferred that cached_Essa::TEssaEdgeDefinition#be4738bc/4@2da913f5 is empty, due to Essa::TEssaEdgeDefinition#be4738bc/4@81064dgf.
[2025-06-03 11:05:01] (0s) Inferred that project#SsaCompute::EssaDefinitions::piNode/3#b1917f93_10#join_rhs/2@65def8pf is empty, due to project#SsaCompute::EssaDefinitions::piNode/3#b1917f93/2@eacc13qp.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Flow::CallNode#c5d1dc47_Flow::ControlFlowNode.getBasicBlock/0#dispred#32502ca1_py_flow_bb_node#shared/3@e69f40fg
[2025-06-03 11:05:01] (0s)  >>> Created relation _Flow::CallNode#c5d1dc47_Flow::ControlFlowNode.getBasicBlock/0#dispred#32502ca1_py_flow_bb_node#shared/3@e69f40fg with 7 rows and digest 95dd42d3556p0bsoom06d53qu0e.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::CallNode.getArg/1#dispred#044295cc/3@f46861pq
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::CallNode.getArg/1#dispred#044295cc/3@f46861pq with 5 rows and digest 9d21dc6jrddft7c0ojbve61bsk4.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::CallNode.getArg/1#dispred#044295cc_102#join_rhs/3@06a9058a
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::CallNode.getArg/1#dispred#044295cc_102#join_rhs/3@06a9058a with 5 rows and digest 10d308fb760gm3489h6nuncl9s7.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::CallNode.getFunction/0#dispred#cc4c2bc8/2@99e0a0g4
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::CallNode.getFunction/0#dispred#cc4c2bc8/2@99e0a0g4 with 7 rows and digest 5c9767sd61140jie0u2t0no76vf.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9/3@b3077aai
[2025-06-03 11:05:01] (0s)  >>> Created relation SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9/3@b3077aai with 2 rows and digest ad8cbfggshaf5jrsdmflrimdjmc.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9_02#antijoin_rhs/2@086037ue
[2025-06-03 11:05:01] (0s)  >>> Created relation SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9_02#antijoin_rhs/2@086037ue with 2 rows and digest ffaf53k6b0r7a7m2u86817afjif.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate SsaDefinitions::SsaSource::argument_refinement/3#615fb3a9/3@c0a40blj
[2025-06-03 11:05:01] (0s)  >>> Created relation SsaDefinitions::SsaSource::argument_refinement/3#615fb3a9/3@c0a40blj with 1 rows and digest d22634250nvm4lnndccm7b5ni39.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Flow::DeletionNode.getTarget/0#dispred#2931681b/2@900905hn
[2025-06-03 11:05:01] (0s)  >>> Created relation Flow::DeletionNode.getTarget/0#dispred#2931681b/2@900905hn with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that SsaDefinitions::SsaSource::attribute_deletion_refinement/3#7de1bd7a/3@855894tv is empty, due to Flow::DeletionNode.getTarget/0#dispred#2931681b/2@900905hn.
[2025-06-03 11:05:01] (0s) Inferred that cached_SsaDefinitions::SsaSource::attribute_deletion_refinement/3#7de1bd7a/3@bc7edf3e is empty, due to SsaDefinitions::SsaSource::attribute_deletion_refinement/3#7de1bd7a/3@855894tv.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Definitions::ModuleVariable.getScopeEntryDefinition/0#71ddbc6f/2@788c8ebq
[2025-06-03 11:05:01] (0s)  >>> Created relation Definitions::ModuleVariable.getScopeEntryDefinition/0#71ddbc6f/2@788c8ebq with 27 rows and digest a1ccf5g11eafg742bmpouf3t7n4.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs_py_flow_bb_node_10#join_rhs#shared/1@a937a3qn
[2025-06-03 11:05:01] (0s)  >>> Created relation _py_exprs_10#join_rhs_py_flow_bb_node_10#join_rhs#shared/1@a937a3qn with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that Flow::ListNode#fccc80d7/1@273596u4 is empty, due to _py_exprs_10#join_rhs_py_flow_bb_node_10#join_rhs#shared/1@a937a3qn.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate m#Flow::SequenceNode.SequenceNode#b8039542#b/1@0ef2ddc9
[2025-06-03 11:05:01] (0s)  >>> Created relation m#Flow::SequenceNode.SequenceNode#b8039542#b/1@0ef2ddc9 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that Flow::SequenceNode.SequenceNode#b8039542#b/1@7920bcg1 is empty, due to m#Flow::SequenceNode.SequenceNode#b8039542#b/1@0ef2ddc9.
[2025-06-03 11:05:01] (0s) Inferred that Flow::SequenceNode.getElement/1#dispred#4cc4068f/3@49e480so is empty, due to Flow::SequenceNode.SequenceNode#b8039542#b/1@7920bcg1.
[2025-06-03 11:05:01] (0s) Inferred that Flow::SequenceNode.getElement/1#dispred#4cc4068f_201#join_rhs/3@968118v4 is empty, due to Flow::SequenceNode.getElement/1#dispred#4cc4068f/3@49e480so.
[2025-06-03 11:05:01] (0s) Inferred that cached_Flow::SequenceNode.getElement/1#dispred#4cc4068f/3@cc5e78ka is empty, due to Flow::SequenceNode.getElement/1#dispred#4cc4068f/3@49e480so.
[2025-06-03 11:05:01] (0s) Inferred that SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/4@36bf64pa is empty, due to Flow::SequenceNode.getElement/1#dispred#4cc4068f_201#join_rhs/3@968118v4.
[2025-06-03 11:05:01] (0s) Inferred that project#SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/2@704beekm is empty, due to SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/4@36bf64pa.
[2025-06-03 11:05:01] (0s) Inferred that cached_SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/4@89e653d3 is empty, due to SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/4@36bf64pa.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getASourceUse/0#dispred#8daf149a_10#join_rhs/2@5f403a1i
[2025-06-03 11:05:01] (0s)  >>> Created relation Definitions::SsaSourceVariable.getASourceUse/0#dispred#8daf149a_10#join_rhs/2@5f403a1i with 11 rows and digest 73993c1gtsbaeua29n0gdu59og5.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19/4@939457gr
[2025-06-03 11:05:01] (0s)  >>> Created relation SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19/4@939457gr with 11 rows and digest 1d9d42qqmmmesn7cdld5hcnqfke.
[2025-06-03 11:05:01] (0s) No need to promote strings for predicate SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9  as it does not contain computed strings.
[2025-06-03 11:05:01] (0s)  >>> Created relation cached_SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9/3@ec7d91se with 2 rows and digest ad8cbfggshaf5jrsdmflrimdjmc.
[2025-06-03 11:05:01] (0s) No need to promote strings for predicate SsaDefinitions::SsaSource::argument_refinement/3#615fb3a9  as it does not contain computed strings.
[2025-06-03 11:05:01] (0s)  >>> Created relation cached_SsaDefinitions::SsaSource::argument_refinement/3#615fb3a9/3@2d844778 with 1 rows and digest d22634250nvm4lnndccm7b5ni39.
[2025-06-03 11:05:01] (0s) No need to promote strings for predicate SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19  as it does not contain computed strings.
[2025-06-03 11:05:01] (0s)  >>> Created relation cached_SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19/4@271bba4s with 11 rows and digest 1d9d42qqmmmesn7cdld5hcnqfke.
[2025-06-03 11:05:01] (0s)  >>> Created relation py_ints/2@866b74k2 with 2 rows and digest 382f59sg2nvab80at252ctfr2ie.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _py_flags_versioned#shared/5@5fdafb5i
[2025-06-03 11:05:01] (0s)  >>> Created relation _py_flags_versioned#shared/5@5fdafb5i with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that __py_flags_versioned#shared#antijoin_rhs/1@9b13e686 is empty, due to _py_flags_versioned#shared/5@5fdafb5i.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate ___py_flags_versioned#shared#antijoin_rhs__py_flags_versioned#shared_py_flags_versioned#shared/2@2bd776gb
[2025-06-03 11:05:01] (0s)  >>> Created relation ___py_flags_versioned#shared#antijoin_rhs__py_flags_versioned#shared_py_flags_versioned#shared/2@2bd776gb with 1 rows and digest 42abdfi2685ce7s7boi4297dl89.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Constants::latest_supported_minor_version/1#983c5ab9/2@7f5a9a9f
[2025-06-03 11:05:01] (0s)  >>> Created relation Constants::latest_supported_minor_version/1#983c5ab9/2@7f5a9a9f with 2 rows and digest f0ef517kobdu4l809gckpoka73a.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Constants::latest_supported_minor_version/1#983c5ab9____py_flags_versioned#shared#antijoin_rhs__py___#antijoin_rhs/2@bd8ed8u5
[2025-06-03 11:05:01] (0s)  >>> Created relation _Constants::latest_supported_minor_version/1#983c5ab9____py_flags_versioned#shared#antijoin_rhs__py___#antijoin_rhs/2@bd8ed8u5 with 1 rows and digest 42abdfi2685ce7s7boi4297dl89.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Constants::major_version/0#2c8abd7c/1@ba2f3aqb
[2025-06-03 11:05:01] (0s)  >>> Created relation Constants::major_version/0#2c8abd7c/1@ba2f3aqb with 1 rows and digest 1a7690ikgoip19a1kgio2vkcn37.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate project#Import::ImportExpr.implicitRelativeImportsAllowed/0#dispred#2a9b35e7#nullary/0@4aa54bn8
[2025-06-03 11:05:01] (0s)  >>> Created relation project#Import::ImportExpr.implicitRelativeImportsAllowed/0#dispred#2a9b35e7#nullary/0@4aa54bn8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that _project#Import::ImportExpr.implicitRelativeImportsAllowed/0#dispred#2a9b35e7#nullary_py_exprs_10#jo__#shared/1@2089b042 is empty, due to project#Import::ImportExpr.implicitRelativeImportsAllowed/0#dispred#2a9b35e7#nullary/0@4aa54bn8.
[2025-06-03 11:05:01] (0s) Inferred that Import::ImportExpr.implicitRelativeImportsAllowed/0#dispred#2a9b35e7/1@ddeb74hf is empty, due to _project#Import::ImportExpr.implicitRelativeImportsAllowed/0#dispred#2a9b35e7#nullary_py_exprs_10#jo__#shared/1@2089b042.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Import::ImportExpr.getLevel/0#dispred#68c28689/2@ffde2a1s
[2025-06-03 11:05:01] (0s)  >>> Created relation Import::ImportExpr.getLevel/0#dispred#68c28689/2@ffde2a1s with 2 rows and digest 364b65u33chk152estcauqd51v4.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Import::ImportExpr.getLevel/0#dispred#68c28689_10#join_rhs/2@d70219qk
[2025-06-03 11:05:01] (0s)  >>> Created relation Import::ImportExpr.getLevel/0#dispred#68c28689_10#join_rhs/2@d70219qk with 2 rows and digest 382f59sg2nvab80at252ctfr2ie.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62_AstGenerated::ImportExpr_.getName/0#dispred#bfa__#join_rhs/2@adb6af0r
[2025-06-03 11:05:01] (0s)  >>> Created relation _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62_AstGenerated::ImportExpr_.getName/0#dispred#bfa__#join_rhs/2@adb6af0r with 2 rows and digest 237bea5r6sqm34b5g91k7d88vp3.
[2025-06-03 11:05:01] (0s)  >>> Created relation sourceLocationPrefix/1@64802cd1 with 1 rows and digest 8a3ae6qhdnd8d74ovm5e2odhl7a.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate m#Files::Impl::Container.getRelativePath/0#dispred#21f7a09a#bf/1@85688236
[2025-06-03 11:05:01] (0s)  >>> Created relation m#Files::Impl::Container.getRelativePath/0#dispred#21f7a09a#bf/1@85688236 with 5 rows and digest d6c8d9t41qnqkdr5069pol1u9a0.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Files::Impl::Container.getRelativePath/0#dispred#21f7a09a#bf/2@34eacem9
[2025-06-03 11:05:01] (0s)  >>> Created relation Files::Impl::Container.getRelativePath/0#dispred#21f7a09a#bf/2@34eacem9 with 5 rows and digest 7017d4bvsc2apg933rlf7m34i38.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate project#Files::Impl::Container.getRelativePath/0#dispred#21f7a09a#bf/1@53f767h8
[2025-06-03 11:05:01] (0s)  >>> Created relation project#Files::Impl::Container.getRelativePath/0#dispred#21f7a09a#bf/1@53f767h8 with 5 rows and digest d6c8d9t41qnqkdr5069pol1u9a0.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate _Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_201#join_rhs#antijoin_rhs/1@502bf159
[2025-06-03 11:05:01] (0s)  >>> Created relation _Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_201#join_rhs#antijoin_rhs/1@502bf159 with 2 rows and digest b5bfffbaho3dj5e9ec2vmiedsme.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Files::File.isPossibleEntryPoint/0#dispred#4586fa29#b/1@eddd4a28
[2025-06-03 11:05:01] (0s)  >>> Created relation Files::File.isPossibleEntryPoint/0#dispred#4586fa29#b/1@eddd4a28 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that Module::transitively_imported_from_entry_point/1#96984e0d/1@3a5985nn is empty, due to Files::File.isPossibleEntryPoint/0#dispred#4586fa29#b/1@eddd4a28.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Module::moduleNameFromFile/1#a01d5f51/2@i1#78e23656 (iteration 1)
[2025-06-03 11:05:01] (0s) Empty delta for Module::moduleNameFromFile/1#a01d5f51_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Accumulating deltas
[2025-06-03 11:05:01] (0s)  >>> Created relation Module::moduleNameFromFile/1#a01d5f51/2@78e23656 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that cached_Module::moduleNameFromFile/1#a01d5f51/2@010e4f4r is empty, due to Module::moduleNameFromFile/1#a01d5f51/2@78e23656.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Module::Module.getName/0#dispred#45a481b3/2@f0a518q4
[2025-06-03 11:05:01] (0s)  >>> Created relation Module::Module.getName/0#dispred#45a481b3/2@f0a518q4 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (0s) Inferred that Module::Module.getName/0#dispred#45a481b3_10#join_rhs/2@751ef928 is empty, due to Module::Module.getName/0#dispred#45a481b3/2@f0a518q4.
[2025-06-03 11:05:01] (0s) Inferred that Module::Module.getPackageName/0#dispred#bb0c3872/2@0be635sq is empty, due to Module::Module.getName/0#dispred#45a481b3/2@f0a518q4.
[2025-06-03 11:05:01] (0s) Inferred that project#Module::Module.getName/0#dispred#45a481b3#2/1@e54441pb is empty, due to Module::Module.getName/0#dispred#45a481b3/2@f0a518q4.
[2025-06-03 11:05:01] (0s) Inferred that Module::Module.getPackage/0#dispred#0378d53e/2@b6130els is empty, due to Module::Module.getPackageName/0#dispred#bb0c3872/2@0be635sq.
[2025-06-03 11:05:01] (0s) Inferred that Module::Module.getSubModule/1#dispred#e658b323/3@1c4040ub is empty, due to Module::Module.getPackage/0#dispred#0378d53e/2@b6130els.
[2025-06-03 11:05:01] (0s) Inferred that project#Module::Module.getSubModule/1#dispred#e658b323/1@4ae2c5tj is empty, due to Module::Module.getSubModule/1#dispred#e658b323/3@1c4040ub.
[2025-06-03 11:05:01] (0s) Inferred that project#Module::Module.getSubModule/1#dispred#e658b323#3/2@0a7227fj is empty, due to Module::Module.getSubModule/1#dispred#e658b323/3@1c4040ub.
[2025-06-03 11:05:01] (0s) Inferred that Definitions::SsaSourceVariable.getName/0#dispred#6acf6d7c#fb/2@41ee66kv is empty, due to project#Module::Module.getSubModule/1#dispred#e658b323/1@4ae2c5tj.
[2025-06-03 11:05:01] (0s) Inferred that SsaDefinitions::SsaSource::init_module_submodule_defn/2#64b1af7d/2@7b14fb10 is empty, due to project#Module::Module.getSubModule/1#dispred#e658b323#3/2@0a7227fj.
[2025-06-03 11:05:01] (0s) Inferred that Definitions::SsaSourceVariable.getName/0#dispred#6acf6d7c#fb_10#join_rhs/2@f2a8f4se is empty, due to Definitions::SsaSourceVariable.getName/0#dispred#6acf6d7c#fb/2@41ee66kv.
[2025-06-03 11:05:01] (0s) Inferred that Module::Module.isPackageInit/0#dispred#56393e5c#b/1@fb3f2580 is empty, due to Definitions::SsaSourceVariable.getName/0#dispred#6acf6d7c#fb_10#join_rhs/2@f2a8f4se.
[2025-06-03 11:05:01] (0s) Inferred that Essa::ImplicitSubModuleDefinition#5d33e2dd/1@f1a14cu2 is empty, due to SsaDefinitions::SsaSource::init_module_submodule_defn/2#64b1af7d/2@7b14fb10.
[2025-06-03 11:05:01] (0s) Inferred that cached_SsaDefinitions::SsaSource::init_module_submodule_defn/2#64b1af7d/2@2761bd9c is empty, due to SsaDefinitions::SsaSource::init_module_submodule_defn/2#64b1af7d/2@7b14fb10.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Module::Module.toString/0#dispred#96ef98d9/2@eb913cp1
[2025-06-03 11:05:01] (0s)  >>> Created relation Module::Module.toString/0#dispred#96ef98d9/2@eb913cp1 with 2 rows and digest 82dcd9fgh3fk9f244feiabk0tk0.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Exprs::Expr.toString/0#dispred#7435dfcd/2@i1#77d4ax0j (iteration 1)
[2025-06-03 11:05:01] (0s) 			 - Exprs::Expr.toString/0#dispred#7435dfcd_delta has 79 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::AstNode_.toString/0#dispred#192f27b5/2@i1#77d4aw0j (iteration 1)
[2025-06-03 11:05:01] (0s) 			 - AstGenerated::AstNode_.toString/0#dispred#192f27b5_delta has 43 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Exprs::Expr.toString/0#dispred#7435dfcd/2@i2#77d4ax0j (iteration 2)
[2025-06-03 11:05:01] (0s) 			 - Exprs::Expr.toString/0#dispred#7435dfcd_delta has 7 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::AstNode_.toString/0#dispred#192f27b5/2@i2#77d4aw0j (iteration 2)
[2025-06-03 11:05:01] (0s) 			 - AstGenerated::AstNode_.toString/0#dispred#192f27b5_delta has 79 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Exprs::Expr.toString/0#dispred#7435dfcd/2@i3#77d4ax0j (iteration 3)
[2025-06-03 11:05:01] (0s) Empty delta for Exprs::Expr.toString/0#dispred#7435dfcd_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::AstNode_.toString/0#dispred#192f27b5/2@i3#77d4aw0j (iteration 3)
[2025-06-03 11:05:01] (0s) 			 - AstGenerated::AstNode_.toString/0#dispred#192f27b5_delta has 7 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate Exprs::Expr.toString/0#dispred#7435dfcd/2@i4#77d4ax0j (iteration 4)
[2025-06-03 11:05:01] (0s) Empty delta for Exprs::Expr.toString/0#dispred#7435dfcd_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (0s) Accumulating deltas
[2025-06-03 11:05:01] (0s)  >>> Created relation AstGenerated::AstNode_.toString/0#dispred#192f27b5/2@77d4aw0j with 129 rows and digest 536b95bb0ktc0i4bboi3u467122.
[2025-06-03 11:05:01] (0s)  >>> Created relation Exprs::Expr.toString/0#dispred#7435dfcd/2@77d4ax0j with 86 rows and digest c43300spki6g1kqtglo00fbtgk2.
[2025-06-03 11:05:01] (0s) Promoting strings for predicate Exprs::Expr.toString/0#dispred#7435dfcd
[2025-06-03 11:05:01] (0s) Promoted strings in predicate Exprs::Expr.toString/0#dispred#7435dfcd in memory, took 1ms
[2025-06-03 11:05:01] (0s) Saving stringpool to save strings from predicate Exprs::Expr.toString/0#dispred#7435dfcd
[2025-06-03 11:05:01] (0s) Saved stringpool to save strings from predicate Exprs::Expr.toString/0#dispred#7435dfcd, took 0ms
[2025-06-03 11:05:01] (0s)  >>> Created relation cached_Exprs::Expr.toString/0#dispred#7435dfcd/2@a8c03872 with 86 rows and digest c43300spki6g1kqtglo00fbtgk2.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate AstGenerated::Scope_.toString/0#dispred#8b6e5a05/2@bb0e1096
[2025-06-03 11:05:01] (0s)  >>> Created relation AstGenerated::Scope_.toString/0#dispred#8b6e5a05/2@bb0e1096 with 5 rows and digest 0b4aaa486sbg8ecq5e7qbjc6f9f.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate project#AstGenerated::Scope_.toString/0#dispred#8b6e5a05/1@73d698f9
[2025-06-03 11:05:01] (0s)  >>> Created relation project#AstGenerated::Scope_.toString/0#dispred#8b6e5a05/1@73d698f9 with 5 rows and digest 02de80n4di8hr38mqovcu5vedg8.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate project#py_scope_flow/1@48c253lo
[2025-06-03 11:05:01] (0s)  >>> Created relation project#py_scope_flow/1@48c253lo with 5 rows and digest 02de80n4di8hr38mqovcu5vedg8.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entity/2@24aee2sg
[2025-06-03 11:05:01] (0s)  >>> Created relation disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entity/2@24aee2sg with 127 rows and digest 0ff003utmn1r571p3qqg9bkklc7.
[2025-06-03 11:05:01] (0s) Starting to evaluate predicate #disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entityPlus#bf/2@i1#04af58l7 (iteration 1)
[2025-06-03 11:05:01] (0s) 			 - #disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entityPlus#bf_delta has 3 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate #disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entityPlus#bf/2@i2#04af58l7 (iteration 2)
[2025-06-03 11:05:01] (1s) Empty delta for #disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entityPlus#bf_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Accumulating deltas
[2025-06-03 11:05:01] (1s)  >>> Created relation #disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entityPlus#bf/2@04af58l7 with 3 rows and digest 3d8634v8d46grf7q7k4arqnaege.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Definitions::SsaSourceVariable.getScopeEntryDefinition/0#dispred#6adeb52f/2@c3444fid
[2025-06-03 11:05:01] (1s)  >>> Created relation Definitions::SsaSourceVariable.getScopeEntryDefinition/0#dispred#6adeb52f/2@c3444fid with 32 rows and digest 061eb88coj3debb88unqmcia5k8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Exprs::Expr.getASubExpression/0#dispred#443395f9_10#join_rhs/2@f87c9dtu
[2025-06-03 11:05:01] (1s)  >>> Created relation Exprs::Expr.getASubExpression/0#dispred#443395f9_10#join_rhs/2@f87c9dtu with 36 rows and digest 98f4bd22m73shcav49bom4l6kbf.
[2025-06-03 11:05:01] (1s)  >>> Created relation m##Exprs::Expr.getASubExpression/0#dispred#443395f9Plus#fb/1@640ea5i9 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (1s) Inferred that #Exprs::Expr.getASubExpression/0#dispred#443395f9Plus#fb#flipped/2@95abd0rl is empty, due to m##Exprs::Expr.getASubExpression/0#dispred#443395f9Plus#fb/1@640ea5i9.
[2025-06-03 11:05:01] (1s) Inferred that #Exprs::Expr.getASubExpression/0#dispred#443395f9Plus#fb#flipped_10#join_rhs/2@852481hd is empty, due to #Exprs::Expr.getASubExpression/0#dispred#443395f9Plus#fb#flipped/2@95abd0rl.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Flow::nested_sequence_assign/4#08cf2807/4@i1#9bd516ft (iteration 1)
[2025-06-03 11:05:01] (1s) Empty delta for Flow::nested_sequence_assign/4#08cf2807_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Accumulating deltas
[2025-06-03 11:05:01] (1s)  >>> Created relation Flow::nested_sequence_assign/4#08cf2807/4@9bd516ft with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Flow::assigned_value/1#97af2d4b#bf/2@2fecfdv1
[2025-06-03 11:05:01] (1s)  >>> Created relation Flow::assigned_value/1#97af2d4b#bf/2@2fecfdv1 with 23 rows and digest 577b6c5l9h6lpcf24giojragtmd.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee_10#join_rhs/2@e86b40f3
[2025-06-03 11:05:01] (1s)  >>> Created relation AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee_10#join_rhs/2@e86b40f3 with 109 rows and digest d2fde9brp2c43s5v0ont1nd4fq8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Flow::DefinitionNode.getValue/0#dispred#e8c0c58e#bf/2@3244560u
[2025-06-03 11:05:01] (1s)  >>> Created relation Flow::DefinitionNode.getValue/0#dispred#e8c0c58e#bf/2@3244560u with 23 rows and digest 6b236fgscqhj946k8v0fom4ftc4.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/3@d174ac60
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/3@d174ac60 with 23 rows and digest f0d941po4rfm4pghgansasjk06a.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/2@8ef90csr
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/2@8ef90csr with 23 rows and digest f2acc0gp8r1c9s0q3t963jbffs1.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482/2@6968db4t
[2025-06-03 11:05:01] (1s)  >>> Created relation Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482/2@6968db4t with 55 rows and digest 53cb922crm4bvvtt6ffguevd86c.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482_10#join_rhs/2@aacec1qn
[2025-06-03 11:05:01] (1s)  >>> Created relation Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482_10#join_rhs/2@aacec1qn with 55 rows and digest ee72bdj8ei5sdve31b2o13hmvma.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableDefine/4#79261f91/4@f9882707
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::variableDefine/4#79261f91/4@f9882707 with 55 rows and digest f0af40jc4tshc9ft2i0lclbnc9e.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::variableDefine/4#79261f91  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::variableDefine/4#79261f91/4@1e9ed9n1 with 55 rows and digest f0af40jc4tshc9ft2i0lclbnc9e.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/3@5c11868o with 23 rows and digest f0d941po4rfm4pghgansasjk06a.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482/1@e5a0e8v6
[2025-06-03 11:05:01] (1s)  >>> Created relation project#Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482/1@e5a0e8v6 with 29 rows and digest 5e0171i5cla9lpu4ggq59ku31l6.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Definitions::SpecialSsaSourceVariable.scope_as_global_variable/0#d3f1993c/2@c09632d0
[2025-06-03 11:05:01] (1s)  >>> Created relation Definitions::SpecialSsaSourceVariable.scope_as_global_variable/0#d3f1993c/2@c09632d0 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (1s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#5#3/1@a1e01c00 is empty, due to Definitions::SpecialSsaSourceVariable.scope_as_global_variable/0#d3f1993c/2@c09632d0.
[2025-06-03 11:05:01] (1s) Inferred that Definitions::SpecialSsaSourceVariable.scope_as_global_variable/0#d3f1993c_10#join_rhs/2@c21d86ob is empty, due to Definitions::SpecialSsaSourceVariable.scope_as_global_variable/0#d3f1993c/2@c09632d0.
[2025-06-03 11:05:01] (1s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#bounded#3/2@f1a718de is empty, due to #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#5#3/1@a1e01c00.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Definitions::SsaSourceVariable.redefinedAtCallSite/0#dispred#2b69e25a/2@016dcfh8
[2025-06-03 11:05:01] (1s)  >>> Created relation Definitions::SsaSourceVariable.redefinedAtCallSite/0#dispred#2b69e25a/2@016dcfh8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Definitions::refinement/3#4b377430/3@fc74a9bs
[2025-06-03 11:05:01] (1s)  >>> Created relation Definitions::refinement/3#4b377430/3@fc74a9bs with 3 rows and digest 05f28aasp2rrs8rtjk5c3lq68m5.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd/3@6452e6kk
[2025-06-03 11:05:01] (1s)  >>> Created relation Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd/3@6452e6kk with 3 rows and digest 05f28aasp2rrs8rtjk5c3lq68m5.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd#2/2@a5046a55
[2025-06-03 11:05:01] (1s)  >>> Created relation project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd#2/2@a5046a55 with 3 rows and digest 08ec72te6cgagg3mc92iuu7dok6.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd#2_10#join_rhs/2@a13ecfhj
[2025-06-03 11:05:01] (1s)  >>> Created relation project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd#2_10#join_rhs/2@a13ecfhj with 3 rows and digest 9c66eb9kvmb5qipo0kun9m4ptj8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableUse/4#da62dc30/4@f2c3b95u
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::variableUse/4#da62dc30/4@f2c3b95u with 72 rows and digest e061381rd58uh7kttr3uorvp9nd.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd/2@a9dc37nb
[2025-06-03 11:05:01] (1s)  >>> Created relation project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd/2@a9dc37nb with 3 rows and digest 9f055aq32pl9ji7icu80aacfgk0.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd_10#join_rhs/2@6f6e5a5o
[2025-06-03 11:05:01] (1s)  >>> Created relation project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd_10#join_rhs/2@6f6e5a5o with 3 rows and digest db0da9vtcnp1tolmfhllblj2in6.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableRefine/4#b20a3c26/4@fe03852h
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::variableRefine/4#b20a3c26/4@fe03852h with 3 rows and digest 6e2619ol47gc8sosqs0cvrtjhhb.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e/4@a68ed5g3
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e/4@a68ed5g3 with 58 rows and digest 73b2be1t55j4ie8mesec8bbrqg7.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e/4@8a1c0f76 with 58 rows and digest 73b2be1t55j4ie8mesec8bbrqg7.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::variableRefine/4#b20a3c26  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::variableRefine/4#b20a3c26/4@209e7767 with 3 rows and digest 6e2619ol47gc8sosqs0cvrtjhhb.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::variableUse/4#da62dc30  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::variableUse/4#da62dc30/4@ce02f549 with 72 rows and digest e061381rd58uh7kttr3uorvp9nd.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate _SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e_SsaCompute::SsaComputeImpl::variableUse/4#da62dc__#rank_range/3@890f13f9
[2025-06-03 11:05:01] (1s)  >>> Created relation _SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e_SsaCompute::SsaComputeImpl::variableUse/4#da62dc__#rank_range/3@890f13f9 with 130 rows and digest 121cd6vs8ev8pal1jtdljc0r103.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate _SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e_SsaCompute::SsaComputeImpl::variableUse/4#da62dc__#rank_term/4@5c208a60
[2025-06-03 11:05:01] (1s)  >>> Created relation _SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e_SsaCompute::SsaComputeImpl::variableUse/4#da62dc__#rank_term/4@5c208a60 with 130 rows and digest dd86fbqu644cgtm77vnp0cubl70.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/4@b3befalb
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/4@b3befalb with 130 rows and digest 25d73cg3gqaj39ogc9acq623hl8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs/4@c21f6564
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs/4@c21f6564 with 130 rows and digest bb8080ct9skq6jhgsnrgotovlr1.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#3/3@b3c54bs6
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#3/3@b3c54bs6 with 130 rows and digest ab827epgag5epterrt0qkg8f94c.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::defRank/4#f608ea69/4@da81e32q
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::defRank/4#f608ea69/4@da81e32q with 58 rows and digest 0850c7pms3v3ogckuivs8tu7hl6.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate _SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variabl__#shared#1/6@8e8ed026
[2025-06-03 11:05:01] (1s)  >>> Created relation _SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variabl__#shared#1/6@8e8ed026 with 3 rows and digest f14fcb4l6p4vsa8nkr0etfub75b.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defRank/4#f608ea69#2/3@2a826fo0
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defRank/4#f608ea69#2/3@2a826fo0 with 58 rows and digest fb7db3eus18ceanc9mr02egef23.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#2/5@812be6br
[2025-06-03 11:05:01] (1s)  >>> Created relation __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#2/5@812be6br with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_201#join_rhs/3@ccdbd5s2
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_201#join_rhs/3@ccdbd5s2 with 130 rows and digest 176e3eegojabu9jar9tbs2hhocb.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/2@28e2d9fe
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/2@28e2d9fe with 37 rows and digest ae7259tm0agvnmn3udbp9ohkaie.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#2/2@e6c50ejn
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#2/2@e6c50ejn with 37 rows and digest ae7259tm0agvnmn3udbp9ohkaie.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::defRank/4#f608ea69_201#join_rhs/3@9e56cd2f
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::defRank/4#f608ea69_201#join_rhs/3@9e56cd2f with 58 rows and digest 77a7a9eqvuk7gvs62n7qtijvn8e.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defRank/4#f608ea69/2@acb191tk
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defRank/4#f608ea69/2@acb191tk with 32 rows and digest 061eb88coj3debb88unqmcia5k8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@i1#34c6exe2 (iteration 1)
[2025-06-03 11:05:01] (1s) Empty delta for SsaCompute::Liveness::liveAtExit/2#b6aa63f4_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::Liveness::liveAtEntry/2#bab3ea7c/2@i1#34c6ewe2 (iteration 1)
[2025-06-03 11:05:01] (1s) 			 - SsaCompute::Liveness::liveAtEntry/2#bab3ea7c_delta has 5 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@i2#34c6exe2 (iteration 2)
[2025-06-03 11:05:01] (1s) Empty delta for SsaCompute::Liveness::liveAtExit/2#b6aa63f4_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Accumulating deltas
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::Liveness::liveAtEntry/2#bab3ea7c/2@34c6ewe2 with 5 rows and digest fd3201pqqeb2dloh2qrkt53s6b4.
[2025-06-03 11:05:01] (1s)  >>> Discarded freshly computed SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@34c6exe2, preempted by a cache hit with digest THIS-RELATION-HAS-NO-TUPLES
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#3/5@4e74ffee
[2025-06-03 11:05:01] (1s)  >>> Created relation __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#3/5@4e74ffee with 2 rows and digest e6ae700f7ic81tck23ofmlm6co9.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::EssaDefinitions::variableRefinement/5#4e891107/5@7592ec5c
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::EssaDefinitions::variableRefinement/5#4e891107/5@7592ec5c with 2 rows and digest ab4479eommd6ggr8umnpqk7ohj8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Essa::TEssaNodeRefinement#dom#8a14bc22/3@96c416k8
[2025-06-03 11:05:01] (1s)  >>> Created relation Essa::TEssaNodeRefinement#dom#8a14bc22/3@96c416k8 with 2 rows and digest 2498afil07ch0pm9hmqeub4m9k7.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Essa::TEssaNodeRefinement#8de2de42/4@1c2186sa
[2025-06-03 11:05:01] Evaluating HOP construct<Essa#24e22a14::TEssaDefinition,1> with inputs:
                        2 tuples in Essa::TEssaNodeRefinement#dom#8a14bc22/3@96c416k8
[2025-06-03 11:05:01] (1s)  >>> Created relation Essa::TEssaNodeRefinement#8de2de42/4@1c2186sa with 2 rows and digest bade7a0ldoksn7dfcusa4udfc49.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate _SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variabl__#shared/6@531e5ab9
[2025-06-03 11:05:01] (1s)  >>> Created relation _SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variabl__#shared/6@531e5ab9 with 55 rows and digest 3af05cunh9msqjn85gf27la84cf.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs/5@bef0ecvk
[2025-06-03 11:05:01] (1s)  >>> Created relation __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs/5@bef0ecvk with 16 rows and digest 4d5b43jloldcautlqmohfgsco78.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#1/5@9354301l
[2025-06-03 11:05:01] (1s)  >>> Created relation __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#1/5@9354301l with 55 rows and digest 9ee2d740u51ns3kj4gqsp9i02m6.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::EssaDefinitions::variableDefinition/5#32b5d84f/5@a9149de0
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::EssaDefinitions::variableDefinition/5#32b5d84f/5@a9149de0 with 39 rows and digest b6eec00j5rmih9an5e7kei04o70.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Essa::TEssaNodeDefinition#dom#e1989adc/3@8ace0c61
[2025-06-03 11:05:01] (1s)  >>> Created relation Essa::TEssaNodeDefinition#dom#e1989adc/3@8ace0c61 with 39 rows and digest e514d9brtnuva4jqo1ma54fp0b2.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Essa::TEssaNodeDefinition#96e9ebfe/4@db33fact
[2025-06-03 11:05:01] Evaluating HOP construct<Essa#24e22a14::TEssaDefinition,0> with inputs:
                        39 tuples in Essa::TEssaNodeDefinition#dom#e1989adc/3@8ace0c61
[2025-06-03 11:05:01] (1s)  >>> Created relation Essa::TEssaNodeDefinition#96e9ebfe/4@db33fact with 39 rows and digest 1b88abkdhpuc45rjnkltkh00oo7.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/5@457885ml
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/5@457885ml with 41 rows and digest 315cea3j4uq0oo059sc3cda5tca.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/2@4f1599to
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/2@4f1599to with 32 rows and digest 061eb88coj3debb88unqmcia5k8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b/2@i1#9473axuj (iteration 1)
[2025-06-03 11:05:01] (1s) 			 - SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b_delta has 32 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@i1#9473awuj (iteration 1)
[2025-06-03 11:05:01] (1s) Empty delta for SsaCompute::EssaDefinitions::phiNode/2#4864b30d_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@i2#9473awuj (iteration 2)
[2025-06-03 11:05:01] (1s) Empty delta for SsaCompute::EssaDefinitions::phiNode/2#4864b30d_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Accumulating deltas
[2025-06-03 11:05:01] (1s)  >>> Discarded freshly computed SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@9473awuj, preempted by a cache hit with digest THIS-RELATION-HAS-NO-TUPLES
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b/2@9473axuj with 32 rows and digest 061eb88coj3debb88unqmcia5k8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Essa::TEssaDefinition#4c062f56/1@8d6b97js
[2025-06-03 11:05:01] (1s)  >>> Created relation Essa::TEssaDefinition#4c062f56/1@8d6b97js with 41 rows and digest 9a96c1ct2q0kg6j9r9c4su7r5if.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate Essa::TEssaDefinition#4c062f56  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_Essa::TEssaDefinition#4c062f56/1@918595pb with 41 rows and digest 9a96c1ct2q0kg6j9r9c4su7r5if.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefRank/4#ea13f11c/4@91d805ha
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::ssaDefRank/4#ea13f11c/4@91d805ha with 41 rows and digest 4f9f7et8a6dfq3n2vjq3jn2vq39.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#3_0122#max_term/4@4143aaec
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#3_0122#max_term/4@4143aaec with 130 rows and digest 2e1ea9amkg9i39g69lq63s6bu6f.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::lastRank/2#6cfcb19d/3@53614d7d
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::lastRank/2#6cfcb19d/3@53614d7d with 37 rows and digest 52fe47of0m88acm1o64g7m4mre0.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::ssaDefRank/4#ea13f11c/3@9824f5bv
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaCompute::SsaComputeImpl::ssaDefRank/4#ea13f11c/3@9824f5bv with 41 rows and digest 765e6dq56i1i3ai1tbeo8s5qur4.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@i1#8372bcfa (iteration 1)
[2025-06-03 11:05:01] (1s) 			 - SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_delta has 41 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@i2#8372bcfa (iteration 2)
[2025-06-03 11:05:01] (1s) 			 - SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_delta has 41 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@i3#8372bcfa (iteration 3)
[2025-06-03 11:05:01] (1s) 			 - SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_delta has 21 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@i4#8372bcfa (iteration 4)
[2025-06-03 11:05:01] (1s) 			 - SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_delta has 3 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@i5#8372bcfa (iteration 5)
[2025-06-03 11:05:01] (1s) Empty delta for SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Accumulating deltas
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@8372bcfa with 106 rows and digest 36fdc7ftg1jgc1a4jo993gdbjs8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_0132#join_rhs/4@b09cddj8
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_0132#join_rhs/4@b09cddj8 with 106 rows and digest ed3894vb15eo9p8k8o20gvlt9i8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Essa::TEssaNodeDefinition#96e9ebfe_1203#join_rhs/4@ad9db5t4
[2025-06-03 11:05:01] (1s)  >>> Created relation Essa::TEssaNodeDefinition#96e9ebfe_1203#join_rhs/4@ad9db5t4 with 39 rows and digest 026c2bolclhthofog4ao86lgdpd.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Essa::EssaNodeDefinition.definedBy/2#dispred#88969aa9/3@76067dst
[2025-06-03 11:05:01] (1s)  >>> Created relation Essa::EssaNodeDefinition.definedBy/2#dispred#88969aa9/3@76067dst with 39 rows and digest f9cdc8fams679qdc79in3b5n5tc.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::AdjacentUses::definesAt/4#68130204/4@8c44424q
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::AdjacentUses::definesAt/4#68130204/4@8c44424q with 39 rows and digest 1ed504sgaq4bc3nair6iq9qj0vd.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::Liveness::liveAtEntry/2#bab3ea7c  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::Liveness::liveAtEntry/2#bab3ea7c/2@6601ffvo with 5 rows and digest fd3201pqqeb2dloh2qrkt53s6b4.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/4@a2d696uu with 130 rows and digest 25d73cg3gqaj39ogc9acq623hl8.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::defRank/4#f608ea69  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::defRank/4#f608ea69/4@7f1611ue with 58 rows and digest 0850c7pms3v3ogckuivs8tu7hl6.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::lastRank/2#6cfcb19d  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::lastRank/2#6cfcb19d/3@0891d874 with 37 rows and digest 52fe47of0m88acm1o64g7m4mre0.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b/2@ae9182bm with 32 rows and digest 061eb88coj3debb88unqmcia5k8.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/5@2ba91fut with 41 rows and digest 315cea3j4uq0oo059sc3cda5tca.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::EssaDefinitions::variableDefinition/5#32b5d84f  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::EssaDefinitions::variableDefinition/5#32b5d84f/5@5f1279gb with 39 rows and digest b6eec00j5rmih9an5e7kei04o70.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::EssaDefinitions::variableRefinement/5#4e891107  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::EssaDefinitions::variableRefinement/5#4e891107/5@95e977ab with 2 rows and digest ab4479eommd6ggr8umnpqk7ohj8.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@404efe5b with 106 rows and digest 36fdc7ftg1jgc1a4jo993gdbjs8.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate Essa::TEssaNodeDefinition#96e9ebfe  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_Essa::TEssaNodeDefinition#96e9ebfe/4@a811d0mp with 39 rows and digest 1b88abkdhpuc45rjnkltkh00oo7.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate Essa::TEssaNodeRefinement#8de2de42  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_Essa::TEssaNodeRefinement#8de2de42/4@3787696p with 2 rows and digest bade7a0ldoksn7dfcusa4udfc49.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::AdjacentUses::definesAt/4#68130204  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::AdjacentUses::definesAt/4#68130204/4@388909lj with 39 rows and digest 1ed504sgaq4bc3nair6iq9qj0vd.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/4@7fee275b
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/4@7fee275b with 64 rows and digest 386a7546i76tul016qdjaf4objc.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/3@5a2c648u
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/3@5a2c648u with 64 rows and digest 203e341u324qu6p30cp5iqvkr1a.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableUse/4#da62dc30_201#join_rhs/3@f3e250ah
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaComputeImpl::variableUse/4#da62dc30_201#join_rhs/3@f3e250ah with 72 rows and digest 272384ftqsr0b50d2adn8nffuhe.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6/4@01ed91u6
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6/4@01ed91u6 with 64 rows and digest 386a7546i76tul016qdjaf4objc.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6/4@2dbd699l with 64 rows and digest 386a7546i76tul016qdjaf4objc.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/4@f3e4d889 with 64 rows and digest 386a7546i76tul016qdjaf4objc.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate _SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19_SsaCompute::SsaComputeImpl::variableDefine/4__#rank_range/3@c11f6av9
[2025-06-03 11:05:01] (1s)  >>> Created relation _SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19_SsaCompute::SsaComputeImpl::variableDefine/4__#rank_range/3@c11f6av9 with 66 rows and digest b2aeb99pjcesu7c5m6ihcttg4fd.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate _SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19_SsaCompute::SsaComputeImpl::variableDefine/4__#rank_term/4@4acd0dnd
[2025-06-03 11:05:01] (1s)  >>> Created relation _SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19_SsaCompute::SsaComputeImpl::variableDefine/4__#rank_term/4@4acd0dnd with 66 rows and digest f5ef02imfvsg4e6kesb04gbhlla.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/4@da356cil
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/4@da356cil with 66 rows and digest e018a42trdrh9roipdhbhi53cec.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/3@2ff57dsj
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/3@2ff57dsj with 66 rows and digest 8abc97bl41f8d8h0o8ejrgqs5e3.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate project#SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb_0122#max_term/4@a05a2ed4
[2025-06-03 11:05:01] (1s)  >>> Created relation project#SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb_0122#max_term/4@a05a2ed4 with 66 rows and digest 8dd305p3431kfguooknfunc4tcf.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::AdjacentUses::lastSourceUseRank/2#062a0026/3@dd0b0c0d
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::AdjacentUses::lastSourceUseRank/2#062a0026/3@dd0b0c0d with 36 rows and digest 8b289e70vfptfd14e3ci18qll60.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5/5@62aca43k
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5/5@62aca43k with 30 rows and digest 6855a6siea2560of9nrlnj7jh35.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::AdjacentUses::definesAt/4#68130204_1230#join_rhs/4@8e8a3ds2
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::AdjacentUses::definesAt/4#68130204_1230#join_rhs/4@8e8a3ds2 with 39 rows and digest 1b88abkdhpuc45rjnkltkh00oo7.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5_03412#join_rhs/5@ef7769gk
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5_03412#join_rhs/5@ef7769gk with 30 rows and digest 98f2867d0voe97l0h2fm5bnmii1.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::AdjacentUses::firstUse/2#ca053ce0/2@i1#e0b6f1ue (iteration 1)
[2025-06-03 11:05:01] (1s) 			 - SsaCompute::AdjacentUses::firstUse/2#ca053ce0_delta has 7 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::AdjacentUses::firstUse/2#ca053ce0/2@i2#e0b6f1ue (iteration 2)
[2025-06-03 11:05:01] (1s) Empty delta for SsaCompute::AdjacentUses::firstUse/2#ca053ce0_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:05:01] (1s) Accumulating deltas
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::AdjacentUses::firstUse/2#ca053ce0/2@e0b6f1ue with 7 rows and digest 50dba43m42lq9hbatojlhdsg7q8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::AdjacentUses::adjacentRefUse/4#6e481bc3/4@9b26d33c
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::AdjacentUses::adjacentRefUse/4#6e481bc3/4@9b26d33c with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (1s) Inferred that SsaCompute::AdjacentUses::adjacentUseUseSameVar/2#2eafd7db/2@7f67651f is empty, due to SsaCompute::AdjacentUses::adjacentRefUse/4#6e481bc3/4@9b26d33c.
[2025-06-03 11:05:01] (1s) Inferred that cached_SsaCompute::AdjacentUses::adjacentUseUseSameVar/2#2eafd7db/2@0dcc16hn is empty, due to SsaCompute::AdjacentUses::adjacentUseUseSameVar/2#2eafd7db/2@7f67651f.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@c7a44a78
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@c7a44a78 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:05:01] (1s) Inferred that boundedFastTC:SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e:SsaCompute::AdjacentUses::firstUse/2#ca053ce0_1#higher_order_body/2@06714dia is empty, due to SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@c7a44a78.
[2025-06-03 11:05:01] (1s) Inferred that cached_SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@1fd2fe9p is empty, due to SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@c7a44a78.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5/5@4359219o with 30 rows and digest 6855a6siea2560of9nrlnj7jh35.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/4@99c70bvp with 66 rows and digest e018a42trdrh9roipdhbhi53cec.
[2025-06-03 11:05:01] (1s) No need to promote strings for predicate SsaCompute::AdjacentUses::firstUse/2#ca053ce0  as it does not contain computed strings.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::AdjacentUses::firstUse/2#ca053ce0/2@b3437a3s with 7 rows and digest 50dba43m42lq9hbatojlhdsg7q8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate SsaCompute::AdjacentUses::useOfDef/2#16a7ee50/2@6b079csq
[2025-06-03 11:05:01] (1s)  >>> Created relation SsaCompute::AdjacentUses::useOfDef/2#16a7ee50/2@6b079csq with 7 rows and digest 50dba43m42lq9hbatojlhdsg7q8.
[2025-06-03 11:05:01] (1s)  >>> Created relation cached_SsaCompute::AdjacentUses::useOfDef/2#16a7ee50/2@c0f71efh with 7 rows and digest 50dba43m42lq9hbatojlhdsg7q8.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_#concat_range/3@9d27916p
[2025-06-03 11:05:01] (1s)  >>> Created relation AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_#concat_range/3@9d27916p with 5 rows and digest 4d6513do6efflobiv5hlng9jd65.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_1_#concat_term/5@8c3c3eg0
[2025-06-03 11:05:01] (1s)  >>> Created relation AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_1_#concat_term/5@8c3c3eg0 with 5 rows and digest 662b32trebe6hsk46kcldqri66d.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate _AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_#concat_range_AstGenerated::Scope_.toString/0#d__#join_rhs/2@3d8906nc
[2025-06-03 11:05:01] (1s)  >>> Created relation _AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_#concat_range_AstGenerated::Scope_.toString/0#d__#join_rhs/2@3d8906nc with 5 rows and digest 0b4aaa486sbg8ecq5e7qbjc6f9f.
[2025-06-03 11:05:01] (1s) Starting to evaluate predicate Flow::ControlFlowNode.toString/0#dispred#e1af144b/2@2e40ddqr
[2025-06-03 11:05:01] (1s)  >>> Created relation Flow::ControlFlowNode.toString/0#dispred#e1af144b/2@2e40ddqr with 109 rows and digest 84c277p3ud6cqs7iqgcdhk4fo6d.
[2025-06-03 11:05:01] (1s) Promoting strings for predicate Flow::ControlFlowNode.toString/0#dispred#e1af144b
[2025-06-03 11:05:01] (1s) Promoted strings in predicate Flow::ControlFlowNode.toString/0#dispred#e1af144b in memory, took 3ms
[2025-06-03 11:05:01] (1s) Saving stringpool to save strings from predicate Flow::ControlFlowNode.toString/0#dispred#e1af144b
[2025-06-03 11:05:01] [WARN] Failed to perform move from E:\advance_javascript\codeQL\7\python-db-updated\db-python\default\cache\cached-strings\pools\0\buckets\info-new to E:\advance_javascript\codeQL\7\python-db-updated\db-python\default\cache\cached-strings\pools\0\buckets\info (attempt 1)
                             java.nio.file.AccessDeniedException: E:\advance_javascript\codeQL\7\python-db-updated\db-python\default\cache\cached-strings\pools\0\buckets\info-new -> E:\advance_javascript\codeQL\7\python-db-updated\db-python\default\cache\cached-strings\pools\0\buckets\info
                             	at java.base/sun.nio.fs.WindowsException.translateToIOException(Unknown Source)
                             	at java.base/sun.nio.fs.WindowsException.rethrowAsIOException(Unknown Source)
                             	at java.base/sun.nio.fs.WindowsFileCopy.move(Unknown Source)
                             	at java.base/sun.nio.fs.WindowsFileSystemProvider.move(Unknown Source)
                             	at java.base/java.nio.file.Files.move(Unknown Source)
                             	at com.semmle.util.files.FileUtil8.lambda$moveWithRetries$6(FileUtil8.java:592)
                             	at com.semmle.util.files.FileUtil8.performWithRetries(FileUtil8.java:559)
                             	at com.semmle.util.files.FileUtil8.moveWithRetries(FileUtil8.java:591)
                             	at com.semmle.inmemory.util.MMapResizingIntArray.writeInfoFile(MMapResizingIntArray.java:376)
                             	at com.semmle.inmemory.util.MMapResizingIntArray.save(MMapResizingIntArray.java:467)
                             	at com.semmle.inmemory.util.NonSequentialDiskPool.save(NonSequentialDiskPool.java:119)
                             	at com.semmle.inmemory.stringpool.ExtensionalGeneration.save(ExtensionalGeneration.java:281)
                             	at com.semmle.inmemory.stringpool.StringPool.saveCachedStrings(StringPool.java:586)
                             	at com.semmle.inmemory.scheduler.ComputedExtensionalLayer.promoteStrings(ComputedExtensionalLayer.java:127)
                             	at com.semmle.inmemory.scheduler.ComputedExtensionalLayer$1.evaluate(ComputedExtensionalLayer.java:82)
                             	at com.semmle.inmemory.scheduler.SimpleLayerTask$SimpleLayerWork.doWork(SimpleLayerTask.java:78)
                             	at com.semmle.inmemory.scheduler.execution.ThreadableWork.doSomeWork(ThreadableWork.java:396)
                             	at com.semmle.inmemory.scheduler.execution.ExecutionScheduler.runnerMain(ExecutionScheduler.java:707)
                             	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
                             	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
                             	at java.base/java.lang.Thread.run(Unknown Source)
[2025-06-03 11:05:02] (2s) Saved stringpool to save strings from predicate Flow::ControlFlowNode.toString/0#dispred#e1af144b, took 0ms
[2025-06-03 11:05:02] (2s)  >>> Created relation cached_Flow::ControlFlowNode.toString/0#dispred#e1af144b/2@54b666h0 with 109 rows and digest 84c277p3ud6cqs7iqgcdhk4fo6d.
[2025-06-03 11:05:02] (2s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableUse/4#da62dc30_0213#join_rhs/4@ebef90hv
[2025-06-03 11:05:02] (2s)  >>> Created relation SsaCompute::SsaComputeImpl::variableUse/4#da62dc30_0213#join_rhs/4@ebef90hv with 72 rows and digest 65c57ffq8a87c06qh3ka8u40gg2.
[2025-06-03 11:05:02] (2s) Starting to evaluate predicate SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6_0312#join_rhs/4@dedab2lb
[2025-06-03 11:05:02] (2s)  >>> Created relation SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6_0312#join_rhs/4@dedab2lb with 64 rows and digest 456e176kg7fktgfnkcnp156jvn8.
[2025-06-03 11:05:02] (2s) Starting to evaluate predicate SsaCompute::SsaDefinitions::reachesExit/3#a534505a/3@a990156h
[2025-06-03 11:05:02] (2s)  >>> Created relation SsaCompute::SsaDefinitions::reachesExit/3#a534505a/3@a990156h with 31 rows and digest cf8c04ifauet6eenn28k6tv0ka3.
[2025-06-03 11:05:02] (2s) No need to promote strings for predicate SsaCompute::SsaDefinitions::reachesExit/3#a534505a  as it does not contain computed strings.
[2025-06-03 11:05:02] (2s)  >>> Created relation cached_SsaCompute::SsaDefinitions::reachesExit/3#a534505a/3@cbea635k with 31 rows and digest cf8c04ifauet6eenn28k6tv0ka3.
[2025-06-03 11:05:02] (2s) Query done
[2025-06-03 11:05:02] (2s) Sequence stamp origin is -6042293659345584315
[2025-06-03 11:05:02] (2s) Pausing evaluation to sync to disk at sequence stamp o+0
[2025-06-03 11:05:02] (2s) Unpausing evaluation
[2025-06-03 11:05:02] Evaluation of E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql produced BQRS results.
[2025-06-03 11:05:02] [PROGRESS] execute queries> [1/1 eval 2.4s] Evaluation done; writing results to python-security-queries\queries\python-password-vulnerability.bqrs.
[2025-06-03 11:05:02] [PROGRESS] execute queries> Shutting down query evaluator.
[2025-06-03 11:05:02] Pausing evaluation to close the cache at sequence stamp o+1
[2025-06-03 11:05:02] Doing closing disk-cache trim now.
[2025-06-03 11:05:02] After trimming, disk cache uses 24.87kiB.
[2025-06-03 11:05:02] Unpausing evaluation
[2025-06-03 11:05:02] Exiting with code 0
