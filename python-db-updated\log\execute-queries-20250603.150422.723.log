[2025-06-03 15:04:22] This is codeql execute queries -J-Xmx1374M --verbosity=progress --logdir=E:\advance_javascript\codeQL\7\python-db-updated\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\7\python-db-updated\results -- E:\advance_javascript\codeQL\7\python-db-updated\db-python queries/python-problm-1.ql
[2025-06-03 15:04:22] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --format=json -- queries/python-problm-1.ql
[2025-06-03 15:04:23] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 15:04:23] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\queries\\python-problm-1.ql"
                      ]
[2025-06-03 15:04:23] Refusing fancy output: The terminal is not an xterm: 
[2025-06-03 15:04:23] Creating executor with 1 threads.
[2025-06-03 15:04:24] Calling plumbing command: codeql resolve extensions --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations queries/python-problm-1.ql
[2025-06-03 15:04:24] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --allow-library-packs --format startingpacks -- queries/python-problm-1.ql
[2025-06-03 15:04:24] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 15:04:24] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7"
                      ]
[2025-06-03 15:04:24] Calling plumbing command: codeql resolve extensions-by-pack --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\7
[2025-06-03 15:04:24] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}
[2025-06-03 15:04:24] [SPAMMY] resolve extensions-by-pack> [DERIVATION] python-security-queries: 1.0.0 {python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 1] python-security-queries: 1.0.0
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/python-all: * [*] {python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/python-all: 4.0.7
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 15:04:28] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 15:04:28] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\ext\default-threat-models-fixup.model.yml.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 15:04:28] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Asyncpg.model.yml.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 5 tuples.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 6 tuples.
[2025-06-03 15:04:28] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Stdlib.model.yml.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 12 tuples.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 1 tuples.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 66 tuples.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 15:04:28] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\empty.model.yml.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 0 tuples.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 0 tuples.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 0 tuples.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 15:04:28] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 15:04:29] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\subclass-capture\ALL.model.yml.
[2025-06-03 15:04:29] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 58275 tuples.
[2025-06-03 15:04:29] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-03 15:04:29] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 15:04:29] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-03 15:04:29] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-03 15:04:29] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-03 15:04:29] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-03 15:04:29] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-03 15:04:29] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=7+1+2+1+2",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=20+4+1*2+2+1",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4+2+1+2+1*2+4+2",
                                "columnNumbers" : "A=9*12"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=29",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=37+1+2+4+2*2+4+2*3+1+2+1+2+1+2+4+2+4+2*2+3+2*2+3+1+2*4+4+1+4+1+4+1*5+2*4+4+1+2*11+3+2+3+4+1+2*2+1+2",
                                "columnNumbers" : "A=9*66"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+3*58274",
                                "columnNumbers" : "A=5*58275"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 15:04:29] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=7+1+2+1+2",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=20+4+1*2+2+1",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4+2+1+2+1*2+4+2",
                                "columnNumbers" : "A=9*12"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=29",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=37+1+2+4+2*2+4+2*3+1+2+1+2+1+2+4+2+4+2*2+3+2*2+3+1+2*4+4+1+4+1+4+1*5+2*4+4+1+2*11+3+2+3+4+1+2*2+1+2",
                                "columnNumbers" : "A=9*66"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+3*58274",
                                "columnNumbers" : "A=5*58275"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 15:04:30] Calling plumbing command: codeql resolve library-path --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --query=E:\advance_javascript\codeQL\7\queries\python-problm-1.ql --format=json
[2025-06-03 15:04:30] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 15:04:30] [DETAILS] resolve library-path> Found enclosing pack 'python-security-queries' at E:\advance_javascript\codeQL\7.
[2025-06-03 15:04:30] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-03 15:04:30] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\7\qlpack.yml.
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] python-security-queries: 1.0.0 {python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 1] python-security-queries: 1.0.0
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] codeql/python-all: * [*] {python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 2] codeql/python-all: 4.0.7
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] codeql/yaml: * [*] {codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] codeql/xml: * [*] {codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] codeql/util: * [*] {codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] codeql/tutorial: * [*] {codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] codeql/threat-models: * [*] {codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] codeql/regex: * [*] {codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] codeql/mad: * [*] {codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] codeql/dataflow: * [*] {codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] codeql/typetracking: * [*] {codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DERIVATION] codeql/ssa: * [*] {codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 15:04:30] [SPAMMY] resolve library-path> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 15:04:30] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\7 resolved OK.
[2025-06-03 15:04:30] [DETAILS] resolve library-path> Found dbscheme through QL packs: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmlecode.python.dbscheme.
[2025-06-03 15:04:30] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\dataflow\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\mad\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\regex\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\ssa\\1.1.2",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\tutorial\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\typetracking\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\xml\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\yaml\\1.0.23"
                        ],
                        "dbscheme" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmlecode.python.dbscheme",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "python-security-queries\\queries\\python-problm-1.ql",
                        "qlPackName" : "python-security-queries"
                      }
[2025-06-03 15:04:30] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 15:04:30] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 15:04:31] Resolved file set for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql hashes to 2c2380da56dfc3a5980c42863c65105c.
[2025-06-03 15:04:31] [DETAILS] execute queries> Compilation cache hit for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 15:04:31] [SPAMMY] execute queries> No database upgrade/downgrade needed for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql
[2025-06-03 15:04:31] [PROGRESS] execute queries> [1/1] Found in cache: E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 15:04:31] [PROGRESS] execute queries> Starting evaluation of python-security-queries\queries\python-problm-1.ql.
[2025-06-03 15:04:31] Starting evaluation of E:\advance_javascript\codeQL\7\queries\python-problm-1.ql
[2025-06-03 15:04:31] (0s) Start query execution
[2025-06-03 15:04:31] (0s) Beginning execution of E:\advance_javascript\codeQL\7\queries\python-problm-1.ql
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_AstExtended::AstNode.getLocation/0#dispred#6b4dcb62/2@389a43u0 with 129 rows and digest 230d52r3fgbfc6mbinshdjrdvee.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985/3@c992f399 with 35 rows and digest 1cb23dnt3nmevu3157k9j89fo26.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_Exprs::Expr.toString/0#dispred#7435dfcd/2@a8c03872 with 86 rows and digest c43300spki6g1kqtglo00fbtgk2.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_Essa::TEssaDefinition#4c062f56/1@918595pb with 41 rows and digest 9a96c1ct2q0kg6j9r9c4su7r5if.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_Essa::TEssaEdgeDefinition#be4738bc/4@2da913f5 with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@3204beti with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@1fd2fe9p with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaCompute::AdjacentUses::adjacentUseUseSameVar/2#2eafd7db/2@0dcc16hn with empty relation.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5/5@4359219o with 30 rows and digest 6855a6siea2560of9nrlnj7jh35.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/4@99c70bvp with 66 rows and digest e018a42trdrh9roipdhbhi53cec.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::variableDefine/4#79261f91/4@1e9ed9n1 with 55 rows and digest f0af40jc4tshc9ft2i0lclbnc9e.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_Flow::BasicBlock.strictlyDominates/1#dispred#65ae2ca3/2@40c1c68m with empty relation.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_CachedStages::Stages::AST::ref/0#e52d92b5/0@c876115v with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_CachedStages::Stages::AST::backref/0#dddb3000/0@a80d27a0 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9/3@ec7d91se with 2 rows and digest ad8cbfggshaf5jrsdmflrimdjmc.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaDefinitions::SsaSource::argument_refinement/3#615fb3a9/3@2d844778 with 1 rows and digest d22634250nvm4lnndccm7b5ni39.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/3@5c11868o with 23 rows and digest f0d941po4rfm4pghgansasjk06a.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_Flow::DefinitionNode#e8809c3b/1@a95b912i with 23 rows and digest 5f6dcbtbrphb5cuovedd0066220.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee/2@89ae53vo with 109 rows and digest 260af6fcpll0em4qao052h8j0q4.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::parameter_definition/2#40b2fede/2@9409b5ba with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::attribute_assignment_refinement/3#5f777592/3@f4c9688s with empty relation.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_Flow::ControlFlowNode.getScope/0#dispred#b061daac/2@afe6a2k8 with 109 rows and digest 24903dlh7adrl0pf81ntttrn0rd.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_AstExtended::AstNode.getAChildNode/0#dispred#a130356d/2@4a4f6brg with 127 rows and digest fced70t0gh1cundc5s9va72gab4.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@debdc7ao with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::attribute_deletion_refinement/3#7de1bd7a/3@bc7edf3e with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::deletion_definition/2#e74a17e3/2@a934fde8 with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::exception_capture/2#fd0779df/2@793700dj with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::exception_group_capture/2#d53f492d/2@790fdeja with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::import_star_refinement/3#1675f8e2/3@39fa47h0 with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::init_module_submodule_defn/2#64b1af7d/2@2761bd9c with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_Module::moduleNameFromFile/1#a01d5f51/2@010e4f4r with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/4@89e653d3 with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_Flow::SequenceNode.getElement/1#dispred#4cc4068f/3@cc5e78ka with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::pattern_alias_definition/2#e55586af/2@f91af3ha with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::pattern_capture_definition/2#77b3ae42/2@6e282c28 with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::test_refinement/3#2cbdf557/3@c646bdum with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::with_definition/2#e1888ceb/2@e0bd2902 with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@8d3536mv with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@00f3ba04 with empty relation.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::Liveness::liveAtEntry/2#bab3ea7c/2@6601ffvo with 5 rows and digest fd3201pqqeb2dloh2qrkt53s6b4.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/4@a2d696uu with 130 rows and digest 25d73cg3gqaj39ogc9acq623hl8.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e/4@8a1c0f76 with 58 rows and digest 73b2be1t55j4ie8mesec8bbrqg7.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::variableRefine/4#b20a3c26/4@209e7767 with 3 rows and digest 6e2619ol47gc8sosqs0cvrtjhhb.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::variableUse/4#da62dc30/4@ce02f549 with 72 rows and digest e061381rd58uh7kttr3uorvp9nd.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::defRank/4#f608ea69/4@7f1611ue with 58 rows and digest 0850c7pms3v3ogckuivs8tu7hl6.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::lastRank/2#6cfcb19d/3@0891d874 with 37 rows and digest 52fe47of0m88acm1o64g7m4mre0.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@ce8e8e2f with empty relation.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b/2@ae9182bm with 32 rows and digest 061eb88coj3debb88unqmcia5k8.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/5@2ba91fut with 41 rows and digest 315cea3j4uq0oo059sc3cda5tca.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::EssaDefinitions::variableDefinition/5#32b5d84f/5@5f1279gb with 39 rows and digest b6eec00j5rmih9an5e7kei04o70.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::EssaDefinitions::variableRefinement/5#4e891107/5@95e977ab with 2 rows and digest ab4479eommd6ggr8umnpqk7ohj8.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@404efe5b with 106 rows and digest 36fdc7ftg1jgc1a4jo993gdbjs8.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@408ca88v with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_Essa::TPhiFunction#dfb6fe3b/3@2bce46ul with empty relation.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_Essa::PhiFunction.getInput/1#dispred#f797e1b9/3@8f4ad7c4 with empty relation.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_Essa::TEssaNodeDefinition#96e9ebfe/4@a811d0mp with 39 rows and digest 1b88abkdhpuc45rjnkltkh00oo7.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_Essa::TEssaNodeRefinement#8de2de42/4@3787696p with 2 rows and digest bade7a0ldoksn7dfcusa4udfc49.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_Import::ImportStar.getModuleExpr/0#dispred#8ab9ce73/2@9212cahk with empty relation.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_AstExtended::AstNode.getParentNode/0#dispred#c159e3ee/2@699751d5 with 127 rows and digest 76d33di2qp08hg947gac2givg43.
[2025-06-03 15:04:31] (0s)  >>> First-level cache hit cached_#Flow::BasicBlock.getASuccessor/0#dispred#7249dd96Plus/2@4ae642b8 with empty relation.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_Flow::ControlFlowNode.toString/0#dispred#e1af144b/2@54b666h0 with 109 rows and digest 84c277p3ud6cqs7iqgcdhk4fo6d.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19/4@271bba4s with 11 rows and digest 1d9d42qqmmmesn7cdld5hcnqfke.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::definesAt/4#68130204/4@388909lj with 39 rows and digest 1ed504sgaq4bc3nair6iq9qj0vd.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::firstUse/2#ca053ce0/2@b3437a3s with 7 rows and digest 50dba43m42lq9hbatojlhdsg7q8.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::useOfDef/2#16a7ee50/2@c0f71efh with 7 rows and digest 50dba43m42lq9hbatojlhdsg7q8.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaDefinitions::reachesExit/3#a534505a/3@cbea635k with 31 rows and digest cf8c04ifauet6eenn28k6tv0ka3.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6/4@2dbd699l with 64 rows and digest 386a7546i76tul016qdjaf4objc.
[2025-06-03 15:04:31] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/4@f3e4d889 with 64 rows and digest 386a7546i76tul016qdjaf4objc.
[2025-06-03 15:04:31] (0s)  >>> Created relation py_variables/2@62cd450b with 34 rows and digest 05c4e5cs4vkc2t3m8v34ndch22c.
[2025-06-03 15:04:31] (0s)  >>> Created relation py_exprs/4@62fb455g with 86 rows and digest 476bbb9bpd54bfhhl3b277dvi93.
[2025-06-03 15:04:31] (0s)  >>> Created relation variable/3@5e1d40kq with 33 rows and digest 6a3b4b14bgnblis0klnmukgbrr8.
[2025-06-03 15:04:31] (0s) Starting to evaluate predicate variable_201#join_rhs/3@0467c2qr
[2025-06-03 15:04:31] (0s)  >>> Created relation variable_201#join_rhs/3@0467c2qr with 33 rows and digest eb6e8acjiqo513j2dgha178uo93.
[2025-06-03 15:04:31] (0s) Starting to evaluate predicate Exprs::Name.getId/0#dispred#4fa5460e#fb/2@b3e4919s
[2025-06-03 15:04:31] (0s)  >>> Created relation Exprs::Name.getId/0#dispred#4fa5460e#fb/2@b3e4919s with 1 rows and digest 8bdc82kp7atfppf92n5gi2qkrq9.
[2025-06-03 15:04:31] (0s) Starting to evaluate predicate Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs/2@b9a83bie
[2025-06-03 15:04:31] (0s)  >>> Created relation Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs/2@b9a83bie with 1 rows and digest f5a4e7k9mbsv0fse196bmcmjho6.
[2025-06-03 15:04:31] (0s) Starting to evaluate predicate py_exprs_032#join_rhs/3@dc21c4iv
[2025-06-03 15:04:31] (0s)  >>> Created relation py_exprs_032#join_rhs/3@dc21c4iv with 86 rows and digest be84dc6fojrfanlpc0s7kg5t146.
[2025-06-03 15:04:31] (0s) Starting to evaluate predicate _Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared/2@a58601vs
[2025-06-03 15:04:31] (0s)  >>> Created relation _Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared/2@a58601vs with 1 rows and digest ed4640dl190c7bgt7s45m2flra9.
[2025-06-03 15:04:31] (0s)  >>> Created relation folders/2@bad7d9u0 with 7 rows and digest 504939bjsummg163iumr35cn6fc.
[2025-06-03 15:04:31] (0s)  >>> Created relation files/2@ec93749g with 7 rows and digest 25e58crktdgaufn1jel1vuc5rq9.
[2025-06-03 15:04:31] (0s) Starting to evaluate predicate Files::Input::ContainerBase.getAbsolutePath/0#dispred#bb5aca3b/2@966d67vo
[2025-06-03 15:04:31] (0s)  >>> Created relation Files::Input::ContainerBase.getAbsolutePath/0#dispred#bb5aca3b/2@966d67vo with 14 rows and digest da9ba69sf3j2f354p2stvjdrj72.
[2025-06-03 15:04:31] (0s)  >>> Created relation py_module_path/2@7a0f7599 with 2 rows and digest 76820d8bcba222g98abm3vg7e07.
[2025-06-03 15:04:31] (0s) Starting to evaluate predicate Module::Module.getFile/0#dispred#53eb9b1b/2@648ff7sb
[2025-06-03 15:04:31] (0s)  >>> Created relation Module::Module.getFile/0#dispred#53eb9b1b/2@648ff7sb with 2 rows and digest 76820d8bcba222g98abm3vg7e07.
[2025-06-03 15:04:31] (0s) Starting to evaluate predicate Module::Module.getFile/0#dispred#53eb9b1b_0#antijoin_rhs/1@b54f01qv
[2025-06-03 15:04:31] (0s)  >>> Created relation Module::Module.getFile/0#dispred#53eb9b1b_0#antijoin_rhs/1@b54f01qv with 2 rows and digest 1758e8s2mdeq4m767kljah7j4r6.
[2025-06-03 15:04:32] (0s)  >>> Created relation locations_default/6@2b5cc3pr with 574 rows and digest e548d7crqpjq6b0dj0bibvtc344.
[2025-06-03 15:04:32] (0s)  >>> Created relation locations_ast/6@aba204da with 140 rows and digest acf902vjm0jpiucb5cthhe02kqa.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate locations_ast_102345#join_rhs/6@ba5e2d5g
[2025-06-03 15:04:32] (0s)  >>> Created relation locations_ast_102345#join_rhs/6@ba5e2d5g with 140 rows and digest 117827sltp60vgg3jp4m5mfbh1b.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate Files::Location.hasLocationInfo/5#dispred#143bb906/6@5c85b9n0
[2025-06-03 15:04:32] (0s)  >>> Created relation Files::Location.hasLocationInfo/5#dispred#143bb906/6@5c85b9n0 with 714 rows and digest 563123iq49t3qqslv960gl3vkbc.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate __Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared_py_exprs#shared/1@7421accn
[2025-06-03 15:04:32] (0s)  >>> Created relation __Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared_py_exprs#shared/1@7421accn with 1 rows and digest c60effkejgi4u262a0k2kdlb337.
[2025-06-03 15:04:32] (0s)  >>> Created relation py_locations/2@d222f4l2 with 124 rows and digest 1708daoeqjn73e3dd8krpu4got4.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate py_locations_10#join_rhs/2@cad24bnc
[2025-06-03 15:04:32] (0s)  >>> Created relation py_locations_10#join_rhs/2@cad24bnc with 124 rows and digest 56b5b7iks3ev6iouv9k9saassfb.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#jo__#shared/2@1d8233bm
[2025-06-03 15:04:32] (0s)  >>> Created relation _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#jo__#shared/2@1d8233bm with 1 rows and digest b48a7bmlata5th0ujqihfflqml5.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate AstGenerated::Expr_.getLocation/0#dispred#3637884c#bf/2@21ddc67t
[2025-06-03 15:04:32] (0s)  >>> Created relation AstGenerated::Expr_.getLocation/0#dispred#3637884c#bf/2@21ddc67t with 1 rows and digest 611cf6u3sh99ej6iaq0gr2mbdu6.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate _AstGenerated::Expr_.getLocation/0#dispred#3637884c#bf___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#__#shared/2@6fbb28fv
[2025-06-03 15:04:32] (0s)  >>> Created relation _AstGenerated::Expr_.getLocation/0#dispred#3637884c#bf___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#__#shared/2@6fbb28fv with 1 rows and digest b48a7bmlata5th0ujqihfflqml5.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate project#Files::Location.hasLocationInfo/5#dispred#143bb906/1@2da765ju
[2025-06-03 15:04:32] (0s)  >>> Created relation project#Files::Location.hasLocationInfo/5#dispred#143bb906/1@2da765ju with 714 rows and digest c8832acv82q79lafn3sp7afjul4.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate __AstExtended::AstNode.getLocation/0#dispred#6b4dcb62___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#j__#antijoin_rhs/2@f709a4ph
[2025-06-03 15:04:32] (0s)  >>> Created relation __AstExtended::AstNode.getLocation/0#dispred#6b4dcb62___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#j__#antijoin_rhs/2@f709a4ph with 1 rows and digest 28d66f3vp3l2qq1peijr1jt0urc.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate __Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared___AstExtended::As__#shared/2@861abc2t
[2025-06-03 15:04:32] (0s)  >>> Created relation __Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared___AstExtended::As__#shared/2@861abc2t with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate _Files::Location.hasLocationInfo/5#dispred#143bb906__AstExtended::AstNode.getLocation/0#dispred#6b4d__#shared/6@4b3f37ea
[2025-06-03 15:04:32] (0s)  >>> Created relation _Files::Location.hasLocationInfo/5#dispred#143bb906__AstExtended::AstNode.getLocation/0#dispred#6b4d__#shared/6@4b3f37ea with 1 rows and digest 5fd5f514rt6ftkcb6ur4puu3gk2.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate project#Exprs::Expr.toString/0#dispred#7435dfcd/1@68a913ck
[2025-06-03 15:04:32] (0s)  >>> Created relation project#Exprs::Expr.toString/0#dispred#7435dfcd/1@68a913ck with 86 rows and digest cae054glicom1fvk2i6m8kf69p0.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/2@0e8f5fja
[2025-06-03 15:04:32] (0s)  >>> Created relation AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/2@0e8f5fja with 1 rows and digest 861082rc77b88tjpiclopahsnl4.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate project#AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/1@f1729a8s
[2025-06-03 15:04:32] (0s)  >>> Created relation project#AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/1@f1729a8s with 1 rows and digest c60effkejgi4u262a0k2kdlb337.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate __Files::Location.hasLocationInfo/5#dispred#143bb906__AstExtended::AstNode.getLocation/0#dispred#6b4__#antijoin_rhs/7@2cfac9ig
[2025-06-03 15:04:32] (0s)  >>> Created relation __Files::Location.hasLocationInfo/5#dispred#143bb906__AstExtended::AstNode.getLocation/0#dispred#6b4__#antijoin_rhs/7@2cfac9ig with 1 rows and digest 9f4740q0sts2hcr6264f7urnnp1.
[2025-06-03 15:04:32] (0s) Starting to evaluate predicate #select#query/8@2b028ee8
[2025-06-03 15:04:32] (0s)  >>> Created relation #select#query/8@2b028ee8 with 1 rows and digest dd4269l6bk2r2bnt46e8ekn4bdc.
[2025-06-03 15:04:32] (0s) Query done
[2025-06-03 15:04:32] (0s) Sequence stamp origin is -6042231874261287669
[2025-06-03 15:04:32] (0s) Pausing evaluation to sync to disk at sequence stamp o+0
[2025-06-03 15:04:32] (0s) Unpausing evaluation
[2025-06-03 15:04:32] Evaluation of E:\advance_javascript\codeQL\7\queries\python-problm-1.ql produced BQRS results.
[2025-06-03 15:04:32] [PROGRESS] execute queries> [1/1 eval 376ms] Evaluation done; writing results to python-security-queries\queries\python-problm-1.bqrs.
[2025-06-03 15:04:32] [PROGRESS] execute queries> Shutting down query evaluator.
[2025-06-03 15:04:32] Pausing evaluation to close the cache at sequence stamp o+1
[2025-06-03 15:04:32] The disk cache is freshly trimmed; leave it be.
[2025-06-03 15:04:32] Unpausing evaluation
[2025-06-03 15:04:32] Exiting with code 0
