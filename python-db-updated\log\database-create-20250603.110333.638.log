[2025-06-03 11:03:33] This is codeql database create python-db-updated --language=python --source-root=. --overwrite
[2025-06-03 11:03:33] Log file was started late.
[2025-06-03 11:03:33] [PROGRESS] database create> Initializing database at E:\advance_javascript\codeQL\7\python-db-updated.
[2025-06-03 11:03:33] Running plumbing command: codeql database init --overwrite --language=python --extractor-options-verbosity=1 --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --source-root=E:\advance_javascript\codeQL\7 --allow-missing-source-root=false --allow-already-existing -- E:\advance_javascript\codeQL\7\python-db-updated
[2025-06-03 11:03:33] Calling plumbing command: codeql resolve languages --extractor-options-verbosity=1 --format=betterjson
[2025-06-03 11:03:34] [DETAILS] resolve languages> Scanning for [codeql-extractor.yml] from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\.codeqlmanifest.json
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\actions\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\cpp\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csv\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\go\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\html\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\java\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\properties\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\ruby\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\swift\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\xml\codeql-extractor.yml.
[2025-06-03 11:03:34] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\codeql-extractor.yml.
[2025-06-03 11:03:34] Plumbing command codeql resolve languages completed:
                      {
                        "aliases" : {
                          "c" : "cpp",
                          "c++" : "cpp",
                          "c-c++" : "cpp",
                          "c-cpp" : "cpp",
                          "c#" : "csharp",
                          "java-kotlin" : "java",
                          "kotlin" : "java",
                          "javascript-typescript" : "javascript",
                          "typescript" : "javascript"
                        },
                        "extractors" : {
                          "actions" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\actions",
                              "extractor_options" : { }
                            }
                          ],
                          "cpp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\cpp",
                              "extractor_options" : {
                                "scale_timeouts" : {
                                  "title" : "Value to scale compiler introspection timeouts with",
                                  "description" : "The extractor attempts to determine what compiler the source code being extracted is compiled with. To this end the extractor makes additional calls to the compiler, some of which are expected to return within a certain fixed time (either 10s or 15s). On some systems that are under high load this time might be too short, and can be scaled up using this option.\n",
                                  "type" : "string",
                                  "pattern" : "[0-9]+"
                                },
                                "log_verbosity" : {
                                  "title" : "Verbosity of the extractor logging",
                                  "description" : "Set the verbosity of the extractor logging to 'quiet' (0), 'normal' (1), 'chatty' (2), or 'noisy' (3). The default is 'normal'.\n",
                                  "type" : "string",
                                  "pattern" : "[0-3]"
                                }
                              }
                            }
                          ],
                          "csharp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csharp",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'brotli' (the default, to write brotli-compressed TRAP), 'gzip', and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip|brotli)$"
                                    }
                                  }
                                },
                                "buildless" : {
                                  "title" : "DEPRECATED - Whether to use buildless (standalone) extraction.",
                                  "description" : "DEPRECATED: Use `--build-mode none` instead.\nA value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the extractor. The supported levels are (in order of increasing verbosity):\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "binlog" : {
                                  "title" : "Binlog",
                                  "description" : "[EXPERIMENTAL] The value is a path to the MsBuild binary log file that should be extracted. This option only works when `--build-mode none` is also specified.\n",
                                  "type" : "array"
                                }
                              }
                            }
                          ],
                          "csv" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csv"
                            }
                          ],
                          "go" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\go",
                              "extractor_options" : {
                                "extract_tests" : {
                                  "title" : "Whether to include Go test files in the CodeQL database.",
                                  "description" : "A value indicating whether Go test files should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "extract_vendor_dirs" : {
                                  "title" : "Whether to include Go vendor directories in the CodeQL database.",
                                  "description" : "A value indicating whether Go vendor directories should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "html" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\html"
                            }
                          ],
                          "java" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\java",
                              "extractor_options" : {
                                "exclude" : {
                                  "title" : "A glob excluding files from analysis.",
                                  "description" : "A glob indicating what files to exclude from the analysis. This accepts glob patterns that are supported by Java's 'getPathMatcher' implementation.\n",
                                  "type" : "string"
                                },
                                "add_prefer_source" : {
                                  "title" : "Whether to always prefer source files over class files.",
                                  "description" : "A value indicating whether source files should be preferred over class files. If set to 'true', the extraction adds '-Xprefer:source' to the javac command line. If set to 'false', the extraction uses the default javac behavior ('-Xprefer:newer'). The default is 'true'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless" : {
                                  "title" : "Whether to use buildless (standalone) extraction (experimental).",
                                  "description" : "A value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless_dependency_dir" : {
                                  "title" : "The path where buildless (standalone) extraction should keep dependencies.",
                                  "description" : "If set, the buildless (standalone) extractor will store dependencies in this directory.\n",
                                  "type" : "string"
                                },
                                "minimize_dependency_jars" : {
                                  "title" : "Whether to rewrite and minimize downloaded JAR dependencies (experimental).",
                                  "description" : "If 'true', JAR dependencies downloaded during extraction will be rewritten to remove unneeded data, such as method bodies. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "javascript" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\javascript",
                              "extractor_options" : {
                                "skip_types" : {
                                  "title" : "Skip type extraction for TypeScript",
                                  "description" : "Whether to skip the extraction of types in a TypeScript application",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "properties" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\properties"
                            }
                          ],
                          "python" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\python",
                              "extractor_options" : {
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Python extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the CodeQL Python extractor.\nThe supported levels are (in order of increasing verbosity):\n\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "python_executable_name" : {
                                  "title" : "Controls the name of the Python executable used by the Python extractor.",
                                  "description" : "The Python extractor uses platform-dependent heuristics to determine the name of the Python executable to use. Specifying a value for this option overrides the name of the Python executable used by the extractor. Accepted values are py, python and python3. Use this setting with caution, the Python extractor requires Python 3 to run.\n",
                                  "type" : "string",
                                  "pattern" : "^(py|python|python3)$"
                                }
                              }
                            }
                          ],
                          "ruby" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\ruby",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'gzip' (the default, to write gzip-compressed TRAP) and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip)$"
                                    }
                                  }
                                }
                              }
                            }
                          ],
                          "swift" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\swift"
                            }
                          ],
                          "xml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\xml"
                            }
                          ],
                          "yaml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\yaml"
                            }
                          ]
                        }
                      }
[2025-06-03 11:03:34] [PROGRESS] database init> Calculating baseline information in E:\advance_javascript\codeQL\7
[2025-06-03 11:03:34] [SPAMMY] database init> Ignoring the following directories when processing baseline information: .git, .hg, .svn.
[2025-06-03 11:03:34] [DETAILS] database init> Running command in E:\advance_javascript\codeQL\7: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\scc.exe --by-file --exclude-dir .git,.hg,.svn --format json --no-large --no-min .
[2025-06-03 11:03:35] [SPAMMY] database init> Found 2 baseline files for python.
[2025-06-03 11:03:35] [PROGRESS] database init> Calculated baseline information for languages: python (391ms).
[2025-06-03 11:03:35] [PROGRESS] database init> Resolving extractor python.
[2025-06-03 11:03:35] [DETAILS] database init> Found candidate extractor root for python: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python.
[2025-06-03 11:03:35] [PROGRESS] database init> Successfully loaded extractor Python (python) from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python.
[2025-06-03 11:03:35] [PROGRESS] database init> Created skeleton CodeQL database at E:\advance_javascript\codeQL\7\python-db-updated. This in-progress database is ready to be populated by an extractor.
[2025-06-03 11:03:35] Plumbing command codeql database init completed.
[2025-06-03 11:03:35] [PROGRESS] database create> Running build command: []
[2025-06-03 11:03:35] Running plumbing command: codeql database trace-command --working-dir=E:\advance_javascript\codeQL\7 --index-traceless-dbs --no-db-cluster -- E:\advance_javascript\codeQL\7\python-db-updated
[2025-06-03 11:03:35] Using autobuild script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\autobuild.cmd.
[2025-06-03 11:03:35] [PROGRESS] database trace-command> Running command in E:\advance_javascript\codeQL\7: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\autobuild.cmd]
[2025-06-03 11:03:36] [build-stdout] Python 3.13.2
[2025-06-03 11:03:36] [build-stderr] No suitable Python runtime found
[2025-06-03 11:03:36] [build-stderr] Pass --list (-0) to see all detected environments on your machine
[2025-06-03 11:03:36] [build-stderr] or set environment variable PYLAUNCHER_ALLOW_INSTALL to use winget
[2025-06-03 11:03:36] [build-stderr] or open the Microsoft Store to the requested version.
[2025-06-03 11:03:36] [build-stdout] No directories containing root identifiers were found. Returning working directory as root.
[2025-06-03 11:03:36] [build-stdout] Will try to guess Python version, as it was not specified in `lgtm.yml`
[2025-06-03 11:03:36] [build-stdout] Trying to guess Python version based on Trove classifiers in setup.py
[2025-06-03 11:03:36] [build-stdout] Did not find setup.py (expected it to be at E:\advance_javascript\codeQL\7\setup.py)
[2025-06-03 11:03:36] [build-stdout] Trying to guess Python version based on travis file
[2025-06-03 11:03:36] [build-stdout] Did not find any travis files (expected them at either ['E:\\advance_javascript\\codeQL\\7\\.travis.yml', 'E:\\advance_javascript\\codeQL\\7\\travis.yml'])
[2025-06-03 11:03:36] [build-stdout] Trying to guess Python version based on installed versions
[2025-06-03 11:03:36] [build-stdout] Wanted to run Python 2, but it is not available. Using Python 3 instead
[2025-06-03 11:03:36] [build-stdout] This script is running Python 3, but Python 2 is also available (as 'py -3')
[2025-06-03 11:03:36] [build-stdout] Could not guess Python version, will use default: Python 3
[2025-06-03 11:03:36] [build-stdout] Calling py -3 -S C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\python_tracer.py --verbosity 3 -z all -c E:\advance_javascript\codeQL\7\python-db-updated\working\trap_cache -R E:\advance_javascript\codeQL\7
[2025-06-03 11:03:37] [build-stdout] Python 3.13.2
[2025-06-03 11:03:37] [build-stdout] INFO: The Python extractor has recently stopped extracting the standard library by default. If you encounter problems, please let us know by submitting an issue to https://github.com/github/codeql. It is possible to re-enable extraction of the standard library by setting the environment variable CODEQL_EXTRACTOR_PYTHON_EXTRACT_STDLIB.
[2025-06-03 11:03:37] [build-stdout] [INFO] Extraction will use the Python 3 standard library.
[2025-06-03 11:03:37] [build-stdout] [INFO] sys_path is: ['C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\python\\tools', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313']
[2025-06-03 11:03:37] [build-stdout] [INFO] Python version 3.13.2
[2025-06-03 11:03:37] [build-stdout] [INFO] Python extractor version 7.1.2
[2025-06-03 11:03:38] [build-stdout] [INFO] [1] Extracted file E:\advance_javascript\codeQL\7\example.py in 44ms
[2025-06-03 11:03:38] [build-stdout] [INFO] [3] Extracted file E:\advance_javascript\codeQL\7\password_example.py in 42ms
[2025-06-03 11:03:38] [build-stdout] [INFO] Processed 2 modules in 1.16s
[2025-06-03 11:03:39] Plumbing command codeql database trace-command completed.
[2025-06-03 11:03:39] [PROGRESS] database create> Finalizing database at E:\advance_javascript\codeQL\7\python-db-updated.
[2025-06-03 11:03:39] Running plumbing command: codeql database finalize --no-db-cluster -- E:\advance_javascript\codeQL\7\python-db-updated
[2025-06-03 11:03:39] Using pre-finalize script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\pre-finalize.cmd.
[2025-06-03 11:03:39] [PROGRESS] database finalize> Running pre-finalize script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\pre-finalize.cmd in E:\advance_javascript\codeQL\7.
[2025-06-03 11:03:39] Running plumbing command: codeql database trace-command --working-dir=E:\advance_javascript\codeQL\7 --no-tracing -- E:\advance_javascript\codeQL\7\python-db-updated C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\pre-finalize.cmd
[2025-06-03 11:03:39] [PROGRESS] database trace-command> Running command in E:\advance_javascript\codeQL\7: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\pre-finalize.cmd]
[2025-06-03 11:03:40] [build-stderr] Scanning for files in E:\advance_javascript\codeQL\7...
[2025-06-03 11:03:40] [build-stderr] E:\advance_javascript\codeQL\7\python-db-updated: Indexing files in in E:\advance_javascript\codeQL\7...
[2025-06-03 11:03:40] [build-stderr] Running command in E:\advance_javascript\codeQL\7: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\tools\index-files.cmd, E:\advance_javascript\codeQL\7\python-db-updated\working\files-to-index8598505388616343197.list]
[2025-06-03 11:03:41] Plumbing command codeql database trace-command completed.
[2025-06-03 11:03:41] [PROGRESS] database finalize> Running TRAP import for CodeQL database at E:\advance_javascript\codeQL\7\python-db-updated...
[2025-06-03 11:03:41] Running plumbing command: codeql dataset import --dbscheme=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\semmlecode.python.dbscheme -- E:\advance_javascript\codeQL\7\python-db-updated\db-python E:\advance_javascript\codeQL\7\python-db-updated\trap\python
[2025-06-03 11:03:41] Clearing disk cache since the version file E:\advance_javascript\codeQL\7\python-db-updated\db-python\default\cache\version does not exist
[2025-06-03 11:03:41] Tuple pool not found. Clearing relations with cached strings
[2025-06-03 11:03:41] Trimming disk cache at E:\advance_javascript\codeQL\7\python-db-updated\db-python\default\cache in mode clear.
[2025-06-03 11:03:41] Sequence stamp origin is -6042293903679772629
[2025-06-03 11:03:41] Pausing evaluation to hard-clear memory at sequence stamp o+0
[2025-06-03 11:03:41] Unpausing evaluation
[2025-06-03 11:03:41] Pausing evaluation to quickly trim disk at sequence stamp o+1
[2025-06-03 11:03:41] Unpausing evaluation
[2025-06-03 11:03:41] Pausing evaluation to zealously trim disk at sequence stamp o+2
[2025-06-03 11:03:41] Unpausing evaluation
[2025-06-03 11:03:41] Trimming completed (13ms): Purged everything.
[2025-06-03 11:03:41] Scanning for files in E:\advance_javascript\codeQL\7\python-db-updated\trap\python
[2025-06-03 11:03:41] Found 13 TRAP files (480.42 KiB)
[2025-06-03 11:03:41] [PROGRESS] dataset import> Importing TRAP files
[2025-06-03 11:03:41] Importing $files.JbW2QRATLUJxI6d05Ofhgv1AVak=.trap.gz (1 of 13)
[2025-06-03 11:03:41] Importing $files.rBCEBlWW4E-xOSUAYXDHF96kvEM=.trap.gz (2 of 13)
[2025-06-03 11:03:41] Importing $flags.TUkIrPUrO-DT6kk_cRRsAR6Pg7U=.trap.gz (3 of 13)
[2025-06-03 11:03:41] Importing $interpreter3.U1drI33L_GaW4LSx8A59daT0Hng=.trap.gz (4 of 13)
[2025-06-03 11:03:41] Importing $stdlib_33.6T5mBjZdoRqkuAg77KUVI-QDaeM=.trap (5 of 13)
[2025-06-03 11:03:41] Importing codeql-database.yml.trap.gz (6 of 13)
[2025-06-03 11:03:41] Importing run-info-20250603.052958.460.yml.trap.gz (7 of 13)
[2025-06-03 11:03:41] Importing run-info-20250603.053134.924.yml.trap.gz (8 of 13)
[2025-06-03 11:03:41] Importing codeql-database.yml.trap.gz (9 of 13)
[2025-06-03 11:03:41] Importing qlpack.yml.trap.gz (10 of 13)
[2025-06-03 11:03:41] Importing example.py.qBaybTR43GgRTMW3NH9nxIsNtvE=.trap.gz (11 of 13)
[2025-06-03 11:03:41] Importing metadata.trap.gz (12 of 13)
[2025-06-03 11:03:41] Importing password_example.py.0ZgcCfBTmQcCYQPOCQup91UMeLw=.trap.gz (13 of 13)
[2025-06-03 11:03:41] [PROGRESS] dataset import> Merging relations
[2025-06-03 11:03:42] Merging 1 fragment for 'files'.
[2025-06-03 11:03:42] Merged 46 bytes for 'files'.
[2025-06-03 11:03:42] Merging 1 fragment for 'folders'.
[2025-06-03 11:03:42] Merged 42 bytes for 'folders'.
[2025-06-03 11:03:42] Merging 1 fragment for 'containerparent'.
[2025-06-03 11:03:42] Merged 60 bytes for 'containerparent'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_flags_versioned'.
[2025-06-03 11:03:42] Merged 182 bytes for 'py_flags_versioned'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_cobjects'.
[2025-06-03 11:03:42] Merged 4487 bytes (4.38 KiB) for 'py_cobjects'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_cobjecttypes'.
[2025-06-03 11:03:42] Merged 4892 bytes (4.78 KiB) for 'py_cobjecttypes'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_cobject_sources'.
[2025-06-03 11:03:42] Merged 6660 bytes (6.50 KiB) for 'py_cobject_sources'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_cobjectnames'.
[2025-06-03 11:03:42] Merged 5765 bytes (5.63 KiB) for 'py_cobjectnames'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_cmembers_versioned'.
[2025-06-03 11:03:42] Merged 6487 bytes (6.33 KiB) for 'py_cmembers_versioned'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_special_objects'.
[2025-06-03 11:03:42] Merged 135 bytes for 'py_special_objects'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_citems'.
[2025-06-03 11:03:42] Merged 545 bytes for 'py_citems'.
[2025-06-03 11:03:42] Merging 1 fragment for 'ext_rettype'.
[2025-06-03 11:03:42] Merged 4050 bytes (3.96 KiB) for 'ext_rettype'.
[2025-06-03 11:03:42] Merging 1 fragment for 'ext_argtype'.
[2025-06-03 11:03:42] Merged 6814 bytes (6.65 KiB) for 'ext_argtype'.
[2025-06-03 11:03:42] Merging 1 fragment for 'ext_argreturn'.
[2025-06-03 11:03:42] Merged 41 bytes for 'ext_argreturn'.
[2025-06-03 11:03:42] Merging 1 fragment for 'ext_proptype'.
[2025-06-03 11:03:42] Merged 343 bytes for 'ext_proptype'.
[2025-06-03 11:03:42] Merging 1 fragment for 'yaml_scalars'.
[2025-06-03 11:03:42] Merged 1198 bytes (1.17 KiB) for 'yaml_scalars'.
[2025-06-03 11:03:42] Merging 1 fragment for 'yaml'.
[2025-06-03 11:03:42] Merged 3064 bytes (2.99 KiB) for 'yaml'.
[2025-06-03 11:03:42] Merging 1 fragment for 'locations_default'.
[2025-06-03 11:03:42] Merged 3489 bytes (3.41 KiB) for 'locations_default'.
[2025-06-03 11:03:42] Merging 1 fragment for 'yaml_locations'.
[2025-06-03 11:03:42] Merged 1195 bytes (1.17 KiB) for 'yaml_locations'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_Modules'.
[2025-06-03 11:03:42] Merged 19 bytes for 'py_Modules'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_module_path'.
[2025-06-03 11:03:42] Merged 30 bytes for 'py_module_path'.
[2025-06-03 11:03:42] Merging 1 fragment for 'variable'.
[2025-06-03 11:03:42] Merged 142 bytes for 'variable'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_extracted_version'.
[2025-06-03 11:03:42] Merged 30 bytes for 'py_extracted_version'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_stmt_lists'.
[2025-06-03 11:03:42] Merged 62 bytes for 'py_stmt_lists'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_stmts'.
[2025-06-03 11:03:42] Merged 142 bytes for 'py_stmts'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_scopes'.
[2025-06-03 11:03:42] Merged 184 bytes for 'py_scopes'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_exprs'.
[2025-06-03 11:03:42] Merged 405 bytes for 'py_exprs'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_strs'.
[2025-06-03 11:03:42] Merged 239 bytes for 'py_strs'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_arguments'.
[2025-06-03 11:03:42] Merged 39 bytes for 'py_arguments'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_Functions'.
[2025-06-03 11:03:42] Merged 39 bytes for 'py_Functions'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_variables'.
[2025-06-03 11:03:42] Merged 112 bytes for 'py_variables'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_expr_contexts'.
[2025-06-03 11:03:42] Merged 145 bytes for 'py_expr_contexts'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_expr_lists'.
[2025-06-03 11:03:42] Merged 110 bytes for 'py_expr_lists'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_exports'.
[2025-06-03 11:03:42] Merged 50 bytes for 'py_exports'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_flow_bb_node'.
[2025-06-03 11:03:42] Merged 416 bytes for 'py_flow_bb_node'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_scope_flow'.
[2025-06-03 11:03:42] Merged 106 bytes for 'py_scope_flow'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_successors'.
[2025-06-03 11:03:42] Merged 241 bytes for 'py_successors'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_idoms'.
[2025-06-03 11:03:42] Merged 241 bytes for 'py_idoms'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_ssa_var'.
[2025-06-03 11:03:42] Merged 86 bytes for 'py_ssa_var'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_ssa_defn'.
[2025-06-03 11:03:42] Merged 79 bytes for 'py_ssa_defn'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_ssa_use'.
[2025-06-03 11:03:42] Merged 74 bytes for 'py_ssa_use'.
[2025-06-03 11:03:42] Merging 1 fragment for 'locations_ast'.
[2025-06-03 11:03:42] Merged 812 bytes for 'locations_ast'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_scope_location'.
[2025-06-03 11:03:42] Merged 49 bytes for 'py_scope_location'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_comments'.
[2025-06-03 11:03:42] Merged 76 bytes for 'py_comments'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_codelines'.
[2025-06-03 11:03:42] Merged 42 bytes for 'py_codelines'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_commentlines'.
[2025-06-03 11:03:42] Merged 43 bytes for 'py_commentlines'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_docstringlines'.
[2025-06-03 11:03:42] Merged 39 bytes for 'py_docstringlines'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_alllines'.
[2025-06-03 11:03:42] Merged 42 bytes for 'py_alllines'.
[2025-06-03 11:03:42] Merging 1 fragment for 'numlines'.
[2025-06-03 11:03:42] Merged 49 bytes for 'numlines'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_locations'.
[2025-06-03 11:03:42] Merged 280 bytes for 'py_locations'.
[2025-06-03 11:03:42] Merging 1 fragment for 'sourceLocationPrefix'.
[2025-06-03 11:03:42] Merged 17 bytes for 'sourceLocationPrefix'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_alias_lists'.
[2025-06-03 11:03:42] Merged 35 bytes for 'py_alias_lists'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_aliases'.
[2025-06-03 11:03:42] Merged 45 bytes for 'py_aliases'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_ints'.
[2025-06-03 11:03:42] Merged 31 bytes for 'py_ints'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_bools'.
[2025-06-03 11:03:42] Merged 33 bytes for 'py_bools'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_dict_item_lists'.
[2025-06-03 11:03:42] Merged 39 bytes for 'py_dict_item_lists'.
[2025-06-03 11:03:42] Merging 1 fragment for 'py_dict_items'.
[2025-06-03 11:03:42] Merged 91 bytes for 'py_dict_items'.
[2025-06-03 11:03:42] Saving string and id pools to disk.
[2025-06-03 11:03:42] Finished importing TRAP files.
[2025-06-03 11:03:42] Read 1.07 MiB of uncompressed TRAP data.
[2025-06-03 11:03:42] Uncompressed relation data size: 227.77 KiB
[2025-06-03 11:03:42] Relation data size: 52.94 KiB (merge rate: 489.94 KiB/s)
[2025-06-03 11:03:42] String pool size: 2.08 MiB
[2025-06-03 11:03:42] ID pool size: 1.09 MiB
[2025-06-03 11:03:42] [PROGRESS] dataset import> Finished writing database (relations: 52.94 KiB; string pool: 2.08 MiB).
[2025-06-03 11:03:42] Pausing evaluation to close the cache at sequence stamp o+117
[2025-06-03 11:03:42] The disk cache is freshly trimmed; leave it be.
[2025-06-03 11:03:42] Unpausing evaluation
[2025-06-03 11:03:42] Plumbing command codeql dataset import completed.
[2025-06-03 11:03:42] [PROGRESS] database finalize> TRAP import complete (1.3s).
[2025-06-03 11:03:42] Running plumbing command: codeql database cleanup -- E:\advance_javascript\codeQL\7\python-db-updated
[2025-06-03 11:03:42] [PROGRESS] database cleanup> Cleaning up existing TRAP files after import...
[2025-06-03 11:03:42] [PROGRESS] database cleanup> TRAP files cleaned up (4ms).
[2025-06-03 11:03:42] [PROGRESS] database cleanup> Cleaning up scratch directory...
[2025-06-03 11:03:42] [PROGRESS] database cleanup> Scratch directory cleaned up (1ms).
[2025-06-03 11:03:42] Running plumbing command: codeql dataset cleanup -- E:\advance_javascript\codeQL\7\python-db-updated\db-python
[2025-06-03 11:03:42] [PROGRESS] dataset cleanup> Cleaning up dataset in E:\advance_javascript\codeQL\7\python-db-updated\db-python.
[2025-06-03 11:03:42] Trimming disk cache at E:\advance_javascript\codeQL\7\python-db-updated\db-python\default\cache in mode trim.
[2025-06-03 11:03:42] Sequence stamp origin is -6042293898345827404
[2025-06-03 11:03:42] Pausing evaluation to quickly trim memory at sequence stamp o+0
[2025-06-03 11:03:42] Unpausing evaluation
[2025-06-03 11:03:42] Pausing evaluation to zealously trim disk at sequence stamp o+1
[2025-06-03 11:03:42] Unpausing evaluation
[2025-06-03 11:03:42] Trimming completed (3ms): Trimmed disposable data from cache.
[2025-06-03 11:03:42] Pausing evaluation to close the cache at sequence stamp o+2
[2025-06-03 11:03:42] The disk cache is freshly trimmed; leave it be.
[2025-06-03 11:03:42] Unpausing evaluation
[2025-06-03 11:03:42] [PROGRESS] dataset cleanup> Trimmed disposable data from cache.
[2025-06-03 11:03:42] [PROGRESS] dataset cleanup> Finalizing dataset in E:\advance_javascript\codeQL\7\python-db-updated\db-python
[2025-06-03 11:03:42] [DETAILS] dataset cleanup> Finished deleting ID pool from E:\advance_javascript\codeQL\7\python-db-updated\db-python (1ms).
[2025-06-03 11:03:42] Plumbing command codeql dataset cleanup completed.
[2025-06-03 11:03:42] Plumbing command codeql database cleanup completed with status 0.
[2025-06-03 11:03:42] [PROGRESS] database finalize> Finished zipping source archive (4.54 KiB).
[2025-06-03 11:03:42] Plumbing command codeql database finalize completed.
[2025-06-03 11:03:42] [PROGRESS] database create> Successfully created database at E:\advance_javascript\codeQL\7\python-db-updated.
[2025-06-03 11:03:42] Terminating normally.
