/**
 * @name SQL injection vulnerability in C#
 * @description Detects potential SQL injection vulnerabilities where user input is directly concatenated into SQL queries.
 * @kind problem
 * @problem.severity error
 * @id csharp/sql-injection
 */

import csharp

from MethodCall call, AddExpr concat
where
  // Look for SqlCommand constructor or ExecuteScalar/ExecuteReader calls
  (
    call.getTarget().hasName("SqlCommand") or
    call.getTarget().hasName("ExecuteScalar") or
    call.getTarget().hasName("ExecuteReader") or
    call.getTarget().hasName("ExecuteNonQuery")
  ) and
  // Check if the SQL query argument contains string concatenation
  call.getAnArgument() = concat and
  concat instanceof AddExpr and
  // Ensure one operand is a string literal (SQL query part)
  concat.getAnOperand() instanceof StringLiteral
select call, "Potential SQL injection vulnerability: SQL query uses string concatenation which may include unsanitized user input."
