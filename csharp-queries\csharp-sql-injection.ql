/**
 * @name SQL injection vulnerability in C#
 * @description Detects potential SQL injection vulnerabilities where user input is directly concatenated into SQL queries.
 * @kind problem
 * @problem.severity error
 * @id csharp/sql-injection
 */

import csharp

from StringLiteral sql
where
  sql.getValue().toLowerCase().matches("%select%") and
  sql.getParent() instanceof AddExpr
select sql, "Potential SQL injection vulnerability: SQL query contains SELECT statement in string concatenation."
