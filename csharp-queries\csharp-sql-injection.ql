/**
 * @name SQL injection vulnerability in C#
 * @description Detects potential SQL injection vulnerabilities where user input is directly concatenated into SQL queries.
 * @kind problem
 * @problem.severity error
 * @id csharp/sql-injection
 */

import csharp

from AddExpr concat, StringLiteral sqlString
where
  concat.getAnOperand() = sqlString and
  (
    sqlString.getValue().toLowerCase().matches("%select%") or
    sqlString.getValue().toLowerCase().matches("%insert%") or
    sqlString.getValue().toLowerCase().matches("%update%") or
    sqlString.getValue().toLowerCase().matches("%delete%") or
    sqlString.getValue().toLowerCase().matches("%where%")
  )
select concat, "Potential SQL injection vulnerability: SQL query '" + sqlString.getValue() + "' uses string concatenation."
