/**
 * @name SQL injection vulnerability in C#
 * @description Detects potential SQL injection vulnerabilities where user input is directly concatenated into SQL queries.
 * @kind problem
 * @problem.severity error
 * @id csharp/sql-injection
 */

import csharp

from AddExpr concat
where
  exists(StringLiteral sql |
    concat.getAnOperand() = sql and
    sql.getValue().toLowerCase().matches("%select%")
  )
select concat, "Potential SQL injection vulnerability: SQL query uses string concatenation."
