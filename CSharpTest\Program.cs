using System;

class Program
{
    // Hardcoded credentials - security vulnerability
    private static string password = "MySecretPassword123";
    private static string apiKey = "sk-1234567890abcdef";
    private static string secret_token = "ghp_xxxxxxxxxxxxxxxxxxxx";
    
    static void Main()
    {
        Console.WriteLine("Hello World!");
        
        // Simulate SQL injection vulnerability
        string userId = "1'; DROP TABLE Users; --";
        string query = "SELECT * FROM Users WHERE UserId = '" + userId + "'";
        Console.WriteLine(query);
        
        // More hardcoded secrets
        string jwt_secret = "my-jwt-secret-key";
        string encryption_key = "32-char-encryption-key-value!!";
        
        // Safe examples (should not be flagged)
        string passwordPrompt = "Enter your password:";
        string userInput = "";
    }
}
