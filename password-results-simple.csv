"Hardcoded credentials detection","Detects hardcoded passwords, API keys, and secrets in variable assignments.","error","Potential hardcoded credential in variable 'database_password': mySecretPassword123","/password_example.py","5","1","5","41"
"Hardcoded credentials detection","Detects hardcoded passwords, API keys, and secrets in variable assignments.","error","Potential hardcoded credential in variable 'admin_password': admin123","/password_example.py","6","1","6","27"
"Hardcoded credentials detection","Detects hardcoded passwords, API keys, and secrets in variable assignments.","error","Potential hardcoded credential in variable 'api_key': sk-1234567890abcdef","/password_example.py","7","1","7","31"
"Hardcoded credentials detection","Detects hardcoded passwords, API keys, and secrets in variable assignments.","error","Potential hardcoded credential in variable 'secret_token': ghp_xxxxxxxxxxxxxxxxxxxx","/password_example.py","8","1","8","41"
"Hardcoded credentials detection","Detects hardcoded passwords, API keys, and secrets in variable assignments.","error","Potential hardcoded credential in variable 'db_passwd': root123","/password_example.py","9","1","9","21"
"Hardcoded credentials detection","Detects hardcoded passwords, API keys, and secrets in variable assignments.","error","Potential hardcoded credential in variable 'API_SECRET_KEY': very-secret-key-12345","/password_example.py","20","1","20","40"
"Hardcoded credentials detection","Detects hardcoded passwords, API keys, and secrets in variable assignments.","error","Potential hardcoded credential in variable 'JWT_SECRET': jwt-signing-secret","/password_example.py","21","1","21","33"
"Hardcoded credentials detection","Detects hardcoded passwords, API keys, and secrets in variable assignments.","error","Potential hardcoded credential in variable 'ssh_password': ssh123!","/password_example.py","47","1","47","24"
"Hardcoded credentials detection","Detects hardcoded passwords, API keys, and secrets in variable assignments.","error","Potential hardcoded credential in variable 'ftp_pwd': ftppass","/password_example.py","48","1","48","19"
"Hardcoded credentials detection","Detects hardcoded passwords, API keys, and secrets in variable assignments.","error","Potential hardcoded credential in variable 'encryption_key': 32-char-encryption-key-here!!","/password_example.py","49","1","49","48"
