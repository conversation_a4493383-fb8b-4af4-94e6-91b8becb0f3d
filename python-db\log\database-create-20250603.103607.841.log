[2025-06-03 10:36:07] This is codeql database create python-db --language=python --source-root=.
[2025-06-03 10:36:07] Log file was started late.
[2025-06-03 10:36:07] [PROGRESS] database create> Initializing database at E:\advance_javascript\codeQL\7\python-db.
[2025-06-03 10:36:07] Running plumbing command: codeql database init --language=python --extractor-options-verbosity=1 --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --source-root=E:\advance_javascript\codeQL\7 --allow-missing-source-root=false --allow-already-existing -- E:\advance_javascript\codeQL\7\python-db
[2025-06-03 10:36:07] Calling plumbing command: codeql resolve languages --extractor-options-verbosity=1 --format=betterjson
[2025-06-03 10:36:08] [DETAILS] resolve languages> Scanning for [codeql-extractor.yml] from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\.codeqlmanifest.json
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\actions\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\cpp\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csv\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\go\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\html\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\java\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\properties\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\ruby\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\swift\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\xml\codeql-extractor.yml.
[2025-06-03 10:36:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\codeql-extractor.yml.
[2025-06-03 10:36:08] Plumbing command codeql resolve languages completed:
                      {
                        "aliases" : {
                          "c" : "cpp",
                          "c++" : "cpp",
                          "c-c++" : "cpp",
                          "c-cpp" : "cpp",
                          "c#" : "csharp",
                          "java-kotlin" : "java",
                          "kotlin" : "java",
                          "javascript-typescript" : "javascript",
                          "typescript" : "javascript"
                        },
                        "extractors" : {
                          "actions" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\actions",
                              "extractor_options" : { }
                            }
                          ],
                          "cpp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\cpp",
                              "extractor_options" : {
                                "scale_timeouts" : {
                                  "title" : "Value to scale compiler introspection timeouts with",
                                  "description" : "The extractor attempts to determine what compiler the source code being extracted is compiled with. To this end the extractor makes additional calls to the compiler, some of which are expected to return within a certain fixed time (either 10s or 15s). On some systems that are under high load this time might be too short, and can be scaled up using this option.\n",
                                  "type" : "string",
                                  "pattern" : "[0-9]+"
                                },
                                "log_verbosity" : {
                                  "title" : "Verbosity of the extractor logging",
                                  "description" : "Set the verbosity of the extractor logging to 'quiet' (0), 'normal' (1), 'chatty' (2), or 'noisy' (3). The default is 'normal'.\n",
                                  "type" : "string",
                                  "pattern" : "[0-3]"
                                }
                              }
                            }
                          ],
                          "csharp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csharp",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'brotli' (the default, to write brotli-compressed TRAP), 'gzip', and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip|brotli)$"
                                    }
                                  }
                                },
                                "buildless" : {
                                  "title" : "DEPRECATED - Whether to use buildless (standalone) extraction.",
                                  "description" : "DEPRECATED: Use `--build-mode none` instead.\nA value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the extractor. The supported levels are (in order of increasing verbosity):\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "binlog" : {
                                  "title" : "Binlog",
                                  "description" : "[EXPERIMENTAL] The value is a path to the MsBuild binary log file that should be extracted. This option only works when `--build-mode none` is also specified.\n",
                                  "type" : "array"
                                }
                              }
                            }
                          ],
                          "csv" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csv"
                            }
                          ],
                          "go" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\go",
                              "extractor_options" : {
                                "extract_tests" : {
                                  "title" : "Whether to include Go test files in the CodeQL database.",
                                  "description" : "A value indicating whether Go test files should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "extract_vendor_dirs" : {
                                  "title" : "Whether to include Go vendor directories in the CodeQL database.",
                                  "description" : "A value indicating whether Go vendor directories should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "html" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\html"
                            }
                          ],
                          "java" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\java",
                              "extractor_options" : {
                                "exclude" : {
                                  "title" : "A glob excluding files from analysis.",
                                  "description" : "A glob indicating what files to exclude from the analysis. This accepts glob patterns that are supported by Java's 'getPathMatcher' implementation.\n",
                                  "type" : "string"
                                },
                                "add_prefer_source" : {
                                  "title" : "Whether to always prefer source files over class files.",
                                  "description" : "A value indicating whether source files should be preferred over class files. If set to 'true', the extraction adds '-Xprefer:source' to the javac command line. If set to 'false', the extraction uses the default javac behavior ('-Xprefer:newer'). The default is 'true'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless" : {
                                  "title" : "Whether to use buildless (standalone) extraction (experimental).",
                                  "description" : "A value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless_dependency_dir" : {
                                  "title" : "The path where buildless (standalone) extraction should keep dependencies.",
                                  "description" : "If set, the buildless (standalone) extractor will store dependencies in this directory.\n",
                                  "type" : "string"
                                },
                                "minimize_dependency_jars" : {
                                  "title" : "Whether to rewrite and minimize downloaded JAR dependencies (experimental).",
                                  "description" : "If 'true', JAR dependencies downloaded during extraction will be rewritten to remove unneeded data, such as method bodies. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "javascript" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\javascript",
                              "extractor_options" : {
                                "skip_types" : {
                                  "title" : "Skip type extraction for TypeScript",
                                  "description" : "Whether to skip the extraction of types in a TypeScript application",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "properties" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\properties"
                            }
                          ],
                          "python" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\python",
                              "extractor_options" : {
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Python extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the CodeQL Python extractor.\nThe supported levels are (in order of increasing verbosity):\n\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "python_executable_name" : {
                                  "title" : "Controls the name of the Python executable used by the Python extractor.",
                                  "description" : "The Python extractor uses platform-dependent heuristics to determine the name of the Python executable to use. Specifying a value for this option overrides the name of the Python executable used by the extractor. Accepted values are py, python and python3. Use this setting with caution, the Python extractor requires Python 3 to run.\n",
                                  "type" : "string",
                                  "pattern" : "^(py|python|python3)$"
                                }
                              }
                            }
                          ],
                          "ruby" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\ruby",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'gzip' (the default, to write gzip-compressed TRAP) and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip)$"
                                    }
                                  }
                                }
                              }
                            }
                          ],
                          "swift" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\swift"
                            }
                          ],
                          "xml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\xml"
                            }
                          ],
                          "yaml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\yaml"
                            }
                          ]
                        }
                      }
[2025-06-03 10:36:08] [PROGRESS] database init> Calculating baseline information in E:\advance_javascript\codeQL\7
[2025-06-03 10:36:08] [SPAMMY] database init> Ignoring the following directories when processing baseline information: .git, .hg, .svn.
[2025-06-03 10:36:08] [DETAILS] database init> Running command in E:\advance_javascript\codeQL\7: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\scc.exe --by-file --exclude-dir .git,.hg,.svn --format json --no-large --no-min .
[2025-06-03 10:36:08] [SPAMMY] database init> Found 1 baseline files for python.
[2025-06-03 10:36:08] [PROGRESS] database init> Calculated baseline information for languages: python (104ms).
[2025-06-03 10:36:08] [PROGRESS] database init> Resolving extractor python.
[2025-06-03 10:36:08] [DETAILS] database init> Found candidate extractor root for python: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python.
[2025-06-03 10:36:08] [PROGRESS] database init> Successfully loaded extractor Python (python) from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python.
[2025-06-03 10:36:08] [PROGRESS] database init> Created skeleton CodeQL database at E:\advance_javascript\codeQL\7\python-db. This in-progress database is ready to be populated by an extractor.
[2025-06-03 10:36:08] Plumbing command codeql database init completed.
[2025-06-03 10:36:08] [PROGRESS] database create> Running build command: []
[2025-06-03 10:36:08] Running plumbing command: codeql database trace-command --working-dir=E:\advance_javascript\codeQL\7 --index-traceless-dbs --no-db-cluster -- E:\advance_javascript\codeQL\7\python-db
[2025-06-03 10:36:08] Using autobuild script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\autobuild.cmd.
[2025-06-03 10:36:08] [PROGRESS] database trace-command> Running command in E:\advance_javascript\codeQL\7: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\autobuild.cmd]
[2025-06-03 10:36:09] [build-stdout] Python 3.13.2
[2025-06-03 10:36:09] [build-stderr] No suitable Python runtime found
[2025-06-03 10:36:09] [build-stderr] Pass --list (-0) to see all detected environments on your machine
[2025-06-03 10:36:09] [build-stderr] or set environment variable PYLAUNCHER_ALLOW_INSTALL to use winget
[2025-06-03 10:36:09] [build-stderr] or open the Microsoft Store to the requested version.
[2025-06-03 10:36:10] [build-stdout] No directories containing root identifiers were found. Returning working directory as root.
[2025-06-03 10:36:10] [build-stdout] Will try to guess Python version, as it was not specified in `lgtm.yml`
[2025-06-03 10:36:10] [build-stdout] Trying to guess Python version based on Trove classifiers in setup.py
[2025-06-03 10:36:10] [build-stdout] Did not find setup.py (expected it to be at E:\advance_javascript\codeQL\7\setup.py)
[2025-06-03 10:36:10] [build-stdout] Trying to guess Python version based on travis file
[2025-06-03 10:36:10] [build-stdout] Did not find any travis files (expected them at either ['E:\\advance_javascript\\codeQL\\7\\.travis.yml', 'E:\\advance_javascript\\codeQL\\7\\travis.yml'])
[2025-06-03 10:36:10] [build-stdout] Trying to guess Python version based on installed versions
[2025-06-03 10:36:10] [build-stdout] Wanted to run Python 2, but it is not available. Using Python 3 instead
[2025-06-03 10:36:10] [build-stdout] This script is running Python 3, but Python 2 is also available (as 'py -3')
[2025-06-03 10:36:10] [build-stdout] Could not guess Python version, will use default: Python 3
[2025-06-03 10:36:10] [build-stdout] Calling py -3 -S C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\python_tracer.py --verbosity 3 -z all -c E:\advance_javascript\codeQL\7\python-db\working\trap_cache -R E:\advance_javascript\codeQL\7
[2025-06-03 10:36:10] [build-stdout] Python 3.13.2
[2025-06-03 10:36:10] [build-stdout] INFO: The Python extractor has recently stopped extracting the standard library by default. If you encounter problems, please let us know by submitting an issue to https://github.com/github/codeql. It is possible to re-enable extraction of the standard library by setting the environment variable CODEQL_EXTRACTOR_PYTHON_EXTRACT_STDLIB.
[2025-06-03 10:36:10] [build-stdout] [INFO] Extraction will use the Python 3 standard library.
[2025-06-03 10:36:10] [build-stdout] [INFO] sys_path is: ['C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\python\\tools', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313']
[2025-06-03 10:36:10] [build-stdout] [INFO] Python version 3.13.2
[2025-06-03 10:36:10] [build-stdout] [INFO] Python extractor version 7.1.2
[2025-06-03 10:36:11] [build-stdout] [INFO] [1] Extracted file E:\advance_javascript\codeQL\7\example.py in 64ms
[2025-06-03 10:36:12] [build-stdout] [INFO] Processed 1 modules in 1.18s
[2025-06-03 10:36:12] Plumbing command codeql database trace-command completed.
[2025-06-03 10:36:12] [PROGRESS] database create> Finalizing database at E:\advance_javascript\codeQL\7\python-db.
[2025-06-03 10:36:12] Running plumbing command: codeql database finalize --no-db-cluster -- E:\advance_javascript\codeQL\7\python-db
[2025-06-03 10:36:12] Using pre-finalize script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\pre-finalize.cmd.
[2025-06-03 10:36:12] [PROGRESS] database finalize> Running pre-finalize script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\pre-finalize.cmd in E:\advance_javascript\codeQL\7.
[2025-06-03 10:36:12] Running plumbing command: codeql database trace-command --working-dir=E:\advance_javascript\codeQL\7 --no-tracing -- E:\advance_javascript\codeQL\7\python-db C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\pre-finalize.cmd
[2025-06-03 10:36:12] [PROGRESS] database trace-command> Running command in E:\advance_javascript\codeQL\7: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\tools\pre-finalize.cmd]
[2025-06-03 10:36:13] [build-stderr] Scanning for files in E:\advance_javascript\codeQL\7...
[2025-06-03 10:36:13] [build-stderr] E:\advance_javascript\codeQL\7\python-db: Indexing files in in E:\advance_javascript\codeQL\7...
[2025-06-03 10:36:13] [build-stderr] Running command in E:\advance_javascript\codeQL\7: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\tools\index-files.cmd, E:\advance_javascript\codeQL\7\python-db\working\files-to-index8766245005983289623.list]
[2025-06-03 10:36:14] Plumbing command codeql database trace-command completed.
[2025-06-03 10:36:14] [PROGRESS] database finalize> Running TRAP import for CodeQL database at E:\advance_javascript\codeQL\7\python-db...
[2025-06-03 10:36:14] Running plumbing command: codeql dataset import --dbscheme=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\semmlecode.python.dbscheme -- E:\advance_javascript\codeQL\7\python-db\db-python E:\advance_javascript\codeQL\7\python-db\trap\python
[2025-06-03 10:36:15] Clearing disk cache since the version file E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\version does not exist
[2025-06-03 10:36:16] Tuple pool not found. Clearing relations with cached strings
[2025-06-03 10:36:16] Trimming disk cache at E:\advance_javascript\codeQL\7\python-db\db-python\default\cache in mode clear.
[2025-06-03 10:36:16] Sequence stamp origin is -6042300970028432241
[2025-06-03 10:36:16] Pausing evaluation to hard-clear memory at sequence stamp o+0
[2025-06-03 10:36:16] Unpausing evaluation
[2025-06-03 10:36:16] Pausing evaluation to quickly trim disk at sequence stamp o+1
[2025-06-03 10:36:16] Unpausing evaluation
[2025-06-03 10:36:16] Pausing evaluation to zealously trim disk at sequence stamp o+2
[2025-06-03 10:36:16] Unpausing evaluation
[2025-06-03 10:36:16] Trimming completed (13ms): Purged everything.
[2025-06-03 10:36:16] Scanning for files in E:\advance_javascript\codeQL\7\python-db\trap\python
[2025-06-03 10:36:16] Found 7 TRAP files (448.47 KiB)
[2025-06-03 10:36:16] [PROGRESS] dataset import> Importing TRAP files
[2025-06-03 10:36:16] Importing $files.SdeQWNnXo2YvCCdBmC3_YXqoFsA=.trap.gz (1 of 7)
[2025-06-03 10:36:16] Importing $flags.TUkIrPUrO-DT6kk_cRRsAR6Pg7U=.trap.gz (2 of 7)
[2025-06-03 10:36:16] Importing $interpreter3.U1drI33L_GaW4LSx8A59daT0Hng=.trap.gz (3 of 7)
[2025-06-03 10:36:16] Importing $stdlib_33.6T5mBjZdoRqkuAg77KUVI-QDaeM=.trap (4 of 7)
[2025-06-03 10:36:16] Importing codeql-database.yml.trap.gz (5 of 7)
[2025-06-03 10:36:16] Importing example.py.qBaybTR43GgRTMW3NH9nxIsNtvE=.trap.gz (6 of 7)
[2025-06-03 10:36:16] Importing metadata.trap.gz (7 of 7)
[2025-06-03 10:36:16] [PROGRESS] dataset import> Merging relations
[2025-06-03 10:36:16] Merging 1 fragment for 'files'.
[2025-06-03 10:36:16] Merged 32 bytes for 'files'.
[2025-06-03 10:36:16] Merging 1 fragment for 'folders'.
[2025-06-03 10:36:16] Merged 36 bytes for 'folders'.
[2025-06-03 10:36:16] Merging 1 fragment for 'containerparent'.
[2025-06-03 10:36:16] Merged 42 bytes for 'containerparent'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_flags_versioned'.
[2025-06-03 10:36:16] Merged 182 bytes for 'py_flags_versioned'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_cobjects'.
[2025-06-03 10:36:16] Merged 4428 bytes (4.32 KiB) for 'py_cobjects'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_cobjecttypes'.
[2025-06-03 10:36:16] Merged 4718 bytes (4.61 KiB) for 'py_cobjecttypes'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_cobject_sources'.
[2025-06-03 10:36:16] Merged 6601 bytes (6.45 KiB) for 'py_cobject_sources'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_cobjectnames'.
[2025-06-03 10:36:16] Merged 5649 bytes (5.52 KiB) for 'py_cobjectnames'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_cmembers_versioned'.
[2025-06-03 10:36:16] Merged 6487 bytes (6.33 KiB) for 'py_cmembers_versioned'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_special_objects'.
[2025-06-03 10:36:16] Merged 135 bytes for 'py_special_objects'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_citems'.
[2025-06-03 10:36:16] Merged 545 bytes for 'py_citems'.
[2025-06-03 10:36:16] Merging 1 fragment for 'ext_rettype'.
[2025-06-03 10:36:16] Merged 4050 bytes (3.96 KiB) for 'ext_rettype'.
[2025-06-03 10:36:16] Merging 1 fragment for 'ext_argtype'.
[2025-06-03 10:36:16] Merged 6814 bytes (6.65 KiB) for 'ext_argtype'.
[2025-06-03 10:36:16] Merging 1 fragment for 'ext_argreturn'.
[2025-06-03 10:36:16] Merged 41 bytes for 'ext_argreturn'.
[2025-06-03 10:36:16] Merging 1 fragment for 'ext_proptype'.
[2025-06-03 10:36:16] Merged 343 bytes for 'ext_proptype'.
[2025-06-03 10:36:16] Merging 1 fragment for 'yaml_scalars'.
[2025-06-03 10:36:16] Merged 140 bytes for 'yaml_scalars'.
[2025-06-03 10:36:16] Merging 1 fragment for 'yaml'.
[2025-06-03 10:36:16] Merged 502 bytes for 'yaml'.
[2025-06-03 10:36:16] Merging 1 fragment for 'locations_default'.
[2025-06-03 10:36:16] Merged 440 bytes for 'locations_default'.
[2025-06-03 10:36:16] Merging 1 fragment for 'yaml_locations'.
[2025-06-03 10:36:16] Merged 164 bytes for 'yaml_locations'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_Modules'.
[2025-06-03 10:36:16] Merged 18 bytes for 'py_Modules'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_module_path'.
[2025-06-03 10:36:16] Merged 28 bytes for 'py_module_path'.
[2025-06-03 10:36:16] Merging 1 fragment for 'variable'.
[2025-06-03 10:36:16] Merged 64 bytes for 'variable'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_extracted_version'.
[2025-06-03 10:36:16] Merged 27 bytes for 'py_extracted_version'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_stmt_lists'.
[2025-06-03 10:36:16] Merged 46 bytes for 'py_stmt_lists'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_stmts'.
[2025-06-03 10:36:16] Merged 64 bytes for 'py_stmts'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_scopes'.
[2025-06-03 10:36:16] Merged 60 bytes for 'py_scopes'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_exprs'.
[2025-06-03 10:36:16] Merged 99 bytes for 'py_exprs'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_strs'.
[2025-06-03 10:36:16] Merged 59 bytes for 'py_strs'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_arguments'.
[2025-06-03 10:36:16] Merged 33 bytes for 'py_arguments'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_Functions'.
[2025-06-03 10:36:16] Merged 33 bytes for 'py_Functions'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_variables'.
[2025-06-03 10:36:16] Merged 44 bytes for 'py_variables'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_expr_contexts'.
[2025-06-03 10:36:16] Merged 58 bytes for 'py_expr_contexts'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_expr_lists'.
[2025-06-03 10:36:16] Merged 54 bytes for 'py_expr_lists'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_exports'.
[2025-06-03 10:36:16] Merged 31 bytes for 'py_exports'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_flow_bb_node'.
[2025-06-03 10:36:16] Merged 113 bytes for 'py_flow_bb_node'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_scope_flow'.
[2025-06-03 10:36:16] Merged 63 bytes for 'py_scope_flow'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_successors'.
[2025-06-03 10:36:16] Merged 59 bytes for 'py_successors'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_idoms'.
[2025-06-03 10:36:16] Merged 59 bytes for 'py_idoms'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_ssa_var'.
[2025-06-03 10:36:16] Merged 39 bytes for 'py_ssa_var'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_ssa_defn'.
[2025-06-03 10:36:16] Merged 35 bytes for 'py_ssa_defn'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_ssa_use'.
[2025-06-03 10:36:16] Merged 42 bytes for 'py_ssa_use'.
[2025-06-03 10:36:16] Merging 1 fragment for 'locations_ast'.
[2025-06-03 10:36:16] Merged 163 bytes for 'locations_ast'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_scope_location'.
[2025-06-03 10:36:16] Merged 36 bytes for 'py_scope_location'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_comments'.
[2025-06-03 10:36:16] Merged 46 bytes for 'py_comments'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_codelines'.
[2025-06-03 10:36:16] Merged 35 bytes for 'py_codelines'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_commentlines'.
[2025-06-03 10:36:16] Merged 36 bytes for 'py_commentlines'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_docstringlines'.
[2025-06-03 10:36:16] Merged 35 bytes for 'py_docstringlines'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_alllines'.
[2025-06-03 10:36:16] Merged 35 bytes for 'py_alllines'.
[2025-06-03 10:36:16] Merging 1 fragment for 'numlines'.
[2025-06-03 10:36:16] Merged 45 bytes for 'numlines'.
[2025-06-03 10:36:16] Merging 1 fragment for 'py_locations'.
[2025-06-03 10:36:16] Merged 63 bytes for 'py_locations'.
[2025-06-03 10:36:16] Merging 1 fragment for 'sourceLocationPrefix'.
[2025-06-03 10:36:16] Merged 17 bytes for 'sourceLocationPrefix'.
[2025-06-03 10:36:16] Saving string and id pools to disk.
[2025-06-03 10:36:17] Finished importing TRAP files.
[2025-06-03 10:36:17] Read 933.62 KiB of uncompressed TRAP data.
[2025-06-03 10:36:17] Uncompressed relation data size: 180.96 KiB
[2025-06-03 10:36:17] Relation data size: 41.98 KiB (merge rate: 432.35 KiB/s)
[2025-06-03 10:36:17] String pool size: 2.06 MiB
[2025-06-03 10:36:17] ID pool size: 1.09 MiB
[2025-06-03 10:36:17] [PROGRESS] dataset import> Finished writing database (relations: 41.98 KiB; string pool: 2.06 MiB).
[2025-06-03 10:36:17] Pausing evaluation to close the cache at sequence stamp o+105
[2025-06-03 10:36:17] The disk cache is freshly trimmed; leave it be.
[2025-06-03 10:36:17] Unpausing evaluation
[2025-06-03 10:36:17] Plumbing command codeql dataset import completed.
[2025-06-03 10:36:17] [PROGRESS] database finalize> TRAP import complete (2.8s).
[2025-06-03 10:36:17] Running plumbing command: codeql database cleanup -- E:\advance_javascript\codeQL\7\python-db
[2025-06-03 10:36:17] [PROGRESS] database cleanup> Cleaning up existing TRAP files after import...
[2025-06-03 10:36:17] [PROGRESS] database cleanup> TRAP files cleaned up (2ms).
[2025-06-03 10:36:17] [PROGRESS] database cleanup> Cleaning up scratch directory...
[2025-06-03 10:36:17] [PROGRESS] database cleanup> Scratch directory cleaned up (1ms).
[2025-06-03 10:36:17] Running plumbing command: codeql dataset cleanup -- E:\advance_javascript\codeQL\7\python-db\db-python
[2025-06-03 10:36:17] [PROGRESS] dataset cleanup> Cleaning up dataset in E:\advance_javascript\codeQL\7\python-db\db-python.
[2025-06-03 10:36:17] Trimming disk cache at E:\advance_javascript\codeQL\7\python-db\db-python\default\cache in mode trim.
[2025-06-03 10:36:17] Sequence stamp origin is -6042300965263157865
[2025-06-03 10:36:17] Pausing evaluation to quickly trim memory at sequence stamp o+0
[2025-06-03 10:36:17] Unpausing evaluation
[2025-06-03 10:36:17] Pausing evaluation to zealously trim disk at sequence stamp o+1
[2025-06-03 10:36:17] Unpausing evaluation
[2025-06-03 10:36:17] Trimming completed (4ms): Trimmed disposable data from cache.
[2025-06-03 10:36:17] Pausing evaluation to close the cache at sequence stamp o+2
[2025-06-03 10:36:17] The disk cache is freshly trimmed; leave it be.
[2025-06-03 10:36:17] Unpausing evaluation
[2025-06-03 10:36:17] [PROGRESS] dataset cleanup> Trimmed disposable data from cache.
[2025-06-03 10:36:17] [PROGRESS] dataset cleanup> Finalizing dataset in E:\advance_javascript\codeQL\7\python-db\db-python
[2025-06-03 10:36:17] [DETAILS] dataset cleanup> Finished deleting ID pool from E:\advance_javascript\codeQL\7\python-db\db-python (2ms).
[2025-06-03 10:36:17] Plumbing command codeql dataset cleanup completed.
[2025-06-03 10:36:17] Plumbing command codeql database cleanup completed with status 0.
[2025-06-03 10:36:17] [PROGRESS] database finalize> Finished zipping source archive (912.00 B).
[2025-06-03 10:36:17] Plumbing command codeql database finalize completed.
[2025-06-03 10:36:17] [PROGRESS] database create> Successfully created database at E:\advance_javascript\codeQL\7\python-db.
[2025-06-03 10:36:17] Terminating normally.
