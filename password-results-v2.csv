"Hardcoded passwords and sensitive credentials","Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.","error","Hardcoded credential found in variable 'database_password' - this poses a security risk.","/password_example.py","5","1","5","41"
"Hardcoded passwords and sensitive credentials","Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.","error","Hardcoded credential found in variable 'admin_password' - this poses a security risk.","/password_example.py","6","1","6","27"
"Hardcoded passwords and sensitive credentials","Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.","error","Hardcoded credential found in variable 'api_key' - this poses a security risk.","/password_example.py","7","1","7","31"
"Hardcoded passwords and sensitive credentials","Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.","error","Hardcoded credential found in variable 'secret_token' - this poses a security risk.","/password_example.py","8","1","8","41"
"Hardcoded passwords and sensitive credentials","Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.","error","Hardcoded credential found in variable 'db_passwd' - this poses a security risk.","/password_example.py","9","1","9","21"
"Hardcoded passwords and sensitive credentials","Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.","error","Hardcoded credential found in variable 'API_SECRET_KEY' - this poses a security risk.","/password_example.py","20","1","20","40"
"Hardcoded passwords and sensitive credentials","Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.","error","Hardcoded credential found in variable 'JWT_SECRET' - this poses a security risk.","/password_example.py","21","1","21","33"
"Hardcoded passwords and sensitive credentials","Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.","error","Hardcoded credential found in variable 'password_prompt' - this poses a security risk.","/password_example.py","24","1","24","40"
"Hardcoded passwords and sensitive credentials","Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.","error","Hardcoded credential found in variable 'ssh_password' - this poses a security risk.","/password_example.py","47","1","47","24"
"Hardcoded passwords and sensitive credentials","Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.","error","Hardcoded credential found in variable 'ftp_pwd' - this poses a security risk.","/password_example.py","48","1","48","19"
"Hardcoded passwords and sensitive credentials","Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.","error","Hardcoded credential found in variable 'encryption_key' - this poses a security risk.","/password_example.py","49","1","49","48"
