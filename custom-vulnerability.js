// Custom vulnerability injection for CodeQL detection
const express = require('express');
const router = express.Router();

// CUSTOM VULNERABILITY: Specific pattern for detection
// This is our target vulnerability that we'll create a custom query for
const VAL_VAR = "I am hacker's paradise";
const DANGER_ZONE = "I am hacker's paradise";
const EXPLOIT_ME = "I am hacker's paradise";
var password = "supersecret";
let apiKey = "abcdef123456";

console.log("password: " + password);
console.log("apiKey: " + apiKey);
console.log("I am hacker's paradise")
// Function that uses the vulnerable pattern
function processUserData(userData) {
    // This pattern should be detected by our custom query
    if (userData.includes("I am hacker's paradise")) {
        console.log("Dangerous pattern detected!");
        return userData;
    }
    return "safe";
}

// Another instance of the vulnerability
function authenticateUser(credentials) {
    const secretKey = "I am hacker's paradise";
    
    if (credentials.password === secretKey) {
        return { success: true, message: "Access granted" };
    }
    return { success: false, message: "Access denied" };
}

// FALSE POSITIVE SCENARIOS (should NOT be detected)
// These look similar but are legitimate uses
const helpMessage = "Enter your password here";
const promptText = "I am a legitimate user message";
const documentationText = "This is example text for documentation";
const configComment = "// This is a configuration comment";

// Function with false positive pattern
function displayHelpText() {
    const helpText = "Enter your password here";
    console.log(helpText);
    return helpText;
}

// Another false positive
function generateUserPrompt() {
    const prompt = "I am a legitimate user message";
    return prompt;
}

// Export for testing
module.exports = {
    processUserData,
    authenticateUser,
    displayHelpText,
    generateUserPrompt,
    VAL_VAR,
    DANGER_ZONE,
    EXPLOIT_ME
};
