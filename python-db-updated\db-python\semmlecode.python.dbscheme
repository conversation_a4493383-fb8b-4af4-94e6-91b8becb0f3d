/*
 *      This dbscheme is auto-generated by 'semmle/dbscheme_gen.py'.
 *      WARNING: Any modifications to this file will be lost.
 *      Relations can be changed by modifying master.py or
 *      by adding rules to dbscheme.template
 */

/* This is a dummy line to alter the dbscheme, so we can make a database upgrade
 * without actually changing any of the dbscheme predicates. It contains a date
 * to allow for such updates in the future as well.
 *
 * 2020-07-02
 *
 * DO NOT remove this comment carelessly, since it can revert the dbscheme back to a
 * previously seen state (matching a previously seen SHA), which would make the upgrade
 * mechanism not work properly.
 */

/*- DEPRECATED: External defects and metrics -*/

externalDefects(
  unique int id : @externalDefect,
  varchar(900) queryPath : string ref,
  int location : @location ref,
  varchar(900) message : string ref,
  float severity : float ref
);

externalMetrics(
  unique int id : @externalMetric,
  varchar(900) queryPath : string ref,
  int location : @location ref,
  float value : float ref
);

/*- External data -*/

/**
 * External data, loaded from CSV files during snapshot creation. See
 * [Tutorial: Incorporating external data](https://help.semmle.com/wiki/display/SD/Tutorial%3A+Incorporating+external+data)
 * for more information.
 */
externalData(
  int id : @externalDataElement,
  string path : string ref,
  int column: int ref,
  string value : string ref
);

/*- DEPRECATED: Snapshot date -*/

snapshotDate(unique date snapshotDate : date ref);

/*- Source location prefix -*/

/**
 * The source location of the snapshot.
 */
sourceLocationPrefix(string prefix : string ref);

/*- DEPRECATED: Duplicate code -*/

duplicateCode(
  unique int id : @duplication,
  string relativePath : string ref,
  int equivClass : int ref
);

similarCode(
  unique int id : @similarity,
  string relativePath : string ref,
  int equivClass : int ref
);

@duplication_or_similarity = @duplication | @similarity

tokens(
  int id : @duplication_or_similarity ref,
  int offset : int ref,
  int beginLine : int ref,
  int beginColumn : int ref,
  int endLine : int ref,
  int endColumn : int ref
);

/*- DEPRECATED: Version control data -*/

svnentries(
  unique int id : @svnentry,
  string revision : string ref,
  string author : string ref,
  date revisionDate : date ref,
  int changeSize : int ref
)

svnaffectedfiles(
  int id : @svnentry ref,
  int file : @file ref,
  string action : string ref
)

svnentrymsg(
  unique int id : @svnentry ref,
  string message : string ref
)

svnchurn(
  int commit : @svnentry ref,
  int file : @file ref,
  int addedLines : int ref,
  int deletedLines : int ref
)

/*- Lines of code -*/

numlines(
  int element_id: @sourceline ref,
  int num_lines: int ref,
  int num_code: int ref,
  int num_comment: int ref
);

/*- Files and folders -*/

/**
 * The location of an element.
 * The location spans column `startcolumn` of line `startline` to
 * column `endcolumn` of line `endline` in file `file`.
 * For more information, see
 * [Locations](https://codeql.github.com/docs/writing-codeql-queries/providing-locations-in-codeql-queries/).
 */
locations_default(
  unique int id: @location_default,
  int file: @file ref,
  int beginLine: int ref,
  int beginColumn: int ref,
  int endLine: int ref,
  int endColumn: int ref
);

files(
  unique int id: @file,
  string name: string ref
);

folders(
  unique int id: @folder,
  string name: string ref
);

@container = @file | @folder

containerparent(
  int parent: @container ref,
  unique int child: @container ref
);

/*- XML Files -*/

xmlEncoding(
  unique int id: @file ref,
  string encoding: string ref
);

xmlDTDs(
  unique int id: @xmldtd,
  string root: string ref,
  string publicId: string ref,
  string systemId: string ref,
  int fileid: @file ref
);

xmlElements(
  unique int id: @xmlelement,
  string name: string ref,
  int parentid: @xmlparent ref,
  int idx: int ref,
  int fileid: @file ref
);

xmlAttrs(
  unique int id: @xmlattribute,
  int elementid: @xmlelement ref,
  string name: string ref,
  string value: string ref,
  int idx: int ref,
  int fileid: @file ref
);

xmlNs(
  int id: @xmlnamespace,
  string prefixName: string ref,
  string URI: string ref,
  int fileid: @file ref
);

xmlHasNs(
  int elementId: @xmlnamespaceable ref,
  int nsId: @xmlnamespace ref,
  int fileid: @file ref
);

xmlComments(
  unique int id: @xmlcomment,
  string text: string ref,
  int parentid: @xmlparent ref,
  int fileid: @file ref
);

xmlChars(
  unique int id: @xmlcharacters,
  string text: string ref,
  int parentid: @xmlparent ref,
  int idx: int ref,
  int isCDATA: int ref,
  int fileid: @file ref
);

@xmlparent = @file | @xmlelement;
@xmlnamespaceable = @xmlelement | @xmlattribute;

xmllocations(
  int xmlElement: @xmllocatable ref,
  int location: @location_default ref
);

@xmllocatable = @xmlcharacters | @xmlelement | @xmlcomment | @xmlattribute | @xmldtd | @file | @xmlnamespace;

/*- YAML -*/

#keyset[parent, idx]
yaml (unique int id: @yaml_node,
      int kind: int ref,
      int parent: @yaml_node_parent ref,
      int idx: int ref,
      string tag: string ref,
      string tostring: string ref);

case @yaml_node.kind of
  0 = @yaml_scalar_node
| 1 = @yaml_mapping_node
| 2 = @yaml_sequence_node
| 3 = @yaml_alias_node
;

@yaml_collection_node = @yaml_mapping_node | @yaml_sequence_node;

@yaml_node_parent = @yaml_collection_node | @file;

yaml_anchors (unique int node: @yaml_node ref,
              string anchor: string ref);

yaml_aliases (unique int alias: @yaml_alias_node ref,
              string target: string ref);

yaml_scalars (unique int scalar: @yaml_scalar_node ref,
              int style: int ref,
              string value: string ref);

yaml_errors (unique int id: @yaml_error,
             string message: string ref);

yaml_locations(unique int locatable: @yaml_locatable ref,
             int location: @location_default ref);

@yaml_locatable = @yaml_node | @yaml_error;

/*- Python dbscheme -*/

/*
 * Line metrics
 */
py_codelines(int id : @py_scope ref,
             int count : int ref);

py_commentlines(int id : @py_scope ref,
                int count : int ref);

py_docstringlines(int id : @py_scope ref,
                int count : int ref);

py_alllines(int id : @py_scope ref,
                int count : int ref);

/****************************
        Python dbscheme
****************************/

@sourceline = @file | @py_Module | @xmllocatable;

@location = @location_ast | @location_default ;

locations_ast(unique int id: @location_ast,
          int module: @py_Module ref,
          int beginLine: int ref,
          int beginColumn: int ref,
          int endLine: int ref,
          int endColumn: int ref);

file_contents(unique int file: @file ref, string contents: string ref);

py_module_path(int module: @py_Module ref, int file: @container ref);

variable(unique int id : @py_variable,
         int scope : @py_scope ref,
         varchar(1) name : string ref);

py_line_lengths(unique int id : @py_line,
                int file: @py_Module ref,
                int line : int ref,
                int length : int ref);

py_extracted_version(int module : @py_Module ref,
                     varchar(1) version : string ref);

/* AUTO GENERATED PART STARTS HERE */


/* <Field> AnnAssign.location = 0, location */
/* <Field> AnnAssign.value = 1, expr */
/* <Field> AnnAssign.annotation = 2, expr */
/* <Field> AnnAssign.target = 3, expr */

/* <Field> Assert.location = 0, location */
/* <Field> Assert.test = 1, expr */
/* <Field> Assert.msg = 2, expr */

/* <Field> Assign.location = 0, location */
/* <Field> Assign.value = 1, expr */
/* <Field> Assign.targets = 2, expr_list */

/* <Field> AssignExpr.location = 0, location */
/* <Field> AssignExpr.parenthesised = 1, bool */
/* <Field> AssignExpr.value = 2, expr */
/* <Field> AssignExpr.target = 3, expr */

/* <Field> Attribute.location = 0, location */
/* <Field> Attribute.parenthesised = 1, bool */
/* <Field> Attribute.value = 2, expr */
/* <Field> Attribute.attr = 3, str */
/* <Field> Attribute.ctx = 4, expr_context */

/* <Field> AugAssign.location = 0, location */
/* <Field> AugAssign.operation = 1, BinOp */

/* <Field> Await.location = 0, location */
/* <Field> Await.parenthesised = 1, bool */
/* <Field> Await.value = 2, expr */

/* <Field> BinaryExpr.location = 0, location */
/* <Field> BinaryExpr.parenthesised = 1, bool */
/* <Field> BinaryExpr.left = 2, expr */
/* <Field> BinaryExpr.op = 3, operator */
/* <Field> BinaryExpr.right = 4, expr */
/* <Parent> BinaryExpr = AugAssign  */

/* <Field> BoolExpr.location = 0, location */
/* <Field> BoolExpr.parenthesised = 1, bool */
/* <Field> BoolExpr.op = 2, boolop */
/* <Field> BoolExpr.values = 3, expr_list */

/* <Field> Break.location = 0, location */

/* <Field> Bytes.location = 0, location */
/* <Field> Bytes.parenthesised = 1, bool */
/* <Field> Bytes.s = 2, bytes */
/* <Field> Bytes.prefix = 3, bytes */
/* <Field> Bytes.implicitly_concatenated_parts = 4, StringPart_list */

/* <Field> Call.location = 0, location */
/* <Field> Call.parenthesised = 1, bool */
/* <Field> Call.func = 2, expr */
/* <Field> Call.positional_args = 3, expr_list */
/* <Field> Call.named_args = 4, dict_item_list */

/* <Field> Case.location = 0, location */
/* <Field> Case.pattern = 1, pattern */
/* <Field> Case.guard = 2, expr */
/* <Field> Case.body = 3, stmt_list */

/* <Field> Class.name = 0, str */
/* <Field> Class.body = 1, stmt_list */
/* <Parent> Class = ClassExpr  */

/* <Field> ClassExpr.location = 0, location */
/* <Field> ClassExpr.parenthesised = 1, bool */
/* <Field> ClassExpr.name = 2, str */
/* <Field> ClassExpr.bases = 3, expr_list */
/* <Field> ClassExpr.keywords = 4, dict_item_list */
/* <Field> ClassExpr.inner_scope = 5, Class */
/* <Field> ClassExpr.type_parameters = 6, type_parameter_list */

/* <Field> Compare.location = 0, location */
/* <Field> Compare.parenthesised = 1, bool */
/* <Field> Compare.left = 2, expr */
/* <Field> Compare.ops = 3, cmpop_list */
/* <Field> Compare.comparators = 4, expr_list */

/* <Field> Continue.location = 0, location */

/* <Field> Delete.location = 0, location */
/* <Field> Delete.targets = 1, expr_list */

/* <Field> Dict.location = 0, location */
/* <Field> Dict.parenthesised = 1, bool */
/* <Field> Dict.items = 2, dict_item_list */

/* <Field> DictComp.location = 0, location */
/* <Field> DictComp.parenthesised = 1, bool */
/* <Field> DictComp.function = 2, Function */
/* <Field> DictComp.iterable = 3, expr */

/* <Field> DictUnpacking.location = 0, location */
/* <Field> DictUnpacking.value = 1, expr */

/* <Field> Ellipsis.location = 0, location */
/* <Field> Ellipsis.parenthesised = 1, bool */

/* <Field> ExceptGroupStmt.location = 0, location */
/* <Field> ExceptGroupStmt.type = 1, expr */
/* <Field> ExceptGroupStmt.name = 2, expr */
/* <Field> ExceptGroupStmt.body = 3, stmt_list */

/* <Field> ExceptStmt.location = 0, location */
/* <Field> ExceptStmt.type = 1, expr */
/* <Field> ExceptStmt.name = 2, expr */
/* <Field> ExceptStmt.body = 3, stmt_list */

/* <Field> Exec.location = 0, location */
/* <Field> Exec.body = 1, expr */
/* <Field> Exec.globals = 2, expr */
/* <Field> Exec.locals = 3, expr */

/* <Field> ExprStmt.location = 0, location */
/* <Field> ExprStmt.value = 1, expr */

/* <Field> Filter.location = 0, location */
/* <Field> Filter.parenthesised = 1, bool */
/* <Field> Filter.value = 2, expr */
/* <Field> Filter.filter = 3, expr */

/* <Field> For.location = 0, location */
/* <Field> For.target = 1, expr */
/* <Field> For.iter = 2, expr */
/* <Field> For.body = 3, stmt_list */
/* <Field> For.orelse = 4, stmt_list */
/* <Field> For.is_async = 5, bool */

/* <Field> FormattedValue.location = 0, location */
/* <Field> FormattedValue.parenthesised = 1, bool */
/* <Field> FormattedValue.value = 2, expr */
/* <Field> FormattedValue.conversion = 3, str */
/* <Field> FormattedValue.format_spec = 4, JoinedStr */

/* <Field> Function.name = 0, str */
/* <Field> Function.args = 1, parameter_list */
/* <Field> Function.vararg = 2, expr */
/* <Field> Function.kwonlyargs = 3, expr_list */
/* <Field> Function.kwarg = 4, expr */
/* <Field> Function.body = 5, stmt_list */
/* <Field> Function.is_async = 6, bool */
/* <Field> Function.type_parameters = 7, type_parameter_list */
/* <Parent> Function = FunctionParent  */

/* <Field> FunctionExpr.location = 0, location */
/* <Field> FunctionExpr.parenthesised = 1, bool */
/* <Field> FunctionExpr.name = 2, str */
/* <Field> FunctionExpr.args = 3, arguments */
/* <Field> FunctionExpr.returns = 4, expr */
/* <Field> FunctionExpr.inner_scope = 5, Function */

/* <Field> GeneratorExp.location = 0, location */
/* <Field> GeneratorExp.parenthesised = 1, bool */
/* <Field> GeneratorExp.function = 2, Function */
/* <Field> GeneratorExp.iterable = 3, expr */

/* <Field> Global.location = 0, location */
/* <Field> Global.names = 1, str_list */

/* <Field> Guard.location = 0, location */
/* <Field> Guard.parenthesised = 1, bool */
/* <Field> Guard.test = 2, expr */

/* <Field> If.location = 0, location */
/* <Field> If.test = 1, expr */
/* <Field> If.body = 2, stmt_list */
/* <Field> If.orelse = 3, stmt_list */

/* <Field> IfExp.location = 0, location */
/* <Field> IfExp.parenthesised = 1, bool */
/* <Field> IfExp.test = 2, expr */
/* <Field> IfExp.body = 3, expr */
/* <Field> IfExp.orelse = 4, expr */

/* <Field> Import.location = 0, location */
/* <Field> Import.names = 1, alias_list */

/* <Field> ImportExpr.location = 0, location */
/* <Field> ImportExpr.parenthesised = 1, bool */
/* <Field> ImportExpr.level = 2, int */
/* <Field> ImportExpr.name = 3, str */
/* <Field> ImportExpr.top = 4, bool */

/* <Field> ImportStar.location = 0, location */
/* <Field> ImportStar.module = 1, expr */

/* <Field> ImportMember.location = 0, location */
/* <Field> ImportMember.parenthesised = 1, bool */
/* <Field> ImportMember.module = 2, expr */
/* <Field> ImportMember.name = 3, str */

/* <Field> Fstring.location = 0, location */
/* <Field> Fstring.parenthesised = 1, bool */
/* <Field> Fstring.values = 2, expr_list */
/* <Parent> Fstring = FormattedValue  */

/* <Field> KeyValuePair.location = 0, location */
/* <Field> KeyValuePair.value = 1, expr */
/* <Field> KeyValuePair.key = 2, expr */

/* <Field> Lambda.location = 0, location */
/* <Field> Lambda.parenthesised = 1, bool */
/* <Field> Lambda.args = 2, arguments */
/* <Field> Lambda.inner_scope = 3, Function */

/* <Field> List.location = 0, location */
/* <Field> List.parenthesised = 1, bool */
/* <Field> List.elts = 2, expr_list */
/* <Field> List.ctx = 3, expr_context */

/* <Field> ListComp.location = 0, location */
/* <Field> ListComp.parenthesised = 1, bool */
/* <Field> ListComp.function = 2, Function */
/* <Field> ListComp.iterable = 3, expr */
/* <Field> ListComp.generators = 4, comprehension_list */
/* <Field> ListComp.elt = 5, expr */

/* <Field> MatchStmt.location = 0, location */
/* <Field> MatchStmt.subject = 1, expr */
/* <Field> MatchStmt.cases = 2, stmt_list */

/* <Field> MatchAsPattern.location = 0, location */
/* <Field> MatchAsPattern.parenthesised = 1, bool */
/* <Field> MatchAsPattern.pattern = 2, pattern */
/* <Field> MatchAsPattern.alias = 3, expr */

/* <Field> MatchCapturePattern.location = 0, location */
/* <Field> MatchCapturePattern.parenthesised = 1, bool */
/* <Field> MatchCapturePattern.variable = 2, expr */

/* <Field> MatchClassPattern.location = 0, location */
/* <Field> MatchClassPattern.parenthesised = 1, bool */
/* <Field> MatchClassPattern.class = 2, expr */
/* <Field> MatchClassPattern.class_name = 3, expr */
/* <Field> MatchClassPattern.positional = 4, pattern_list */
/* <Field> MatchClassPattern.keyword = 5, pattern_list */

/* <Field> MatchDoubleStarPattern.location = 0, location */
/* <Field> MatchDoubleStarPattern.parenthesised = 1, bool */
/* <Field> MatchDoubleStarPattern.target = 2, pattern */

/* <Field> MatchKeyValuePattern.location = 0, location */
/* <Field> MatchKeyValuePattern.parenthesised = 1, bool */
/* <Field> MatchKeyValuePattern.key = 2, pattern */
/* <Field> MatchKeyValuePattern.value = 3, pattern */

/* <Field> MatchKeywordPattern.location = 0, location */
/* <Field> MatchKeywordPattern.parenthesised = 1, bool */
/* <Field> MatchKeywordPattern.attribute = 2, expr */
/* <Field> MatchKeywordPattern.value = 3, pattern */

/* <Field> MatchLiteralPattern.location = 0, location */
/* <Field> MatchLiteralPattern.parenthesised = 1, bool */
/* <Field> MatchLiteralPattern.literal = 2, expr */

/* <Field> MatchMappingPattern.location = 0, location */
/* <Field> MatchMappingPattern.parenthesised = 1, bool */
/* <Field> MatchMappingPattern.mappings = 2, pattern_list */

/* <Field> MatchOrPattern.location = 0, location */
/* <Field> MatchOrPattern.parenthesised = 1, bool */
/* <Field> MatchOrPattern.patterns = 2, pattern_list */

/* <Field> MatchSequencePattern.location = 0, location */
/* <Field> MatchSequencePattern.parenthesised = 1, bool */
/* <Field> MatchSequencePattern.patterns = 2, pattern_list */

/* <Field> MatchStarPattern.location = 0, location */
/* <Field> MatchStarPattern.parenthesised = 1, bool */
/* <Field> MatchStarPattern.target = 2, pattern */

/* <Field> MatchValuePattern.location = 0, location */
/* <Field> MatchValuePattern.parenthesised = 1, bool */
/* <Field> MatchValuePattern.value = 2, expr */

/* <Field> MatchWildcardPattern.location = 0, location */
/* <Field> MatchWildcardPattern.parenthesised = 1, bool */

/* <Field> Module.name = 0, str */
/* <Field> Module.hash = 1, str */
/* <Field> Module.body = 2, stmt_list */
/* <Field> Module.kind = 3, str */

/* <Field> Name.location = 0, location */
/* <Field> Name.parenthesised = 1, bool */
/* <Field> Name.variable = 2, variable */
/* <Field> Name.ctx = 3, expr_context */
/* <Parent> Name = ParameterList  */

/* <Field> Nonlocal.location = 0, location */
/* <Field> Nonlocal.names = 1, str_list */

/* <Field> Num.location = 0, location */
/* <Field> Num.parenthesised = 1, bool */
/* <Field> Num.n = 2, number */
/* <Field> Num.text = 3, number */

/* <Field> ParamSpec.location = 0, location */
/* <Field> ParamSpec.name = 1, expr */
/* <Field> ParamSpec.default = 2, expr */

/* <Field> Pass.location = 0, location */

/* <Field> PlaceHolder.location = 0, location */
/* <Field> PlaceHolder.parenthesised = 1, bool */
/* <Field> PlaceHolder.variable = 2, variable */
/* <Field> PlaceHolder.ctx = 3, expr_context */

/* <Field> Print.location = 0, location */
/* <Field> Print.dest = 1, expr */
/* <Field> Print.values = 2, expr_list */
/* <Field> Print.nl = 3, bool */

/* <Field> Raise.location = 0, location */
/* <Field> Raise.exc = 1, expr */
/* <Field> Raise.cause = 2, expr */
/* <Field> Raise.type = 3, expr */
/* <Field> Raise.inst = 4, expr */
/* <Field> Raise.tback = 5, expr */

/* <Field> Repr.location = 0, location */
/* <Field> Repr.parenthesised = 1, bool */
/* <Field> Repr.value = 2, expr */

/* <Field> Return.location = 0, location */
/* <Field> Return.value = 1, expr */

/* <Field> Set.location = 0, location */
/* <Field> Set.parenthesised = 1, bool */
/* <Field> Set.elts = 2, expr_list */

/* <Field> SetComp.location = 0, location */
/* <Field> SetComp.parenthesised = 1, bool */
/* <Field> SetComp.function = 2, Function */
/* <Field> SetComp.iterable = 3, expr */

/* <Field> Slice.location = 0, location */
/* <Field> Slice.parenthesised = 1, bool */
/* <Field> Slice.start = 2, expr */
/* <Field> Slice.stop = 3, expr */
/* <Field> Slice.step = 4, expr */

/* <Field> SpecialOperation.location = 0, location */
/* <Field> SpecialOperation.parenthesised = 1, bool */
/* <Field> SpecialOperation.name = 2, str */
/* <Field> SpecialOperation.arguments = 3, expr_list */

/* <Field> Starred.location = 0, location */
/* <Field> Starred.parenthesised = 1, bool */
/* <Field> Starred.value = 2, expr */
/* <Field> Starred.ctx = 3, expr_context */

/* <Field> Str.location = 0, location */
/* <Field> Str.parenthesised = 1, bool */
/* <Field> Str.s = 2, str */
/* <Field> Str.prefix = 3, str */
/* <Field> Str.implicitly_concatenated_parts = 4, StringPart_list */

/* <Field> StringPart.text = 0, str */
/* <Field> StringPart.location = 1, location */
/* <Parent> StringPart = StringPartList  */
/* <Parent> StringPartList = BytesOrStr  */

/* <Field> Subscript.location = 0, location */
/* <Field> Subscript.parenthesised = 1, bool */
/* <Field> Subscript.value = 2, expr */
/* <Field> Subscript.index = 3, expr */
/* <Field> Subscript.ctx = 4, expr_context */

/* <Field> TemplateDottedNotation.location = 0, location */
/* <Field> TemplateDottedNotation.parenthesised = 1, bool */
/* <Field> TemplateDottedNotation.value = 2, expr */
/* <Field> TemplateDottedNotation.attr = 3, str */
/* <Field> TemplateDottedNotation.ctx = 4, expr_context */

/* <Field> TemplateWrite.location = 0, location */
/* <Field> TemplateWrite.value = 1, expr */

/* <Field> Try.location = 0, location */
/* <Field> Try.body = 1, stmt_list */
/* <Field> Try.orelse = 2, stmt_list */
/* <Field> Try.handlers = 3, stmt_list */
/* <Field> Try.finalbody = 4, stmt_list */

/* <Field> Tuple.location = 0, location */
/* <Field> Tuple.parenthesised = 1, bool */
/* <Field> Tuple.elts = 2, expr_list */
/* <Field> Tuple.ctx = 3, expr_context */
/* <Parent> Tuple = ParameterList  */

/* <Field> TypeAlias.location = 0, location */
/* <Field> TypeAlias.name = 1, expr */
/* <Field> TypeAlias.type_parameters = 2, type_parameter_list */
/* <Field> TypeAlias.value = 3, expr */

/* <Field> TypeVar.location = 0, location */
/* <Field> TypeVar.name = 1, expr */
/* <Field> TypeVar.bound = 2, expr */
/* <Field> TypeVar.default = 3, expr */

/* <Field> TypeVarTuple.location = 0, location */
/* <Field> TypeVarTuple.name = 1, expr */
/* <Field> TypeVarTuple.default = 2, expr */

/* <Field> UnaryExpr.location = 0, location */
/* <Field> UnaryExpr.parenthesised = 1, bool */
/* <Field> UnaryExpr.op = 2, unaryop */
/* <Field> UnaryExpr.operand = 3, expr */

/* <Field> While.location = 0, location */
/* <Field> While.test = 1, expr */
/* <Field> While.body = 2, stmt_list */
/* <Field> While.orelse = 3, stmt_list */

/* <Field> With.location = 0, location */
/* <Field> With.context_expr = 1, expr */
/* <Field> With.optional_vars = 2, expr */
/* <Field> With.body = 3, stmt_list */
/* <Field> With.is_async = 4, bool */

/* <Field> Yield.location = 0, location */
/* <Field> Yield.parenthesised = 1, bool */
/* <Field> Yield.value = 2, expr */

/* <Field> YieldFrom.location = 0, location */
/* <Field> YieldFrom.parenthesised = 1, bool */
/* <Field> YieldFrom.value = 2, expr */

/* <Field> Alias.value = 0, expr */
/* <Field> Alias.asname = 1, expr */
/* <Parent> Alias = AliasList  */
/* <Parent> AliasList = Import  */

/* <Field> Arguments.kw_defaults = 0, expr_list */
/* <Field> Arguments.defaults = 1, expr_list */
/* <Field> Arguments.annotations = 2, expr_list */
/* <Field> Arguments.varargannotation = 3, expr */
/* <Field> Arguments.kwargannotation = 4, expr */
/* <Field> Arguments.kw_annotations = 5, expr_list */
/* <Parent> Arguments = ArgumentsParent  */
/* <Parent> boolean = BoolParent  */
/* <Parent> Boolop = BoolExpr  */
/* <Parent> string = Bytes  */
/* <Parent> Cmpop = CmpopList  */
/* <Parent> CmpopList = Compare  */

/* <Field> Comprehension.location = 0, location */
/* <Field> Comprehension.iter = 1, expr */
/* <Field> Comprehension.target = 2, expr */
/* <Field> Comprehension.ifs = 3, expr_list */
/* <Parent> Comprehension = ComprehensionList  */
/* <Parent> ComprehensionList = ListComp  */
/* <Parent> DictItem = DictItemList  */
/* <Parent> DictItemList = DictItemListParent  */

/* <Field> Expr.location = 0, location */
/* <Field> Expr.parenthesised = 1, bool */
/* <Parent> Expr = ExprParent  */
/* <Parent> ExprContext = ExprContextParent  */
/* <Parent> ExprList = ExprListParent  */
/* <Parent> int = ImportExpr  */

/* <Field> Keyword.location = 0, location */
/* <Field> Keyword.value = 1, expr */
/* <Field> Keyword.arg = 2, str */
/* <Parent> Location = LocationParent  */
/* <Parent> string = Num  */
/* <Parent> Operator = BinaryExpr  */
/* <Parent> ParameterList = Function  */

/* <Field> Pattern.location = 0, location */
/* <Field> Pattern.parenthesised = 1, bool */
/* <Parent> Pattern = PatternParent  */
/* <Parent> PatternList = PatternListParent  */

/* <Field> Stmt.location = 0, location */
/* <Parent> Stmt = StmtList  */
/* <Parent> StmtList = StmtListParent  */
/* <Parent> string = StrParent  */
/* <Parent> StringList = StrListParent  */

/* <Field> TypeParameter.location = 0, location */
/* <Parent> TypeParameter = TypeParameterList  */
/* <Parent> TypeParameterList = TypeParameterListParent  */
/* <Parent> Unaryop = UnaryExpr  */
/* <Parent> Variable = VariableParent  */
py_Classes(unique int id : @py_Class,
    unique int parent : @py_ClassExpr ref);

py_Functions(unique int id : @py_Function,
    unique int parent : @py_Function_parent ref);

py_Modules(unique int id : @py_Module);

py_StringParts(unique int id : @py_StringPart,
    int parent : @py_StringPart_list ref,
    int idx : int ref);

py_StringPart_lists(unique int id : @py_StringPart_list,
    unique int parent : @py_Bytes_or_Str ref);

py_aliases(unique int id : @py_alias,
    int parent : @py_alias_list ref,
    int idx : int ref);

py_alias_lists(unique int id : @py_alias_list,
    unique int parent : @py_Import ref);

py_arguments(unique int id : @py_arguments,
    unique int parent : @py_arguments_parent ref);

py_bools(int parent : @py_bool_parent ref,
    int idx : int ref);

py_boolops(unique int id : @py_boolop,
    int kind: int ref,
    unique int parent : @py_BoolExpr ref);

py_bytes(varchar(1) id : string ref,
    int parent : @py_Bytes ref,
    int idx : int ref);

py_cmpops(unique int id : @py_cmpop,
    int kind: int ref,
    int parent : @py_cmpop_list ref,
    int idx : int ref);

py_cmpop_lists(unique int id : @py_cmpop_list,
    unique int parent : @py_Compare ref);

py_comprehensions(unique int id : @py_comprehension,
    int parent : @py_comprehension_list ref,
    int idx : int ref);

py_comprehension_lists(unique int id : @py_comprehension_list,
    unique int parent : @py_ListComp ref);

py_dict_items(unique int id : @py_dict_item,
    int kind: int ref,
    int parent : @py_dict_item_list ref,
    int idx : int ref);

py_dict_item_lists(unique int id : @py_dict_item_list,
    unique int parent : @py_dict_item_list_parent ref);

py_exprs(unique int id : @py_expr,
    int kind: int ref,
    int parent : @py_expr_parent ref,
    int idx : int ref);

py_expr_contexts(unique int id : @py_expr_context,
    int kind: int ref,
    unique int parent : @py_expr_context_parent ref);

py_expr_lists(unique int id : @py_expr_list,
    int parent : @py_expr_list_parent ref,
    int idx : int ref);

py_ints(int id : int ref,
    unique int parent : @py_ImportExpr ref);

py_locations(unique int id : @location ref,
    unique int parent : @py_location_parent ref);

py_numbers(varchar(1) id : string ref,
    int parent : @py_Num ref,
    int idx : int ref);

py_operators(unique int id : @py_operator,
    int kind: int ref,
    unique int parent : @py_BinaryExpr ref);

py_parameter_lists(unique int id : @py_parameter_list,
    unique int parent : @py_Function ref);

py_patterns(unique int id : @py_pattern,
    int kind: int ref,
    int parent : @py_pattern_parent ref,
    int idx : int ref);

py_pattern_lists(unique int id : @py_pattern_list,
    int parent : @py_pattern_list_parent ref,
    int idx : int ref);

py_stmts(unique int id : @py_stmt,
    int kind: int ref,
    int parent : @py_stmt_list ref,
    int idx : int ref);

py_stmt_lists(unique int id : @py_stmt_list,
    int parent : @py_stmt_list_parent ref,
    int idx : int ref);

py_strs(varchar(1) id : string ref,
    int parent : @py_str_parent ref,
    int idx : int ref);

py_str_lists(unique int id : @py_str_list,
    unique int parent : @py_str_list_parent ref);

py_type_parameters(unique int id : @py_type_parameter,
    int kind: int ref,
    int parent : @py_type_parameter_list ref,
    int idx : int ref);

py_type_parameter_lists(unique int id : @py_type_parameter_list,
    unique int parent : @py_type_parameter_list_parent ref);

py_unaryops(unique int id : @py_unaryop,
    int kind: int ref,
    unique int parent : @py_UnaryExpr ref);

py_variables(int id : @py_variable ref,
    unique int parent : @py_variable_parent ref);

case @py_boolop.kind of
    0 = @py_And
|   1 = @py_Or;

case @py_cmpop.kind of
    0 = @py_Eq
|   1 = @py_Gt
|   2 = @py_GtE
|   3 = @py_In
|   4 = @py_Is
|   5 = @py_IsNot
|   6 = @py_Lt
|   7 = @py_LtE
|   8 = @py_NotEq
|   9 = @py_NotIn;

case @py_dict_item.kind of
    0 = @py_DictUnpacking
|   1 = @py_KeyValuePair
|   2 = @py_keyword;

case @py_expr.kind of
    0 = @py_Attribute
|   1 = @py_BinaryExpr
|   2 = @py_BoolExpr
|   3 = @py_Bytes
|   4 = @py_Call
|   5 = @py_ClassExpr
|   6 = @py_Compare
|   7 = @py_Dict
|   8 = @py_DictComp
|   9 = @py_Ellipsis
|   10 = @py_FunctionExpr
|   11 = @py_GeneratorExp
|   12 = @py_IfExp
|   13 = @py_ImportExpr
|   14 = @py_ImportMember
|   15 = @py_Lambda
|   16 = @py_List
|   17 = @py_ListComp
|   18 = @py_Guard
|   19 = @py_Name
|   20 = @py_Num
|   21 = @py_Repr
|   22 = @py_Set
|   23 = @py_SetComp
|   24 = @py_Slice
|   25 = @py_Starred
|   26 = @py_Str
|   27 = @py_Subscript
|   28 = @py_Tuple
|   29 = @py_UnaryExpr
|   30 = @py_Yield
|   31 = @py_YieldFrom
|   32 = @py_TemplateDottedNotation
|   33 = @py_Filter
|   34 = @py_PlaceHolder
|   35 = @py_Await
|   36 = @py_Fstring
|   37 = @py_FormattedValue
|   38 = @py_AssignExpr
|   39 = @py_SpecialOperation;

case @py_expr_context.kind of
    0 = @py_AugLoad
|   1 = @py_AugStore
|   2 = @py_Del
|   3 = @py_Load
|   4 = @py_Param
|   5 = @py_Store;

case @py_operator.kind of
    0 = @py_Add
|   1 = @py_BitAnd
|   2 = @py_BitOr
|   3 = @py_BitXor
|   4 = @py_Div
|   5 = @py_FloorDiv
|   6 = @py_LShift
|   7 = @py_Mod
|   8 = @py_Mult
|   9 = @py_Pow
|   10 = @py_RShift
|   11 = @py_Sub
|   12 = @py_MatMult;

case @py_pattern.kind of
    0 = @py_MatchAsPattern
|   1 = @py_MatchOrPattern
|   2 = @py_MatchLiteralPattern
|   3 = @py_MatchCapturePattern
|   4 = @py_MatchWildcardPattern
|   5 = @py_MatchValuePattern
|   6 = @py_MatchSequencePattern
|   7 = @py_MatchStarPattern
|   8 = @py_MatchMappingPattern
|   9 = @py_MatchDoubleStarPattern
|   10 = @py_MatchKeyValuePattern
|   11 = @py_MatchClassPattern
|   12 = @py_MatchKeywordPattern;

case @py_stmt.kind of
    0 = @py_Assert
|   1 = @py_Assign
|   2 = @py_AugAssign
|   3 = @py_Break
|   4 = @py_Continue
|   5 = @py_Delete
|   6 = @py_ExceptStmt
|   7 = @py_ExceptGroupStmt
|   8 = @py_Exec
|   9 = @py_Expr_stmt
|   10 = @py_For
|   11 = @py_Global
|   12 = @py_If
|   13 = @py_Import
|   14 = @py_ImportStar
|   15 = @py_MatchStmt
|   16 = @py_Case
|   17 = @py_Nonlocal
|   18 = @py_Pass
|   19 = @py_Print
|   20 = @py_Raise
|   21 = @py_Return
|   22 = @py_Try
|   23 = @py_While
|   24 = @py_With
|   25 = @py_TemplateWrite
|   26 = @py_AnnAssign
|   27 = @py_TypeAlias;

case @py_type_parameter.kind of
    0 = @py_ParamSpec
|   1 = @py_TypeVar
|   2 = @py_TypeVarTuple;

case @py_unaryop.kind of
    0 = @py_Invert
|   1 = @py_Not
|   2 = @py_UAdd
|   3 = @py_USub;

@py_Bytes_or_Str = @py_Bytes | @py_Str;

@py_Function_parent = @py_DictComp | @py_FunctionExpr | @py_GeneratorExp | @py_Lambda | @py_ListComp | @py_SetComp;

@py_arguments_parent = @py_FunctionExpr | @py_Lambda;

@py_ast_node = @py_Class | @py_Function | @py_Module | @py_StringPart | @py_comprehension | @py_dict_item | @py_expr | @py_pattern | @py_stmt | @py_type_parameter;

@py_bool_parent = @py_For | @py_Function | @py_Print | @py_With | @py_expr | @py_pattern;

@py_dict_item_list_parent = @py_Call | @py_ClassExpr | @py_Dict;

@py_expr_context_parent = @py_Attribute | @py_List | @py_Name | @py_PlaceHolder | @py_Starred | @py_Subscript | @py_TemplateDottedNotation | @py_Tuple;

@py_expr_list_parent = @py_Assign | @py_BoolExpr | @py_Call | @py_ClassExpr | @py_Compare | @py_Delete | @py_Fstring | @py_Function | @py_List | @py_Print | @py_Set | @py_SpecialOperation | @py_Tuple | @py_arguments | @py_comprehension;

@py_expr_or_stmt = @py_expr | @py_stmt;

@py_expr_parent = @py_AnnAssign | @py_Assert | @py_Assign | @py_AssignExpr | @py_Attribute | @py_AugAssign | @py_Await | @py_BinaryExpr | @py_Call | @py_Case | @py_Compare | @py_DictComp | @py_DictUnpacking | @py_ExceptGroupStmt | @py_ExceptStmt | @py_Exec | @py_Expr_stmt | @py_Filter | @py_For | @py_FormattedValue | @py_Function | @py_FunctionExpr | @py_GeneratorExp | @py_Guard | @py_If | @py_IfExp | @py_ImportMember | @py_ImportStar | @py_KeyValuePair | @py_ListComp | @py_MatchAsPattern | @py_MatchCapturePattern | @py_MatchClassPattern | @py_MatchKeywordPattern | @py_MatchLiteralPattern | @py_MatchStmt | @py_MatchValuePattern | @py_ParamSpec | @py_Print | @py_Raise | @py_Repr | @py_Return | @py_SetComp | @py_Slice | @py_Starred | @py_Subscript | @py_TemplateDottedNotation | @py_TemplateWrite | @py_TypeAlias | @py_TypeVar | @py_TypeVarTuple | @py_UnaryExpr | @py_While | @py_With | @py_Yield | @py_YieldFrom | @py_alias | @py_arguments | @py_comprehension | @py_expr_list | @py_keyword | @py_parameter_list;

@py_location_parent = @py_DictUnpacking | @py_KeyValuePair | @py_StringPart | @py_comprehension | @py_expr | @py_keyword | @py_pattern | @py_stmt | @py_type_parameter;

@py_parameter = @py_Name | @py_Tuple;

@py_pattern_list_parent = @py_MatchClassPattern | @py_MatchMappingPattern | @py_MatchOrPattern | @py_MatchSequencePattern;

@py_pattern_parent = @py_Case | @py_MatchAsPattern | @py_MatchDoubleStarPattern | @py_MatchKeyValuePattern | @py_MatchKeywordPattern | @py_MatchStarPattern | @py_pattern_list;

@py_scope = @py_Class | @py_Function | @py_Module;

@py_stmt_list_parent = @py_Case | @py_Class | @py_ExceptGroupStmt | @py_ExceptStmt | @py_For | @py_Function | @py_If | @py_MatchStmt | @py_Module | @py_Try | @py_While | @py_With;

@py_str_list_parent = @py_Global | @py_Nonlocal;

@py_str_parent = @py_Attribute | @py_Class | @py_ClassExpr | @py_FormattedValue | @py_Function | @py_FunctionExpr | @py_ImportExpr | @py_ImportMember | @py_Module | @py_SpecialOperation | @py_Str | @py_StringPart | @py_TemplateDottedNotation | @py_keyword | @py_str_list;

@py_type_parameter_list_parent = @py_ClassExpr | @py_Function | @py_TypeAlias;

@py_variable_parent = @py_Name | @py_PlaceHolder;


/*
 * End of auto-generated part
 */



/* Map relative names to absolute names for imports */
py_absolute_names(int module : @py_Module ref,
                  varchar(1) relname : string ref,
                  varchar(1) absname : string ref);

py_exports(int id : @py_Module ref,
           varchar(1) name : string ref);

/* Successor information */
py_successors(int predecessor : @py_flow_node ref,
              int successor : @py_flow_node ref);

py_true_successors(int predecessor : @py_flow_node ref,
              int successor : @py_flow_node ref);

py_exception_successors(int predecessor : @py_flow_node ref,
              int successor : @py_flow_node ref);

py_false_successors(int predecessor : @py_flow_node ref,
              int successor : @py_flow_node ref);

py_flow_bb_node(unique int flownode : @py_flow_node,
             int realnode : @py_ast_node ref,
             int basicblock : @py_flow_node ref,
             int index : int ref);

py_scope_flow(int flow : @py_flow_node ref,
              int scope : @py_scope ref,
              int kind : int ref);

py_idoms(unique int node : @py_flow_node ref,
         int immediate_dominator : @py_flow_node ref);

py_ssa_phi(int phi : @py_ssa_var ref,
           int arg: @py_ssa_var ref);

py_ssa_var(unique int id : @py_ssa_var,
            int var : @py_variable ref);

py_ssa_use(int node: @py_flow_node ref,
           int var : @py_ssa_var ref);

py_ssa_defn(unique int id : @py_ssa_var ref,
                  int node: @py_flow_node ref);

@py_base_var = @py_variable | @py_ssa_var;

py_scopes(unique int node : @py_expr_or_stmt ref,
         int scope : @py_scope ref);

py_scope_location(unique int id : @location ref,
                   unique int scope : @py_scope ref);

py_flags_versioned(varchar(1) name : string ref,
                   varchar(1) value : string ref,
                   varchar(1) version : string ref);

py_syntax_error_versioned(unique int id : @location ref,
                varchar(1) message : string ref,
                varchar(1) version : string ref);

py_comments(unique int id : @py_comment,
           varchar(1) text : string ref,
           unique int location : @location ref);

/* Type information support */

py_cobjects(unique int obj : @py_cobject);

py_cobjecttypes(unique int obj : @py_cobject ref,
                int typeof : @py_cobject ref);

py_cobjectnames(unique int obj : @py_cobject ref,
                varchar(1) name : string ref);

/* Kind should be 0 for introspection, > 0 from source, as follows:
   1 from C extension source
 */
py_cobject_sources(int obj : @py_cobject ref,
                  int kind : int ref);

py_cmembers_versioned(int object : @py_cobject ref,
                      varchar(1) name : string ref,
                      int member : @py_cobject ref,
                      varchar(1) version : string ref);

py_citems(int object : @py_cobject ref,
            int index : int ref,
            int member : @py_cobject ref);

ext_argtype(int funcid : @py_object ref,
            int arg : int ref,
            int typeid : @py_object ref);

ext_rettype(int funcid : @py_object ref,
            int typeid : @py_object ref);

ext_proptype(int propid : @py_object ref,
             int typeid : @py_object ref);

ext_argreturn(int funcid : @py_object ref,
              int arg : int ref);

py_special_objects(unique int obj : @py_cobject ref,
                   unique varchar(1) name : string ref);

py_decorated_object(int object : @py_object ref,
                    int level: int ref);

@py_object = @py_cobject | @py_flow_node;

@py_source_element = @py_ast_node | @container;
