/**
 * @name Hardcoded credentials in Node.js
 * @description Detects hardcoded passwords, API keys, and secrets in JavaScript/Node.js code
 * @kind problem
 * @problem.severity error
 * @id js/hardcoded-credentials
 */

import javascript

from Variable var, StringLiteral value
where
  var.getAnAssignedValue() = value and
  (
    var.getName().toLowerCase().matches("%password%") or
    var.getName().toLowerCase().matches("%secret%") or
    var.getName().toLowerCase().matches("%key%") or
    var.getName().toLowerCase().matches("%token%") or
    var.getName().toLowerCase().matches("%api%")
  ) and
  value.getValue().length() > 5 and
  // Exclude obvious non-credentials
  not value.getValue().toLowerCase().matches("%enter%") and
  not value.getValue().toLowerCase().matches("%prompt%") and
  not value.getValue().toLowerCase().matches("%example%") and
  not value.getValue() = ""
select var, "Hardcoded credential found in variable '" + var.getName() + "': " + value.getValue()
