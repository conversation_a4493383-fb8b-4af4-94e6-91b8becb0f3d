/**
 * @name Hardcoded credentials in Node.js
 * @description Detects hardcoded passwords, API keys, and secrets in JavaScript/Node.js code
 * @kind problem
 * @problem.severity error
 * @id js/hardcoded-credentials
 */

import javascript

from AssignExpr assign, StringLiteral value, string varName
where
  assign.getLhs() instanceof VarRef and
  assign.getLhs().(VarRef).getName() = varName and
  assign.getRhs() = value and
  (
    varName.toLowerCase().matches("%password%") or
    varName.toLowerCase().matches("%secret%") or
    varName.toLowerCase().matches("%key%") or
    varName.toLowerCase().matches("%token%") or
    varName.toLowerCase().matches("%api%")
  ) and
  value.getValue().length() > 5 and
  not value.getValue().toLowerCase().matches("%enter%") and
  not value.getValue().toLowerCase().matches("%prompt%") and
  not value.getValue().toLowerCase().matches("%example%") and
  not value.getValue() = ""
select assign, "Hardcoded credential found in variable '" + varName + "': " + value.getValue()