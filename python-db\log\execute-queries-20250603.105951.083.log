[2025-06-03 10:59:51] This is codeql execute queries -J-Xmx1374M --verbosity=progress --logdir=E:\advance_javascript\codeQL\7\python-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\7\python-db\results -- E:\advance_javascript\codeQL\7\python-db\db-python queries/python-problm-1.ql
[2025-06-03 10:59:51] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --format=json -- queries/python-problm-1.ql
[2025-06-03 10:59:51] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 10:59:51] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\queries\\python-problm-1.ql"
                      ]
[2025-06-03 10:59:51] Refusing fancy output: The terminal is not an xterm: 
[2025-06-03 10:59:51] Creating executor with 1 threads.
[2025-06-03 10:59:52] Calling plumbing command: codeql resolve extensions --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations queries/python-problm-1.ql
[2025-06-03 10:59:52] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --allow-library-packs --format startingpacks -- queries/python-problm-1.ql
[2025-06-03 10:59:52] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 10:59:52] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7"
                      ]
[2025-06-03 10:59:52] Calling plumbing command: codeql resolve extensions-by-pack --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\7
[2025-06-03 10:59:52] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}
[2025-06-03 10:59:52] [SPAMMY] resolve extensions-by-pack> [DERIVATION] python-security-queries: 1.0.0 {python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 1] python-security-queries: 1.0.0
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/python-all: * [*] {python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/python-all: 4.0.7
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 10:59:57] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 10:59:57] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\ext\default-threat-models-fixup.model.yml.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 10:59:57] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Asyncpg.model.yml.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 5 tuples.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 6 tuples.
[2025-06-03 10:59:57] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Stdlib.model.yml.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 12 tuples.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 1 tuples.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 66 tuples.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 10:59:57] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\empty.model.yml.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 0 tuples.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 0 tuples.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 0 tuples.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 10:59:57] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 10:59:58] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\subclass-capture\ALL.model.yml.
[2025-06-03 10:59:58] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 58275 tuples.
[2025-06-03 10:59:58] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-03 10:59:58] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 10:59:58] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-03 10:59:58] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-03 10:59:58] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-03 10:59:58] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-03 10:59:58] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-03 10:59:58] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=7+1+2+1+2",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=20+4+1*2+2+1",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4+2+1+2+1*2+4+2",
                                "columnNumbers" : "A=9*12"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=29",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=37+1+2+4+2*2+4+2*3+1+2+1+2+1+2+4+2+4+2*2+3+2*2+3+1+2*4+4+1+4+1+4+1*5+2*4+4+1+2*11+3+2+3+4+1+2*2+1+2",
                                "columnNumbers" : "A=9*66"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+3*58274",
                                "columnNumbers" : "A=5*58275"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 10:59:58] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=7+1+2+1+2",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=20+4+1*2+2+1",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4+2+1+2+1*2+4+2",
                                "columnNumbers" : "A=9*12"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=29",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=37+1+2+4+2*2+4+2*3+1+2+1+2+1+2+4+2+4+2*2+3+2*2+3+1+2*4+4+1+4+1+4+1*5+2*4+4+1+2*11+3+2+3+4+1+2*2+1+2",
                                "columnNumbers" : "A=9*66"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+3*58274",
                                "columnNumbers" : "A=5*58275"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 10:59:59] Calling plumbing command: codeql resolve library-path --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --query=E:\advance_javascript\codeQL\7\queries\python-problm-1.ql --format=json
[2025-06-03 10:59:59] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:59:59] [DETAILS] resolve library-path> Found enclosing pack 'python-security-queries' at E:\advance_javascript\codeQL\7.
[2025-06-03 10:59:59] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-03 10:59:59] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\7\qlpack.yml.
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] python-security-queries: 1.0.0 {python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 1] python-security-queries: 1.0.0
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] codeql/python-all: * [*] {python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 2] codeql/python-all: 4.0.7
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] codeql/yaml: * [*] {codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] codeql/xml: * [*] {codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] codeql/util: * [*] {codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] codeql/tutorial: * [*] {codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] codeql/threat-models: * [*] {codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] codeql/regex: * [*] {codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] codeql/mad: * [*] {codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] codeql/dataflow: * [*] {codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] codeql/typetracking: * [*] {codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DERIVATION] codeql/ssa: * [*] {codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 10:59:59] [SPAMMY] resolve library-path> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 10:59:59] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\7 resolved OK.
[2025-06-03 10:59:59] [DETAILS] resolve library-path> Found dbscheme through QL packs: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmlecode.python.dbscheme.
[2025-06-03 10:59:59] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\dataflow\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\mad\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\regex\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\ssa\\1.1.2",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\tutorial\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\typetracking\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\xml\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\yaml\\1.0.23"
                        ],
                        "dbscheme" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmlecode.python.dbscheme",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "python-security-queries\\queries\\python-problm-1.ql",
                        "qlPackName" : "python-security-queries"
                      }
[2025-06-03 10:59:59] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:59:59] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 11:00:00] Resolved file set for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql hashes to 2c2380da56dfc3a5980c42863c65105c.
[2025-06-03 11:00:00] [DETAILS] execute queries> Checking QL for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 11:00:00] Stale frontend caches are invalidated based on import graph reachability.
[2025-06-03 11:00:00] ExternalModuleBindingPass ...
[2025-06-03 11:00:01] ExternalModuleBindingPass time: 00:01.125
[2025-06-03 11:00:01] CollectInstantiationsPass ...
[2025-06-03 11:00:02] CollectInstantiationsPass time: 00:00.415
[2025-06-03 11:00:02] Ql checks ...
[2025-06-03 11:00:06] Ql checks time: 00:04.556
[2025-06-03 11:00:12] Compilation pipeline
[2025-06-03 11:00:12] Type Inference ...
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: Best number of iterations,Maximum number of runs,Number of BDD variables,Size of BDD for typefacts
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,96,97
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,326,384
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,24,24
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,48,72
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,5,5
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:12] CSV_TYPE_HIERARCHY: 0,0,7,7
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,561,15752
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,12,13
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,8,10
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,7,7
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,5,5
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,10,11
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,10,10
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,13,17
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,8,10
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,52,62
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,18,18
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,41,52
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:00:13] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:00:14] Type Inference time: 00:01.415
[2025-06-03 11:00:14] [DETAILS] execute queries> Optimizing E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 11:00:15] Processing compilation pipeline
[2025-06-03 11:00:15] Started compiling a query
[2025-06-03 11:00:17] Initialising compiler...
[2025-06-03 11:00:17] 	 ... compiler initialised
[2025-06-03 11:00:17] About to start query optimisation
[2025-06-03 11:00:17] Compilation cache miss for 61f5533df7962825b7a6a7e719f87446.
[2025-06-03 11:00:17] Stored compiled program for 61f5533df7962825b7a6a7e719f87446.
[2025-06-03 11:00:17] Compilation cache miss for b5269b09d2ddbfd9b83216ea225fd00d.
[2025-06-03 11:00:23] Stored compiled program for b5269b09d2ddbfd9b83216ea225fd00d.
[2025-06-03 11:00:23] Compilation cache miss for e28b8c173b655e5cdbf8d0c1599d03a8.
[2025-06-03 11:00:23] Stored compiled program for e28b8c173b655e5cdbf8d0c1599d03a8.
[2025-06-03 11:00:23] CSV_COMPILATION: NONE,MISC,RA_TRANSLATION,OPTIMISATIONS
[2025-06-03 11:00:23] CSV_COMPILATION: 365,188,898,5207
[2025-06-03 11:00:23] [SPAMMY] execute queries> No database upgrade/downgrade needed for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql
[2025-06-03 11:00:23] [PROGRESS] execute queries> [1/1 comp 24.8s] Compiled E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 11:00:24] [PROGRESS] execute queries> Starting evaluation of python-security-queries\queries\python-problm-1.ql.
[2025-06-03 11:00:24] Starting evaluation of E:\advance_javascript\codeQL\7\queries\python-problm-1.ql
[2025-06-03 11:00:24] (0s) Start query execution
[2025-06-03 11:00:24] (0s) Beginning execution of E:\advance_javascript\codeQL\7\queries\python-problm-1.ql
[2025-06-03 11:00:24] (0s)  >>> Created relation py_exprs/4@62fb455g with 12 rows and digest 3728e8rh5kt7cllqvjddt17jun9.
[2025-06-03 11:00:24] (0s)  >>> Created relation folders/2@bad7d9u0 with 5 rows and digest a8c3bc7f95kbfikktpl4solo9o0.
[2025-06-03 11:00:24] (0s)  >>> Created relation files/2@ec93749g with 2 rows and digest d254f2721lhu5ro80oljq1066i6.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::Input::ContainerBase.getAbsolutePath/0#dispred#bb5aca3b/2@966d67vo
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::Input::ContainerBase.getAbsolutePath/0#dispred#bb5aca3b/2@966d67vo with 7 rows and digest 99096cjm4ebl3m3r36aoa7prtf1.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_module_path/2@7a0f7599 with 1 rows and digest 58d110scfk169u0vappqlnmo2c3.
[2025-06-03 11:00:24] (0s)  >>> Created relation locations_ast/6@aba204da with 20 rows and digest 5ec561pq3ku4r05q2o857m9nalb.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_scope_location/2@9810bb7v with 2 rows and digest 4a830ek0j27kdbjghqphv68vkh2.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_scope_location_1#antijoin_rhs/1@8ca5b9j1
[2025-06-03 11:00:24] (0s)  >>> Created relation py_scope_location_1#antijoin_rhs/1@8ca5b9j1 with 2 rows and digest 5a3e3bu55kd2p0hdc2avgtn4cd0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate locations_ast_234501#join_rhs/6@6e5a5d36
[2025-06-03 11:00:24] (0s)  >>> Created relation locations_ast_234501#join_rhs/6@6e5a5d36 with 20 rows and digest b637denlb1r90fu2copfep92ic9.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_type_parameters/4@a1fcadgi with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that py_type_parameters_10#join_rhs/2@dc8dd6s9 is empty, due to py_type_parameters/4@a1fcadgi.
[2025-06-03 11:00:24] (0s) Inferred that py_type_parameters_20#join_rhs/2@e9503ao7 is empty, due to py_type_parameters/4@a1fcadgi.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_comprehensions/3@5a5a332m with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstExtended::Comprehension.getASubExpression/0#dispred#63af4970/2@8f912bbl is empty, due to py_comprehensions/3@5a5a332m.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::ListComp_.getAGenerator/0#dispred#0726485f/2@776433m6 is empty, due to py_comprehensions/3@5a5a332m.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_StringParts/3@40e65fbh with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that py_StringParts_10#join_rhs/2@09edc2s7 is empty, due to py_StringParts/3@40e65fbh.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Str_.getAnImplicitlyConcatenatedPart/0#dispred#08057c4a/2@b548ab4a is empty, due to py_StringParts_10#join_rhs/2@09edc2s7.
[2025-06-03 11:00:24] (0s) Inferred that project#AstGenerated::Str_.getAnImplicitlyConcatenatedPart/0#dispred#08057c4a/1@fbc996q5 is empty, due to AstGenerated::Str_.getAnImplicitlyConcatenatedPart/0#dispred#08057c4a/2@b548ab4a.
[2025-06-03 11:00:24] (0s) Inferred that Exprs::StringLiteral.isUnicode/0#dispred#64a81fc8#b/1@7bc781ti is empty, due to project#AstGenerated::Str_.getAnImplicitlyConcatenatedPart/0#dispred#08057c4a/1@fbc996q5.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_patterns/4@5b268b2a with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that py_patterns_20#join_rhs/2@063912q6 is empty, due to py_patterns/4@5b268b2a.
[2025-06-03 11:00:24] (0s) Inferred that py_patterns_10#join_rhs/2@626962h6 is empty, due to py_patterns/4@5b268b2a.
[2025-06-03 11:00:24] (0s) Inferred that py_patterns_230#join_rhs/3@54219et2 is empty, due to py_patterns/4@5b268b2a.
[2025-06-03 11:00:24] (0s) Inferred that #AstExtended::AstNode.getAChildNode/0#dispred#a130356dPlus#fb#flipped/2@f3d5f3hl is empty, due to py_patterns/4@5b268b2a.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::PatternList_.getAnItem/0#dispred#3c725bc9/2@4e0113eh is empty, due to py_patterns_20#join_rhs/2@063912q6.
[2025-06-03 11:00:24] (0s) Inferred that Patterns::MatchAsPattern.getAlias/0#dispred#766c9d3f/2@e01a224v is empty, due to py_patterns_10#join_rhs/2@626962h6.
[2025-06-03 11:00:24] (0s) Inferred that Patterns::MatchCapturePattern.getVariable/0#dispred#1afb04d9/2@fac7a9lg is empty, due to py_patterns_10#join_rhs/2@626962h6.
[2025-06-03 11:00:24] (0s) Inferred that #AstExtended::AstNode.getAChildNode/0#dispred#a130356dPlus#fb#flipped_10#join_rhs/2@bf08f8qt is empty, due to #AstExtended::AstNode.getAChildNode/0#dispred#a130356dPlus#fb#flipped/2@f3d5f3hl.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::pattern_alias_definition/2#e55586af/2@f8ca01uf is empty, due to Patterns::MatchAsPattern.getAlias/0#dispred#766c9d3f/2@e01a224v.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::pattern_capture_definition/2#77b3ae42/2@330db0d6 is empty, due to Patterns::MatchCapturePattern.getVariable/0#dispred#1afb04d9/2@fac7a9lg.
[2025-06-03 11:00:24] (0s) Inferred that Patterns::Pattern.getCase/0#72a79c75/2@c7fb3fgs is empty, due to #AstExtended::AstNode.getAChildNode/0#dispred#a130356dPlus#fb#flipped_10#join_rhs/2@bf08f8qt.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::pattern_alias_definition/2#e55586af/2@f91af3ha is empty, due to SsaDefinitions::SsaSource::pattern_alias_definition/2#e55586af/2@f8ca01uf.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::pattern_capture_definition/2#77b3ae42/2@6e282c28 is empty, due to SsaDefinitions::SsaSource::pattern_capture_definition/2#77b3ae42/2@330db0d6.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_locations/2@d222f4l2 with 16 rows and digest 384d83nh0cs79cqmgjeksmecgu8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_locations_10#join_rhs/2@cad24bnc
[2025-06-03 11:00:24] (0s)  >>> Created relation py_locations_10#join_rhs/2@cad24bnc with 16 rows and digest 11aec84nka31mcm7j3fjcf5nu3e.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_dict_items/4@f807ab0r with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that py_dict_items_20#join_rhs/2@8d60b0p4 is empty, due to py_dict_items/4@f807ab0r.
[2025-06-03 11:00:24] (0s) Inferred that py_dict_items_10#join_rhs/2@1abaaa6d is empty, due to py_dict_items/4@f807ab0r.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Dict_.getAnItem/0#dispred#c0c5c5fb/2@2e9526ji is empty, due to py_dict_items_20#join_rhs/2@8d60b0p4.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::KeyValuePair_.getKey/0#dispred#f9941800/2@79ba2del is empty, due to py_dict_items_10#join_rhs/2@1abaaa6d.
[2025-06-03 11:00:24] (0s) Inferred that Keywords::DictUnpacking.getValue/0#dispred#f8e7301b/2@11db1cso is empty, due to py_dict_items_10#join_rhs/2@1abaaa6d.
[2025-06-03 11:00:24] (0s) Inferred that Keywords::DictUnpackingOrKeyword#749f6a53/1@d3d2841c is empty, due to py_dict_items_10#join_rhs/2@1abaaa6d.
[2025-06-03 11:00:24] (0s) Inferred that Keywords::KeyValuePair.getValue/0#dispred#93775546/2@adc4906v is empty, due to py_dict_items_10#join_rhs/2@1abaaa6d.
[2025-06-03 11:00:24] (0s) Inferred that Keywords::DictDisplayItem.getValue/0#dispred#307ba8c4/2@aaef9f57 is empty, due to py_dict_items_10#join_rhs/2@1abaaa6d.
[2025-06-03 11:00:24] (0s) Inferred that Keywords::Keyword.getValue/0#dispred#bb5e46e3/2@9779f54e is empty, due to py_dict_items_10#join_rhs/2@1abaaa6d.
[2025-06-03 11:00:24] (0s) Inferred that Keywords::DictUnpackingOrKeyword.getValue/0#dispred#f5fe382f/2@7c4cfaeh is empty, due to py_dict_items_10#join_rhs/2@1abaaa6d.
[2025-06-03 11:00:24] (0s) Inferred that Class::ClassExpr.getAKeyword/0#dispred#a84ede11/2@f990e6sg is empty, due to Keywords::DictUnpackingOrKeyword#749f6a53/1@d3d2841c.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb/3@ba700brb is empty, due to Keywords::DictUnpackingOrKeyword#749f6a53/1@d3d2841c.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs/3@06c0e34c is empty, due to AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb/3@ba700brb.
[2025-06-03 11:00:24] (0s) Inferred that _AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs_py_dict_items_10#join_rhs#min_range/2@9982b6ju is empty, due to AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs/3@06c0e34c.
[2025-06-03 11:00:24] (0s) Inferred that _AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs_py_dict_items_10#join_rhs#min_term/3@ccd7f7s1 is empty, due to AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs/3@06c0e34c.
[2025-06-03 11:00:24] (0s) Inferred that Exprs::Call.getAKeyword/0#dispred#5e56df84/2@89a240tb is empty, due to AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs/3@06c0e34c.
[2025-06-03 11:00:24] (0s) Inferred that Exprs::Call.getMinimumUnpackingIndex/0#dispred#8e2a7e5f/2@d254eaa4 is empty, due to _AstGenerated::Call_.getNamedArg/1#dispred#ebc6dc03#ffb_201#join_rhs_py_dict_items_10#join_rhs#min_range/2@9982b6ju.
[2025-06-03 11:00:24] (0s) Inferred that Exprs::Call.getMinimumUnpackingIndex/0#dispred#8e2a7e5f_0#antijoin_rhs/1@997f86hg is empty, due to Exprs::Call.getMinimumUnpackingIndex/0#dispred#8e2a7e5f/2@d254eaa4.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_Functions/2@dc6e589f with 1 rows and digest 89918ec8li16om8l8c32lq15tpd.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_Classes/2@2777c63f with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Class::Class.toString/0#dispred#19bc00cc/2@ef38470h is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:00:24] (0s) Inferred that py_Classes_10#join_rhs/2@132b13h6 is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:00:24] (0s) Inferred that ImportTime::ImportTimeScope.entryEdge/2#dispred#d2e3d051/3@14e4aflr is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::class_with_global_metaclass/2#f9fedc22/2@2f3991ea is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::ClassLocalVariable#75f27010/1@f553a8ab is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:00:24] (0s) Inferred that ImportTime::class_var_scope/3#59eab847/3@8ff9bdsa is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:00:24] (0s) Inferred that Variables::Variable.isSelf/0#dispred#7f6832b2/1@05d48c1b is empty, due to py_Classes/2@2777c63f.
[2025-06-03 11:00:24] (0s) Inferred that Class::Class.getScope/0#dispred#a9980f24/2@cbc60c1m is empty, due to py_Classes_10#join_rhs/2@132b13h6.
[2025-06-03 11:00:24] (0s) Inferred that project#ImportTime::ImportTimeScope.entryEdge/2#dispred#d2e3d051/2@f4a2dbiu is empty, due to ImportTime::ImportTimeScope.entryEdge/2#dispred#d2e3d051/3@14e4aflr.
[2025-06-03 11:00:24] (0s) Inferred that ImportTime::ImportTimeScope.getOuterVariable/1#dispred#2509ef70/3@31cc1fjt is empty, due to ImportTime::class_var_scope/3#59eab847/3@8ff9bdsa.
[2025-06-03 11:00:24] (0s) Inferred that project#ImportTime::ImportTimeScope.getOuterVariable/1#dispred#2509ef70/2@1cbf43a0 is empty, due to ImportTime::ImportTimeScope.getOuterVariable/1#dispred#2509ef70/3@31cc1fjt.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_Modules/1@3e58c00b with 1 rows and digest d7f3bed32abr7eno8gm5o6iork4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_scope_location_10#join_rhs/2@9c0b7etf
[2025-06-03 11:00:24] (0s)  >>> Created relation py_scope_location_10#join_rhs/2@9c0b7etf with 2 rows and digest b0d1e4gcnqgeulvhjf4o26rlmt9.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_stmts/4@93720efi with 4 rows and digest a89af713lunlauoc0telamtfmhc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Stmts::Stmt.getLocation/0#dispred#18f9d034/2@d3cb24lu
[2025-06-03 11:00:24] (0s)  >>> Created relation Stmts::Stmt.getLocation/0#dispred#18f9d034/2@d3cb24lu with 4 rows and digest 7d8bfeulto69vks79j5i3d9sfm5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Expr.getLocation/0#dispred#c128155b/2@d10f96ct
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Expr.getLocation/0#dispred#c128155b/2@d10f96ct with 12 rows and digest ab43be7kebrl0sb6npn66kpale8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstExtended::AstNode.getLocation/0#dispred#6b4dcb62/2@ccf6dfeh
[2025-06-03 11:00:24] (0s)  >>> Created relation AstExtended::AstNode.getLocation/0#dispred#6b4dcb62/2@ccf6dfeh with 18 rows and digest dc02db7hs3f0qvh83om3ml1cgfb.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate AstExtended::AstNode.getLocation/0#dispred#6b4dcb62  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_AstExtended::AstNode.getLocation/0#dispred#6b4dcb62/2@389a43u0 with 18 rows and digest dc02db7hs3f0qvh83om3ml1cgfb.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_strs/3@35cd6b34 with 6 rows and digest 1a52c3o1upg0r7j8ihsb0ofjk48.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_strs_120#join_rhs/3@bec877pd
[2025-06-03 11:00:24] (0s)  >>> Created relation py_strs_120#join_rhs/3@bec877pd with 6 rows and digest 0b6e5379s2556lsihb3d1f8ngh9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_exprs_10#join_rhs/2@073ac12u
[2025-06-03 11:00:24] (0s)  >>> Created relation py_exprs_10#join_rhs/2@073ac12u with 12 rows and digest 9bd5d68kbc0hm1jm25mtrlc7vi7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82/2@6a07768l
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82/2@6a07768l with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Import::ImportExpr.getTopName/0#dispred#d0ee50a0/2@9ac872i4 is empty, due to AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82/2@6a07768l.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82_0#antijoin_rhs/1@c9d75c81 is empty, due to AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82/2@6a07768l.
[2025-06-03 11:00:24] (0s) Inferred that Module::moduleImportedInPackage/1#90099bf3/2@22fd5di2 is empty, due to AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82/2@6a07768l.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82_10#join_rhs/2@049f24pe is empty, due to AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82/2@6a07768l.
[2025-06-03 11:00:24] (0s) Inferred that _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62_AstGenerated::ImportExpr_.getName/0#dispred#bfa__#join_rhs/2@adb6af0r is empty, due to AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82/2@6a07768l.
[2025-06-03 11:00:24] (0s) Inferred that _AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82__py_exprs_10#join_rhs_py_flow_bb_node_10#join___#antijoin_rhs/1@38ea97i6 is empty, due to AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82/2@6a07768l.
[2025-06-03 11:00:24] (0s) Inferred that Import::ImportExpr.relativeTopName/0#dispred#f2d90a1e/2@6741a2ur is empty, due to Import::ImportExpr.getTopName/0#dispred#d0ee50a0/2@9ac872i4.
[2025-06-03 11:00:24] (0s) Inferred that Module::isPotentialModuleFile/2#f93ea5ba/2@c2e6083m is empty, due to Module::moduleImportedInPackage/1#90099bf3/2@22fd5di2.
[2025-06-03 11:00:24] (0s) Inferred that _Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_Module::isRegularPackage/2#c6677254_Mod__#shared/2@4e30f3ku is empty, due to Module::moduleImportedInPackage/1#90099bf3/2@22fd5di2.
[2025-06-03 11:00:24] (0s) Inferred that Module::Module.hasFromFuture/1#dispred#cf037b23#fb/2@2cc94c7b is empty, due to AstGenerated::ImportExpr_.getName/0#dispred#bfa11d82_10#join_rhs/2@049f24pe.
[2025-06-03 11:00:24] (0s) Inferred that Module::transitively_imported_from_entry_point/1#96984e0d/1@3a5985nn is empty, due to _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62_AstGenerated::ImportExpr_.getName/0#dispred#bfa__#join_rhs/2@adb6af0r.
[2025-06-03 11:00:24] (0s) Inferred that project#Import::ImportExpr.relativeTopName/0#dispred#f2d90a1e/1@731b91al is empty, due to Import::ImportExpr.relativeTopName/0#dispred#f2d90a1e/2@6741a2ur.
[2025-06-03 11:00:24] (0s) Inferred that Module::isPotentialModuleFile/2#f93ea5ba_10#join_rhs/2@8f9b7dr9 is empty, due to Module::isPotentialModuleFile/2#f93ea5ba/2@c2e6083m.
[2025-06-03 11:00:24] (0s) Inferred that _Module::isPackage/2#0f4cbd4a_Module::isPotentialModuleFile/2#f93ea5ba_containerparent_10#join_rhs#antijoin_rhs/2@3ffb05ar is empty, due to Module::isPotentialModuleFile/2#f93ea5ba/2@c2e6083m.
[2025-06-03 11:00:24] (0s) Inferred that _Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb_10#join_rhs_Files::Impl::Container.getFolder/__#antijoin_rhs/2@bdbac54b is empty, due to _Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_Module::isRegularPackage/2#c6677254_Mod__#shared/2@4e30f3ku.
[2025-06-03 11:00:24] (0s) Inferred that _Exprs::Expr.getEnclosingModule/0#dispred#e365231e#bf_Module::Module.hasFromFuture/1#dispred#cf037b2__#antijoin_rhs/1@6c4b5dau is empty, due to Module::Module.hasFromFuture/1#dispred#cf037b23#fb/2@2cc94c7b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_exprs_230#join_rhs/3@bbf66b9g
[2025-06-03 11:00:24] (0s)  >>> Created relation py_exprs_230#join_rhs/3@bbf66b9g with 12 rows and digest 0c431255t2b6bcn4ih91lf1ofnd.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_stmts_10#join_rhs/2@97ec97cm
[2025-06-03 11:00:24] (0s)  >>> Created relation py_stmts_10#join_rhs/2@97ec97cm with 4 rows and digest f30a89ck0noiuf2idc38lvb3cu1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::ImportStar_.getModule/0#dispred#f7687244/2@4fac27bk
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::ImportStar_.getModule/0#dispred#f7687244/2@4fac27bk with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Import::ImportStar.getModuleExpr/0#dispred#8ab9ce73/2@ab8dbec8 is empty, due to AstGenerated::ImportStar_.getModule/0#dispred#f7687244/2@4fac27bk.
[2025-06-03 11:00:24] (0s) Inferred that cached_Import::ImportStar.getModuleExpr/0#dispred#8ab9ce73/2@9212cahk is empty, due to Import::ImportStar.getModuleExpr/0#dispred#8ab9ce73/2@ab8dbec8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::ImportMember_.getModule/0#dispred#6911d1ff/2@4412afvu
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::ImportMember_.getModule/0#dispred#6911d1ff/2@4412afvu with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::ImportMember_.getModule/0#dispred#6911d1ff_10#join_rhs/2@d03c004n is empty, due to AstGenerated::ImportMember_.getModule/0#dispred#6911d1ff/2@4412afvu.
[2025-06-03 11:00:24] (0s) Inferred that Flow::ImportMemberNode.getModule/1#dispred#98e51eaa#ffb/3@e3a7330e is empty, due to AstGenerated::ImportMember_.getModule/0#dispred#6911d1ff_10#join_rhs/2@d03c004n.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::ModuleVariable.global_variable_import/0#d33e6f1c/2@8607e5oe is empty, due to Flow::ImportMemberNode.getModule/1#dispred#98e51eaa#ffb/3@e3a7330e.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Assign_.getValue/0#dispred#53d00b56/2@2d1650hr
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Assign_.getValue/0#dispred#53d00b56/2@2d1650hr with 2 rows and digest 8ba329fostk6hhn20s3hm3njln3.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#13/1@c751f579
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#13/1@c751f579 with 1 rows and digest d9a86e15kb0ghibh1dc6a1uvjaa.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Assign_.getValue/0#dispred#53d00b56_10#join_rhs/2@e905adi5
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Assign_.getValue/0#dispred#53d00b56_10#join_rhs/2@e905adi5 with 2 rows and digest 577b19f5kgo2cvfal49u6d0e5u6.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_expr_lists/3@1b5e391t with 5 rows and digest fac03addnj5lu0lng6aclt1qdia.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_expr_lists_120#join_rhs/3@3304a2u1
[2025-06-03 11:00:24] (0s)  >>> Created relation py_expr_lists_120#join_rhs/3@3304a2u1 with 5 rows and digest 87ac81dgdgkm18t8230sa8gvj3e.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_exprs_203#join_rhs/3@0e8aa5mo
[2025-06-03 11:00:24] (0s)  >>> Created relation py_exprs_203#join_rhs/3@0e8aa5mo with 12 rows and digest b738581uukga5bc0n6ijk3cfvt8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::ExprList_.getItem/1#dispred#2a70324c/3@876795dq
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::ExprList_.getItem/1#dispred#2a70324c/3@876795dq with 5 rows and digest 54c1fd1t49830jtv59g8uumg6ac.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Call_.getPositionalArgs/0#dispred#2e9514f5/2@82bcd5pp
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Call_.getPositionalArgs/0#dispred#2e9514f5/2@82bcd5pp with 3 rows and digest 2289d5ie3c3muk3gbhdhb966c60.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7/3@21a3884h
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7/3@21a3884h with 3 rows and digest 34f843v7hpphjfi7re8odej1f26.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#10/1@8754f207
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#10/1@8754f207 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Flow::StarredNode.getValue/0#dispred#77106d16/2@ecb8631a is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#10/1@8754f207.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7_py_exprs#antijoin_rhs/3@492ad1u9
[2025-06-03 11:00:24] (0s)  >>> Created relation _AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7_py_exprs#antijoin_rhs/3@492ad1u9 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Call.getArg/1#dispred#50c53eea/3@63b78cee
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Call.getArg/1#dispred#50c53eea/3@63b78cee with 3 rows and digest 34f843v7hpphjfi7re8odej1f26.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Call.getArg/1#dispred#50c53eea_120#join_rhs/3@38981eo1
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Call.getArg/1#dispred#50c53eea_120#join_rhs/3@38981eo1 with 3 rows and digest 7afbe5kin5agrv5li9ptr8rd7e7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Function::FunctionExpr.getADecoratorCall/0#062a6e2a/2@i1#800dedr3 (iteration 1)
[2025-06-03 11:00:24] (0s) Empty delta for Function::FunctionExpr.getADecoratorCall/0#062a6e2a_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Created relation Function::FunctionExpr.getADecoratorCall/0#062a6e2a/2@800dedr3 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Function::FunctionDef#ffa22159/2@28dc4916
[2025-06-03 11:00:24] (0s)  >>> Created relation Function::FunctionDef#ffa22159/2@28dc4916 with 1 rows and digest 8a200coheaen9vqfp1n2ct9hvo0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#Function::FunctionDef#ffa22159/1@14296568
[2025-06-03 11:00:24] (0s)  >>> Created relation project#Function::FunctionDef#ffa22159/1@14296568 with 1 rows and digest 1f9b4aq1tnpiqgnv5jao94ktbs9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#11/1@64a70adk
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#11/1@64a70adk with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Class::ClassDef#83097204/1@6686b2aa is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#11/1@64a70adk.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Stmts::AssignStmt#45f46a75/1@b012f16r
[2025-06-03 11:00:24] (0s)  >>> Created relation Stmts::AssignStmt#45f46a75/1@b012f16r with 1 rows and digest f7d889nprllq02p2qjap4hqq1lb.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Assign__not_Stmts::AssignStmt_Class::ClassDef_Function::FunctionDef#9571075b/1@c32212q8
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Assign__not_Stmts::AssignStmt_Class::ClassDef_Function::FunctionDef#9571075b/1@c32212q8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_module_path_10#join_rhs/2@5a2bd62f
[2025-06-03 11:00:24] (0s)  >>> Created relation py_module_path_10#join_rhs/2@5a2bd62f with 1 rows and digest feae77egdjp8f023gm46qqod1ae.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::Module.isPackage/0#dispred#a8679d5f/1@9131c737
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::Module.isPackage/0#dispred#a8679d5f/1@9131c737 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Module_.getName/0#2ccd625a/2@38f91ee0
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Module_.getName/0#2ccd625a/2@38f91ee0 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Module_.getName/0#2ccd625a_0#antijoin_rhs/1@a8f41596 is empty, due to AstGenerated::Module_.getName/0#2ccd625a/2@38f91ee0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985/3@e59cbcdu
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985/3@e59cbcdu with 16 rows and digest f7c6ddfmk6vpmap1ig0o3upjog6.
[2025-06-03 11:00:24] (0s) Promoting strings for predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985
[2025-06-03 11:00:24] (0s) Promoted strings in predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985 in memory, took 4ms
[2025-06-03 11:00:24] (0s) Saving stringpool to save strings from predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985
[2025-06-03 11:00:24] (0s) Saved stringpool to save strings from predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985, took 0ms
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985/3@c992f399 with 16 rows and digest f7c6ddfmk6vpmap1ig0o3upjog6.
[2025-06-03 11:00:24] (0s)  >>> Created relation containerparent/2@c7062ak0 with 6 rows and digest e1c3029lvpfvfb0mkmo2p7rp71a.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_flags_versioned/3@ee57f1af with 71 rows and digest 25d62cj75jmt29m7c56bb4bn0u1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_flags_versioned#antijoin_rhs/2@699620a1
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_flags_versioned#antijoin_rhs/2@699620a1 with 1 rows and digest a94f57n8l9f1u22f4i6phmum8f4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_flags_versioned#count_range/2@229366h4
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_flags_versioned#count_range/2@229366h4 with 1 rows and digest 2700ban51uksjhmk81rt4lnepna.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate __py_flags_versioned#count_range#join_rhs/2@14e24109
[2025-06-03 11:00:24] (0s)  >>> Created relation __py_flags_versioned#count_range#join_rhs/2@14e24109 with 1 rows and digest 0cd00a26s94cnlgrlr6ivp1siu0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::import_path_element/1#dd8f0854/2@dee48ej4
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::import_path_element/1#dd8f0854/2@dee48ej4 with 5 rows and digest 9af31cpc6high2lvo97fvqh36p1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::import_path_element/1#dd8f0854_10#join_rhs/2@daa5d9bb
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::import_path_element/1#dd8f0854_10#join_rhs/2@daa5d9bb with 5 rows and digest be3f21bgk1ajh2hsp47i1d8c4d1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::Container.isImportRoot/1#dispred#c81b5504/2@122714vb
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::Container.isImportRoot/1#dispred#c81b5504/2@122714vb with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Files::Container.getImportRoot/1#dispred#d481489f#bff/3@e62cbc8a is empty, due to Files::Container.isImportRoot/1#dispred#c81b5504/2@122714vb.
[2025-06-03 11:00:24] (0s) Inferred that project#Files::Container.isImportRoot/1#dispred#c81b5504/1@2f20cab0 is empty, due to Files::Container.isImportRoot/1#dispred#c81b5504/2@122714vb.
[2025-06-03 11:00:24] (0s) Inferred that project#Files::Container.getImportRoot/1#dispred#d481489f#bff/2@49b53109 is empty, due to Files::Container.getImportRoot/1#dispred#d481489f#bff/3@e62cbc8a.
[2025-06-03 11:00:24] (0s) Inferred that _Files::Container.getImportRoot/1#dispred#d481489f#bff_Files::Impl::Container.splitAbsolutePath/2#di__#shared/3@44ba06cf is empty, due to Files::Container.getImportRoot/1#dispred#d481489f#bff/3@e62cbc8a.
[2025-06-03 11:00:24] (0s) Inferred that #Files::Container.getParent/0#dispred#c9586936Plus#fb#flipped/2@2503cc9v is empty, due to project#Files::Container.isImportRoot/1#dispred#c81b5504/1@2f20cab0.
[2025-06-03 11:00:24] (0s) Inferred that _#Files::Container.getParent/0#dispred#c9586936Plus#fb#flipped_10#join_rhs_folders_project#Files::Co__#antijoin_rhs/1@151961am is empty, due to project#Files::Container.isImportRoot/1#dispred#c81b5504/1@2f20cab0.
[2025-06-03 11:00:24] (0s) Inferred that __Files::Container.getImportRoot/1#dispred#d481489f#bff_Files::Impl::Container.splitAbsolutePath/2#d__#antijoin_rhs/3@6f65f4ib is empty, due to _Files::Container.getImportRoot/1#dispred#d481489f#bff_Files::Impl::Container.splitAbsolutePath/2#di__#shared/3@44ba06cf.
[2025-06-03 11:00:24] (0s) Inferred that Files::Container.getImportRoot/0#dispred#b07a8632#bf/2@178c02a0 is empty, due to _Files::Container.getImportRoot/1#dispred#d481489f#bff_Files::Impl::Container.splitAbsolutePath/2#di__#shared/3@44ba06cf.
[2025-06-03 11:00:24] (0s) Inferred that #Files::Container.getParent/0#dispred#c9586936Plus#fb#flipped_10#join_rhs/2@25ee87d6 is empty, due to #Files::Container.getParent/0#dispred#c9586936Plus#fb#flipped/2@2503cc9v.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::isStubRoot/1#400a3be9/1@4b7698pj
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::isStubRoot/1#400a3be9/1@4b7698pj with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_021#join_rhs/3@9a865792
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_021#join_rhs/3@9a865792 with 16 rows and digest 1d4b57pr290eogdgr2h9t9jth6b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_201#join_rhs/3@117d3emi
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_201#join_rhs/3@117d3emi with 16 rows and digest f19972o9jqs399lm7dasvuqksfb.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::moduleNameFromBase/1#adf5634f/2@f22257b3
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::moduleNameFromBase/1#adf5634f/2@f22257b3 with 9 rows and digest e01c885svnjpu6an59ah6ci67ed.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate containerparent_10#join_rhs/2@e6fa8emb
[2025-06-03 11:00:24] (0s)  >>> Created relation containerparent_10#join_rhs/2@e6fa8emb with 6 rows and digest 5459a2miomhp22qidt3l4jnmp01.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate m#Files::File.isPossibleEntryPoint/0#dispred#4586fa29#b/1@15bbe8h4
[2025-06-03 11:00:24] (0s)  >>> Created relation m#Files::File.isPossibleEntryPoint/0#dispred#4586fa29#b/1@15bbe8h4 with 2 rows and digest 32f8da4lkdq9gqm0o98387e1grd.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Compare_.getComparators/0#dispred#50661f3c/2@1b3416kd
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Compare_.getComparators/0#dispred#50661f3c/2@1b3416kd with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Compare_.getComparator/1#dispred#210faf25#ffb/3@c696dcuo is empty, due to AstGenerated::Compare_.getComparators/0#dispred#50661f3c/2@1b3416kd.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Compare_.getComparator/1#dispred#210faf25#bff/3@a05249h6 is empty, due to AstGenerated::Compare_.getComparators/0#dispred#50661f3c/2@1b3416kd.
[2025-06-03 11:00:24] (0s) Inferred that Operations::Compare.compares/3#dispred#57048f38#fbff/4@71a430or is empty, due to AstGenerated::Compare_.getComparator/1#dispred#210faf25#bff/3@a05249h6.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Compare_.getLeft/0#dispred#02e6fc6e/2@401a4bvm
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Compare_.getLeft/0#dispred#02e6fc6e/2@401a4bvm with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate CachedStages::Stages::AST::backref/0#dddb3000/0@dab1424s
[2025-06-03 11:00:24] (0s)  >>> Created relation CachedStages::Stages::AST::backref/0#dddb3000/0@dab1424s with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::If_.getTest/0#dispred#c582b403/2@4ac151mr
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::If_.getTest/0#dispred#c582b403/2@4ac151mr with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::File.getShortName/0#dispred#1a9d2ead/2@f8191cuc
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::File.getShortName/0#dispred#1a9d2ead/2@f8191cuc with 2 rows and digest 2b606feplo947vd6m7u0oup4aue.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate m#Module::Module.getFile/0#dispred#53eb9b1b#fb/1@c908a2n6
[2025-06-03 11:00:24] (0s)  >>> Created relation m#Module::Module.getFile/0#dispred#53eb9b1b#fb/1@c908a2n6 with 3 rows and digest d36ce5qsm8g8252rgibejqbj1qf.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::Module.getFile/0#dispred#53eb9b1b#fb/2@eafe587d
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::Module.getFile/0#dispred#53eb9b1b#fb/2@eafe587d with 1 rows and digest 58d110scfk169u0vappqlnmo2c3.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_Functions_10#join_rhs/2@eb0da2ov
[2025-06-03 11:00:24] (0s)  >>> Created relation py_Functions_10#join_rhs/2@eb0da2ov with 1 rows and digest 1f35266gmk07ge8nckfq6l3sqs0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Module_.getKind/0#dd90ac6f/2@95377982
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Module_.getKind/0#dd90ac6f/2@95377982 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Module_.getKind/0#dd90ac6f_0#antijoin_rhs/1@a84c4ek9 is empty, due to AstGenerated::Module_.getKind/0#dd90ac6f/2@95377982.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::Module.getKind/0#dispred#ccc4de3e/2@2d81416l
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::Module.getKind/0#dispred#ccc4de3e/2@2d81416l with 1 rows and digest c1c82c8uro1dcndohkndeqinho4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Function::Function.toString/0#dispred#fa659a0b/2@be7086fo
[2025-06-03 11:00:24] (0s)  >>> Created relation Function::Function.toString/0#dispred#fa659a0b/2@be7086fo with 1 rows and digest b76bacm99j3uq5aovr6aufl9b08.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Call_.getFunc/0#dispred#9f0e5cfd/2@3f4718o2
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Call_.getFunc/0#dispred#9f0e5cfd/2@3f4718o2 with 3 rows and digest 95eeedlg4c22a19s6bpaf45vt26.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Call_.getFunc/0#dispred#9f0e5cfd_10#join_rhs/2@1cfa8c7b
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Call_.getFunc/0#dispred#9f0e5cfd_10#join_rhs/2@1cfa8c7b with 3 rows and digest 79f7c7bchgf6akfdria7kubravd.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_numbers/3@fdd7bbbb with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that py_numbers_201#join_rhs/3@1c498f0c is empty, due to py_numbers/3@fdd7bbbb.
[2025-06-03 11:00:24] (0s) Inferred that Exprs::ImaginaryLiteral#d2b980a7/2@d3f538r2 is empty, due to py_numbers_201#join_rhs/3@1c498f0c.
[2025-06-03 11:00:24] (0s) Inferred that Exprs::FloatLiteral#eb01d209/1@b7b0a64j is empty, due to py_numbers_201#join_rhs/3@1c498f0c.
[2025-06-03 11:00:24] (0s) Inferred that Exprs::ImaginaryLiteral#d2b980a7_0#antijoin_rhs/1@fd0e28cj is empty, due to Exprs::ImaginaryLiteral#d2b980a7/2@d3f538r2.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::IntegerLiteral#51a23263/1@13583e4r
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::IntegerLiteral#51a23263/1@13583e4r with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Num#4a336806/1@1e48f0e6
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Num#4a336806/1@1e48f0e6 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#4/1@d21604ou
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#4/1@d21604ou with 6 rows and digest da2dc5oh0hf1t6vn7v3f0ashqk0.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_flow_bb_node/4@f7fce6uq with 16 rows and digest 195fc8cicl7br0opds2skdo3nla.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_scopes/2@a7df0e9a with 16 rows and digest 54ecd0pbd83uvgntvlvorimr1k1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Stmts::Stmt.getScope/0#dispred#1debab62/2@01df4doa
[2025-06-03 11:00:24] (0s)  >>> Created relation Stmts::Stmt.getScope/0#dispred#1debab62/2@01df4doa with 4 rows and digest 0bc29afdvb8rkg5id1mjrerriaa.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Expr.getScope/0#dispred#71058b3b/2@6f18d7fb
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Expr.getScope/0#dispred#71058b3b/2@6f18d7fb with 12 rows and digest 5ffb3d2guq0sjis15ac7ckqb4m7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Function::Function.getEnclosingScope/0#dispred#2073a78d/2@2bb168ta
[2025-06-03 11:00:24] (0s)  >>> Created relation Function::Function.getEnclosingScope/0#dispred#2073a78d/2@2bb168ta with 1 rows and digest 57035btodt35ck120tnd3akqgmc.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_aliases/3@db779a0g with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Alias_.getAsname/0#dispred#be74675b/2@ca3a16t2 is empty, due to py_aliases/3@db779a0g.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Alias_.getValue/0#dispred#8d361153/2@439aa999 is empty, due to py_aliases/3@db779a0g.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Import_.getAName/0#dispred#7741064f/2@1184beuh is empty, due to py_aliases/3@db779a0g.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Alias_.getValue/0#dispred#8d361153_10#join_rhs/2@e52080jn is empty, due to AstGenerated::Alias_.getValue/0#dispred#8d361153/2@439aa999.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Import_.getAName/0#dispred#7741064f_10#join_rhs/2@34bb70pf is empty, due to AstGenerated::Import_.getAName/0#dispred#7741064f/2@1184beuh.
[2025-06-03 11:00:24] (0s) Inferred that Import::Import.getASubExpression/0#dispred#d4e8ee19/2@3ae2502a is empty, due to AstGenerated::Import_.getAName/0#dispred#7741064f_10#join_rhs/2@34bb70pf.
[2025-06-03 11:00:24] (0s) Inferred that Import::Import.getASubExpression/0#dispred#d4e8ee19_10#join_rhs/2@6e341da8 is empty, due to Import::Import.getASubExpression/0#dispred#d4e8ee19/2@3ae2502a.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#5/1@687f043h
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#5/1@687f043h with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::PlaceHolder_.getVariable/0#dispred#708ee41c/2@df5b9dun is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#5/1@687f043h.
[2025-06-03 11:00:24] (0s) Inferred that Exprs::PlaceHolder.getId/0#dispred#9822eecb/2@bdd2efr6 is empty, due to AstGenerated::PlaceHolder_.getVariable/0#dispred#708ee41c/2@df5b9dun.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Scope_#cc08b056/1@78c72bkc
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Scope_#cc08b056/1@78c72bkc with 2 rows and digest 5a3e3bu55kd2p0hdc2avgtn4cd0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::BinaryExpr_.getLeft/0#dispred#7495763a/2@746179qi
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::BinaryExpr_.getLeft/0#dispred#7495763a/2@746179qi with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Stmts::AugAssign.getTarget/0#dispred#5c61a8f3/2@084e7b26 is empty, due to AstGenerated::BinaryExpr_.getLeft/0#dispred#7495763a/2@746179qi.
[2025-06-03 11:00:24] (0s) Inferred that _Stmts::AugAssign.getTarget/0#dispred#5c61a8f3_py_flow_bb_node_10#join_rhs#shared/2@fd3c26dk is empty, due to Stmts::AugAssign.getTarget/0#dispred#5c61a8f3/2@084e7b26.
[2025-06-03 11:00:24] (0s) Inferred that _#Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ecPlus#sinkBound#5#3_Flow::ControlFlowNode.__#higher_order_body/1@72d72bd7 is empty, due to _Stmts::AugAssign.getTarget/0#dispred#5c61a8f3_py_flow_bb_node_10#join_rhs#shared/2@fd3c26dk.
[2025-06-03 11:00:24] (0s) Inferred that Flow::augstore/2#23690dbf/2@cd7820jb is empty, due to _Stmts::AugAssign.getTarget/0#dispred#5c61a8f3_py_flow_bb_node_10#join_rhs#shared/2@fd3c26dk.
[2025-06-03 11:00:24] (0s) Inferred that doublyBoundedFastTC:Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec:_#Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ecPlus#sinkBound#5#3_Flow::ControlFlowNode.__#higher_order_body:#Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ecPlus#sinkBound#5#3/2@777f4428 is empty, due to _#Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ecPlus#sinkBound#5#3_Flow::ControlFlowNode.__#higher_order_body/1@72d72bd7.
[2025-06-03 11:00:24] (0s) Inferred that Flow::augstore/2#23690dbf_1#antijoin_rhs/1@67fb979t is empty, due to Flow::augstore/2#23690dbf/2@cd7820jb.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_exprs_1203#join_rhs/4@f75e08cn
[2025-06-03 11:00:24] (0s)  >>> Created relation py_exprs_1203#join_rhs/4@f75e08cn with 12 rows and digest f69cad42ck1fiprd3aje3qerp45.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::AugAssign_.getOperation/0#dispred#e84e4db7/2@2f1dc9pq
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::AugAssign_.getOperation/0#dispred#e84e4db7/2@2f1dc9pq with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#8/1@e7e871th
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#8/1@e7e871th with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that __py_exprs_10#join_rhs#antijoin_rhs#8_py_flow_bb_node_10#join_rhs#shared/1@82a42ajs is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#8/1@e7e871th.
[2025-06-03 11:00:24] (0s) Inferred that Flow::TupleNode#c15d0066/1@66ae848n is empty, due to __py_exprs_10#join_rhs#antijoin_rhs#8_py_flow_bb_node_10#join_rhs#shared/1@82a42ajs.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Parameter_#667c10c8/1@eae0c5ts
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Parameter_#667c10c8/1@eae0c5ts with 6 rows and digest da2dc5oh0hf1t6vn7v3f0ashqk0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_exprs_20#join_rhs/2@b6605fup
[2025-06-03 11:00:24] (0s)  >>> Created relation py_exprs_20#join_rhs/2@b6605fup with 12 rows and digest d2e9b2o1qbbnuihp5mie18jpef0.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_parameter_lists/2@1e178err with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Function_.getAnArg/0#dispred#8d75a599/2@fa89d62t is empty, due to py_parameter_lists/2@1e178err.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Function_.getArg/1#dispred#0cfe9897/3@6faeb3du is empty, due to py_parameter_lists/2@1e178err.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Function::Function.getKeywordOnlyArg/1#dispred#a9482c2f/3@421581tb
[2025-06-03 11:00:24] (0s)  >>> Created relation Function::Function.getKeywordOnlyArg/1#dispred#a9482c2f/3@421581tb with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Function_.getKwarg/0#dispred#bdc4eab3/2@b97aabko
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Function_.getKwarg/0#dispred#bdc4eab3/2@b97aabko with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Function_.getVararg/0#dispred#07f0fe61/2@cdaed89u
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Function_.getVararg/0#dispred#07f0fe61/2@cdaed89u with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Function::Parameter#352debb4/1@9be23465
[2025-06-03 11:00:24] (0s)  >>> Created relation Function::Parameter#352debb4/1@9be23465 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Function::Parameter.asName/0#dispred#07e8fb90/2@2f65d4du is empty, due to Function::Parameter#352debb4/1@9be23465.
[2025-06-03 11:00:24] (0s) Inferred that Function::Parameter.getDefault/0#dispred#b09fbf4b/2@abf7deok is empty, due to Function::Parameter#352debb4/1@9be23465.
[2025-06-03 11:00:24] (0s) Inferred that Variables::LocalVariable.isParameter/0#dispred#8d4969a3/1@9deae9d5 is empty, due to Function::Parameter#352debb4/1@9be23465.
[2025-06-03 11:00:24] (0s) Inferred that project#Function::Parameter.getDefault/0#dispred#b09fbf4b/1@6321df0d is empty, due to Function::Parameter.getDefault/0#dispred#b09fbf4b/2@abf7deok.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_flow_bb_node_10#join_rhs/2@45b774t7
[2025-06-03 11:00:24] (0s)  >>> Created relation py_flow_bb_node_10#join_rhs/2@45b774t7 with 16 rows and digest fb8e7apompi19oo74usm5ktdcn7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::NameNode#b2c17c8a/1@9fa54cf5
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::NameNode#b2c17c8a/1@9fa54cf5 with 6 rows and digest 26b21ebrhu3b1hhc2qfhgihrl90.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_type_parameter_lists/2@cef51fnk with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_pattern_lists/3@c2339etb with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that py_pattern_lists_201#join_rhs/3@1f47b0lk is empty, due to py_pattern_lists/3@c2339etb.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_stmt_lists/3@bef968k2 with 2 rows and digest c51caaguc7njeai0iiulu8qmo6f.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_stmt_lists_021#join_rhs/3@e85cc0kt
[2025-06-03 11:00:24] (0s)  >>> Created relation py_stmt_lists_021#join_rhs/3@e85cc0kt with 2 rows and digest 75449428624ig13lj50d7e6uit4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Stmts::ExceptionHandler#2d12ceec/1@66ebb9rh
[2025-06-03 11:00:24] (0s)  >>> Created relation Stmts::ExceptionHandler#2d12ceec/1@66ebb9rh with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Stmts::ExceptGroupStmt#d71d1244/1@0312883j is empty, due to Stmts::ExceptionHandler#2d12ceec/1@66ebb9rh.
[2025-06-03 11:00:24] (0s) Inferred that Stmts::ExceptStmt#b7ee7437/1@741f2ef0 is empty, due to Stmts::ExceptionHandler#2d12ceec/1@66ebb9rh.
[2025-06-03 11:00:24] (0s) Inferred that Stmts::ExceptGroupStmt.getName/0#dispred#6e8e2398/2@ced8ecpc is empty, due to Stmts::ExceptGroupStmt#d71d1244/1@0312883j.
[2025-06-03 11:00:24] (0s) Inferred that Stmts::ExceptStmt.getName/0#dispred#13f5307e/2@c40eb561 is empty, due to Stmts::ExceptStmt#b7ee7437/1@741f2ef0.
[2025-06-03 11:00:24] (0s) Inferred that Exceptions::ExceptGroupFlowNode.getName/0#dispred#c709eb39/2@b0af12tk is empty, due to Stmts::ExceptGroupStmt.getName/0#dispred#6e8e2398/2@ced8ecpc.
[2025-06-03 11:00:24] (0s) Inferred that Exceptions::ExceptFlowNode.getName/0#dispred#eedc34d6/2@ac983f07 is empty, due to Stmts::ExceptStmt.getName/0#dispred#13f5307e/2@c40eb561.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::exception_group_capture/2#d53f492d/2@ba1f39e8 is empty, due to Exceptions::ExceptGroupFlowNode.getName/0#dispred#c709eb39/2@b0af12tk.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::exception_capture/2#fd0779df/2@5691e8hg is empty, due to Exceptions::ExceptFlowNode.getName/0#dispred#eedc34d6/2@ac983f07.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::exception_group_capture/2#d53f492d/2@790fdeja is empty, due to SsaDefinitions::SsaSource::exception_group_capture/2#d53f492d/2@ba1f39e8.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::exception_capture/2#fd0779df/2@793700dj is empty, due to SsaDefinitions::SsaSource::exception_capture/2#fd0779df/2@5691e8hg.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::ExprList_.getAnItem/0#dispred#09559f69/2@76feea9p
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::ExprList_.getAnItem/0#dispred#09559f69/2@76feea9p with 5 rows and digest 17de2ff5hho0c1ffu5v1cqdam15.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_stmt_lists_120#join_rhs/3@0e183epv
[2025-06-03 11:00:24] (0s)  >>> Created relation py_stmt_lists_120#join_rhs/3@0e183epv with 2 rows and digest e1f3cekb8qd336h5u1k1i80g37a.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::ExceptGroupStmt_.getType/0#eb73b264/2@1a11ec01
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::ExceptGroupStmt_.getType/0#eb73b264/2@1a11ec01 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Tuple_.getElts/0#dispred#df30ffae/2@4112754c
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Tuple_.getElts/0#dispred#df30ffae/2@4112754c with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Tuple_.getAnElt/0#dispred#dfceea61/2@9b2dda9q is empty, due to AstGenerated::Tuple_.getElts/0#dispred#df30ffae/2@4112754c.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Tuple_.getElt/1#dispred#ba261ee0/3@09213fgb is empty, due to AstGenerated::Tuple_.getElts/0#dispred#df30ffae/2@4112754c.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Tuple_.getAnElt/0#dispred#dfceea61_10#join_rhs/2@515ef3mm is empty, due to AstGenerated::Tuple_.getAnElt/0#dispred#dfceea61/2@9b2dda9q.
[2025-06-03 11:00:24] (0s) Inferred that project#AstGenerated::Tuple_.getElt/1#dispred#ba261ee0/1@b0f9d02h is empty, due to AstGenerated::Tuple_.getElt/1#dispred#ba261ee0/3@09213fgb.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Tuple_.getElt/1#dispred#ba261ee0_201#join_rhs/3@552d09pr is empty, due to AstGenerated::Tuple_.getElt/1#dispred#ba261ee0/3@09213fgb.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Tuple_.getElt/1#dispred#ba261ee0_120#join_rhs/3@9a57558l is empty, due to AstGenerated::Tuple_.getElt/1#dispred#ba261ee0/3@09213fgb.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::ExceptStmt_.getType/0#552fde1b/2@9e1cc6u1
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::ExceptStmt_.getType/0#552fde1b/2@9e1cc6u1 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Function_.getBody/0#dispred#6a61cbfb/2@db9bc52p
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Function_.getBody/0#dispred#6a61cbfb/2@db9bc52p with 1 rows and digest 8f5068dt4ivgfhbjhr5upl3lg5f.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Function_.getBody/0#dispred#6a61cbfb_10#join_rhs/2@3347b8uo
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Function_.getBody/0#dispred#6a61cbfb_10#join_rhs/2@3347b8uo with 1 rows and digest b704b8a561727frmvautkvf9ur0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_stmts_20#join_rhs/2@e780a92c
[2025-06-03 11:00:24] (0s)  >>> Created relation py_stmts_20#join_rhs/2@e780a92c with 4 rows and digest 42c7cd0iid3f94oejbjeuk51945.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::If_.getBody/0#dispred#2a3ec055/2@48f1ab4m
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::If_.getBody/0#dispred#2a3ec055/2@48f1ab4m with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::If_.getBody/0#dispred#2a3ec055_10#join_rhs/2@4b13e6i4 is empty, due to AstGenerated::If_.getBody/0#dispred#2a3ec055/2@48f1ab4m.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_dict_item_lists/2@cc0300gb with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Call_.getNamedArgs/0#dispred#1e143173/2@e37cfbta is empty, due to py_dict_item_lists/2@cc0300gb.
[2025-06-03 11:00:24] (0s) Inferred that py_dict_item_lists_10#join_rhs/2@70e6f16k is empty, due to py_dict_item_lists/2@cc0300gb.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::Call_.getNamedArgs/0#dispred#1e143173_10#join_rhs/2@2eef1ana is empty, due to AstGenerated::Call_.getNamedArgs/0#dispred#1e143173/2@e37cfbta.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#16/1@a85b3cn4
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#16/1@a85b3cn4 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#15/1@3c36657n
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#15/1@3c36657n with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Function::Lambda.getArgs/0#dispred#6c207427/2@4643cbgc is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#15/1@3c36657n.
[2025-06-03 11:00:24] (0s) Inferred that Function::Lambda.getASubExpression/0#dispred#a8882ebd/2@3a0bd610 is empty, due to Function::Lambda.getArgs/0#dispred#6c207427/2@4643cbgc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#14/1@a95d27eh
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#14/1@a95d27eh with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#12/1@260003qk
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#12/1@260003qk with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#9/1@482d23k2
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#9/1@482d23k2 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Comprehensions::ListComp.getFunction/0#dispred#635f9f84/2@22ec95mf is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#9/1@482d23k2.
[2025-06-03 11:00:24] (0s) Inferred that Comprehensions::ListComp.getFunction/0#dispred#635f9f84_10#join_rhs/2@59538du3 is empty, due to Comprehensions::ListComp.getFunction/0#dispred#635f9f84/2@22ec95mf.
[2025-06-03 11:00:24] (0s) Inferred that Comprehensions::Comp.getNthInnerLoop/1#4beb1f8c/3@3a89f4ds is empty, due to Comprehensions::ListComp.getFunction/0#dispred#635f9f84_10#join_rhs/2@59538du3.
[2025-06-03 11:00:24] (0s) Inferred that project#Comprehensions::Comp.getNthInnerLoop/1#4beb1f8c/2@1b9331pm is empty, due to Comprehensions::Comp.getNthInnerLoop/1#4beb1f8c/3@3a89f4ds.
[2025-06-03 11:00:24] (0s) Inferred that project#Comprehensions::Comp.getNthInnerLoop/1#4beb1f8c_10#join_rhs/2@29c265cc is empty, due to project#Comprehensions::Comp.getNthInnerLoop/1#4beb1f8c/2@1b9331pm.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::For_.getBody/0#dispred#166335e5/2@34a3647m
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::For_.getBody/0#dispred#166335e5/2@34a3647m with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::For_.getAStmt/0#dispred#cec2a04c/2@d1941b5c is empty, due to AstGenerated::For_.getBody/0#dispred#166335e5/2@34a3647m.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::For_.getAStmt/0#dispred#cec2a04c_10#join_rhs/2@104a79re is empty, due to AstGenerated::For_.getAStmt/0#dispred#cec2a04c/2@d1941b5c.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_stmts_032#join_rhs/3@ef43dca7
[2025-06-03 11:00:24] (0s)  >>> Created relation py_stmts_032#join_rhs/3@ef43dca7 with 4 rows and digest fda3f0f5uic4rlpgqktdrslnb07.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::ExprStmt_.getValue/0#dispred#5b34bb96/2@1e2508an
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::ExprStmt_.getValue/0#dispred#5b34bb96/2@1e2508an with 2 rows and digest 85122dk8kvb0dpqrdp6oitrfomf.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#7/1@f0f822vn
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#7/1@f0f822vn with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Starred_.getValue/0#dispred#3c0230c4/2@390813gl
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Starred_.getValue/0#dispred#3c0230c4/2@390813gl with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::ClassExpr_.getABase/0#dispred#7af4e693/2@d775edts
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::ClassExpr_.getABase/0#dispred#7af4e693/2@d775edts with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Class::ClassExpr.getASubExpression/0#dispred#93e12f13/2@0a58c9kk
[2025-06-03 11:00:24] (0s)  >>> Created relation Class::ClassExpr.getASubExpression/0#dispred#93e12f13/2@0a58c9kk with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_arguments/2@2b937e7s with 1 rows and digest c2b3a1dsu8mp7ktlm7svecu21rb.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Arguments_.getDefaults/0#dispred#583dac8a/2@e06885ht
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Arguments_.getDefaults/0#dispred#583dac8a/2@e06885ht with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Arguments_.getKwDefaults/0#dispred#d6eb64ab/2@f69dbd3u
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Arguments_.getKwDefaults/0#dispred#d6eb64ab/2@f69dbd3u with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Function::Arguments.getASubExpression/0#dispred#f1c80c65/2@85fa0ajq
[2025-06-03 11:00:24] (0s)  >>> Created relation Function::Arguments.getASubExpression/0#dispred#f1c80c65/2@85fa0ajq with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_arguments_10#join_rhs/2@9dbccce2
[2025-06-03 11:00:24] (0s)  >>> Created relation py_arguments_10#join_rhs/2@9dbccce2 with 1 rows and digest 6733bcke3ur7sjg22vdd6c0tl03.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Function::FunctionExpr.getArgs/0#dispred#85f47c51/2@26e76bfu
[2025-06-03 11:00:24] (0s)  >>> Created relation Function::FunctionExpr.getArgs/0#dispred#85f47c51/2@26e76bfu with 1 rows and digest 6733bcke3ur7sjg22vdd6c0tl03.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Function::FunctionExpr.getASubExpression/0#dispred#fa96f232/2@81dc0ev7
[2025-06-03 11:00:24] (0s)  >>> Created relation Function::FunctionExpr.getASubExpression/0#dispred#fa96f232/2@81dc0ev7 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::AssignExpr_.getTarget/0#dispred#56e0edd1/2@3d0138b8
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::AssignExpr_.getTarget/0#dispred#56e0edd1/2@3d0138b8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::AssignExpr_.getValue/0#dispred#dd4f3283/2@887262id
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::AssignExpr_.getValue/0#dispred#dd4f3283/2@887262id with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Yield_.getValue/0#dispred#b8040756/2@f05607r9
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Yield_.getValue/0#dispred#b8040756/2@f05607r9 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Comprehensions::SetComp.getASubExpression/0#dispred#a437fb1f/2@4cdad73k
[2025-06-03 11:00:24] (0s)  >>> Created relation Comprehensions::SetComp.getASubExpression/0#dispred#a437fb1f/2@4cdad73k with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Comprehensions::ListComp.getIterable/0#dispred#57173e62/2@16a42etf
[2025-06-03 11:00:24] (0s)  >>> Created relation Comprehensions::ListComp.getIterable/0#dispred#57173e62/2@16a42etf with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Comprehensions::GeneratorExp.getASubExpression/0#dispred#daf0ddfb/2@1416e42j
[2025-06-03 11:00:24] (0s)  >>> Created relation Comprehensions::GeneratorExp.getASubExpression/0#dispred#daf0ddfb/2@1416e42j with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Comprehensions::DictComp.getASubExpression/0#dispred#bda1a79a/2@bb81efm9
[2025-06-03 11:00:24] (0s)  >>> Created relation Comprehensions::DictComp.getASubExpression/0#dispred#bda1a79a/2@bb81efm9 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Attribute.getObject/0#dispred#c761f38d/2@d92a98ne
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Attribute.getObject/0#dispred#c761f38d/2@d92a98ne with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Flow::AttrNode.getObject/0#dispred#cba29223/2@1fce0566 is empty, due to Exprs::Attribute.getObject/0#dispred#c761f38d/2@d92a98ne.
[2025-06-03 11:00:24] (0s) Inferred that project#Flow::AttrNode.getObject/0#dispred#cba29223/1@dc476aoa is empty, due to Flow::AttrNode.getObject/0#dispred#cba29223/2@1fce0566.
[2025-06-03 11:00:24] (0s) Inferred that Flow::AttrNode.getObject/0#dispred#cba29223_10#join_rhs/2@88b0d4l6 is empty, due to Flow::AttrNode.getObject/0#dispred#cba29223/2@1fce0566.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::attribute_deletion_refinement/3#7de1bd7a/3@855894tv is empty, due to Flow::AttrNode.getObject/0#dispred#cba29223/2@1fce0566.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9/3@b3077aai is empty, due to Flow::AttrNode.getObject/0#dispred#cba29223/2@1fce0566.
[2025-06-03 11:00:24] (0s) Inferred that Flow::ControlFlowNode.isStore/0#dispred#bd620b20#b/1@45b5bdir is empty, due to project#Flow::AttrNode.getObject/0#dispred#cba29223/1@dc476aoa.
[2025-06-03 11:00:24] (0s) Inferred that _Definitions::ModuleVariable#27049d81_Flow::AttrNode.getObject/0#dispred#cba29223_10#join_rhs_Flow::__#shared/2@c2b478lv is empty, due to Flow::AttrNode.getObject/0#dispred#cba29223_10#join_rhs/2@88b0d4l6.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::attribute_deletion_refinement/3#7de1bd7a/3@bc7edf3e is empty, due to SsaDefinitions::SsaSource::attribute_deletion_refinement/3#7de1bd7a/3@855894tv.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9_02#antijoin_rhs/2@086037ue is empty, due to SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9/3@b3077aai.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9/3@ec7d91se is empty, due to SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9/3@b3077aai.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::attribute_assignment_refinement/3#5f777592/3@b852f33i is empty, due to Flow::ControlFlowNode.isStore/0#dispred#bd620b20#b/1@45b5bdir.
[2025-06-03 11:00:24] (0s) Inferred that _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Variables::Variable.getScope/0#dispred#e3b1c704____#antijoin_rhs#2/2@cf55e9jv is empty, due to _Definitions::ModuleVariable#27049d81_Flow::AttrNode.getObject/0#dispred#cba29223_10#join_rhs_Flow::__#shared/2@c2b478lv.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::attribute_assignment_refinement/3#5f777592/3@f4c9688s is empty, due to SsaDefinitions::SsaSource::attribute_assignment_refinement/3#5f777592/3@b852f33i.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::List_.getElts/0#dispred#697e305c/2@fc9c7br4
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::List_.getElts/0#dispred#697e305c/2@fc9c7br4 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::List_.getAnElt/0#dispred#fd62fa74/2@939b1dej is empty, due to AstGenerated::List_.getElts/0#dispred#697e305c/2@fc9c7br4.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::List_.getElt/1#dispred#a189dc04/3@c356fa2h is empty, due to AstGenerated::List_.getElts/0#dispred#697e305c/2@fc9c7br4.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::List_.getAnElt/0#dispred#fd62fa74_10#join_rhs/2@c8394963 is empty, due to AstGenerated::List_.getAnElt/0#dispred#fd62fa74/2@939b1dej.
[2025-06-03 11:00:24] (0s) Inferred that project#AstGenerated::List_.getElt/1#dispred#a189dc04/1@768cc0sg is empty, due to AstGenerated::List_.getElt/1#dispred#a189dc04/3@c356fa2h.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::List_.getElt/1#dispred#a189dc04_201#join_rhs/3@20bb3drf is empty, due to AstGenerated::List_.getElt/1#dispred#a189dc04/3@c356fa2h.
[2025-06-03 11:00:24] (0s) Inferred that AstGenerated::List_.getElt/1#dispred#a189dc04_120#join_rhs/3@5cb75bps is empty, due to AstGenerated::List_.getElt/1#dispred#a189dc04/3@c356fa2h.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Call_.getAPositionalArg/0#dispred#b66be902/2@1be1d3hs
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Call_.getAPositionalArg/0#dispred#b66be902/2@1be1d3hs with 3 rows and digest 7cbcb472hjaoodnu2eha038npo8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Expr.getASubExpression/0#dispred#443395f9/2@9d4f8cs5
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Expr.getASubExpression/0#dispred#443395f9/2@9d4f8cs5 with 6 rows and digest 3b1a70p2qa5ncpsrbaiui5grt0b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#17/1@1879fd7t
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#17/1@1879fd7t with 2 rows and digest ccfcdcvatefhvouinbofre7d2f3.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#1/1@9af1048t
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#1/1@9af1048t with 3 rows and digest f7ffc3dgup4ot5mg6chbkoid1k5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Expr_not_Exprs::Call_Class::ClassExpr_Exprs::Dict_Comprehensions::DictComp_Function::FunctionExpr_Comprehensions::GeneratorExp_Function::Lambda_Comprehensions::ListComp_Comprehensions::SetComp_Exprs::StringLiteral#3ae9c825/1@cfe2cct2
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Expr_not_Exprs::Call_Class::ClassExpr_Exprs::Dict_Comprehensions::DictComp_Function::FunctionExpr_Comprehensions::GeneratorExp_Function::Lambda_Comprehensions::ListComp_Comprehensions::SetComp_Exprs::StringLiteral#3ae9c825/1@cfe2cct2 with 6 rows and digest da2dc5oh0hf1t6vn7v3f0ashqk0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Assign_.getATarget/0#dispred#8e860934/2@66f4634i
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Assign_.getATarget/0#dispred#8e860934/2@66f4634i with 2 rows and digest 7a33f7d2n41rfnlrvcv1u3182e7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::AnnAssign_.getTarget/0#dispred#ac7debc8/2@f373f3nh
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::AnnAssign_.getTarget/0#dispred#ac7debc8/2@f373f3nh with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::AnnAssign_.getValue/0#dispred#436806a5/2@62c0b7r6
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::AnnAssign_.getValue/0#dispred#436806a5/2@62c0b7r6 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::With_.getOptionalVars/0#dispred#ed12867c/2@ded75ebf
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::With_.getOptionalVars/0#dispred#ed12867c/2@ded75ebf with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::with_definition/2#e1888ceb/2@50ae490u is empty, due to AstGenerated::With_.getOptionalVars/0#dispred#ed12867c/2@ded75ebf.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::with_definition/2#e1888ceb/2@e0bd2902 is empty, due to SsaDefinitions::SsaSource::with_definition/2#e1888ceb/2@50ae490u.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::For_.getTarget/0#dispred#740b9eed/2@119c7901
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::For_.getTarget/0#dispred#740b9eed/2@119c7901 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstExtended::AstNode.getAChildNode/0#dispred#a130356d/2@e22edc72
[2025-06-03 11:00:24] (0s)  >>> Created relation AstExtended::AstNode.getAChildNode/0#dispred#a130356d/2@e22edc72 with 17 rows and digest 438bcdpkfrs7vbb7843f48av692.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstExtended::AstNode.getParentNode/0#dispred#c159e3ee/2@decd0bj1
[2025-06-03 11:00:24] (0s)  >>> Created relation AstExtended::AstNode.getParentNode/0#dispred#c159e3ee/2@decd0bj1 with 17 rows and digest d0a6b1mcqk4k410991cousjues8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstExtended::AstNode.getScope/0#dispred#9b09b826/2@e6c71f9s
[2025-06-03 11:00:24] (0s)  >>> Created relation AstExtended::AstNode.getScope/0#dispred#9b09b826/2@e6c71f9s with 17 rows and digest 573f3d91dsj0repbc8fcc5jg3k2.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _AstGenerated::Scope_#cc08b056_py_flow_bb_node#antijoin_rhs/2@ef4d35dd
[2025-06-03 11:00:24] (0s)  >>> Created relation _AstGenerated::Scope_#cc08b056_py_flow_bb_node#antijoin_rhs/2@ef4d35dd with 4 rows and digest 10c4a7g42m5u221o4f7fevc5u32.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::ControlFlowNode.getScope/0#dispred#b061daac/2@0ac6c94f
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::ControlFlowNode.getScope/0#dispred#b061daac/2@0ac6c94f with 16 rows and digest a76bd1d1ul5231k557a93t6qfj1.
[2025-06-03 11:00:24] (0s)  >>> Created relation variable/3@5e1d40kq with 8 rows and digest 8e08a42fq04rlcpda8qpkg0d099.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_variables/2@62cd450b with 6 rows and digest 34e2d0316hc13sdo160d22g8ulc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Variables::Variable.getId/0#dispred#33796d42/2@7155f3jh
[2025-06-03 11:00:24] (0s)  >>> Created relation Variables::Variable.getId/0#dispred#33796d42/2@7155f3jh with 7 rows and digest b4cc028k2noau1m70p0habq7r5c.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Name_.getVariable/0#dispred#d8133d45/2@e04d1f44
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Name_.getVariable/0#dispred#d8133d45/2@e04d1f44 with 6 rows and digest 944471c7ki2s0bigbvr3cee4fif.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Name.getId/0#dispred#4fa5460e/2@431259f7
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Name.getId/0#dispred#4fa5460e/2@431259f7 with 6 rows and digest a81497kl53b34ebh2e8r9a5non4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Variables::Variable.getId/0#dispred#33796d42_10#join_rhs/2@e85cdfnl
[2025-06-03 11:00:24] (0s)  >>> Created relation Variables::Variable.getId/0#dispred#33796d42_10#join_rhs/2@e85cdfnl with 7 rows and digest 256e9e0c58dal7efj0d41hg8kh0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::name_consts/2#5b93d161/2@26923fa8
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::name_consts/2#5b93d161/2@26923fa8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Exprs::name_consts/2#5b93d161_10#join_rhs/2@ec1e5cqq is empty, due to Exprs::name_consts/2#5b93d161/2@26923fa8.
[2025-06-03 11:00:24] (0s) Inferred that Exprs::NameConstant#42a68058/1@066eb7n4 is empty, due to Exprs::name_consts/2#5b93d161_10#join_rhs/2@ec1e5cqq.
[2025-06-03 11:00:24] (0s) Inferred that _Exprs::Name.uses/1#dispred#b73448f6_10#join_rhs_Exprs::NameConstant#42a68058_variable#antijoin_rhs/1@8a6161rb is empty, due to Exprs::NameConstant#42a68058/1@066eb7n4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Name_not_Exprs::NameConstant#8eac0b8c/1@b35272ig
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Name_not_Exprs::NameConstant#8eac0b8c/1@b35272ig with 6 rows and digest da2dc5oh0hf1t6vn7v3f0ashqk0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.SsaSourceVariable#35012962/1@a502d3nu
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.SsaSourceVariable#35012962/1@a502d3nu with 8 rows and digest a79750fd5019u4cq5ta8t4un892.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#py_flow_bb_node/1@fad6886l
[2025-06-03 11:00:24] (0s)  >>> Created relation project#py_flow_bb_node/1@fad6886l with 2 rows and digest 9931d4ghf0ecfpcir9onhh0c9cf.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::BasicBlock.getNode/1#dispred#4a129537/3@dcd623tr
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::BasicBlock.getNode/1#dispred#4a129537/3@dcd623tr with 16 rows and digest 220e30cch2of38aqsll1u14a87e.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52/2@3d8c107i
[2025-06-03 11:00:24] (0s)  >>> Created relation Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52/2@3d8c107i with 1 rows and digest 57035btodt35ck120tnd3akqgmc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate variable_02#join_rhs/2@f27b5amb
[2025-06-03 11:00:24] (0s)  >>> Created relation variable_02#join_rhs/2@f27b5amb with 8 rows and digest 0184dcg7psqci8elvj9fl1otiud.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SpecialSsaSourceVariable#59f2e69e/1@0fd25fha
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SpecialSsaSourceVariable#59f2e69e/1@0fd25fha with 1 rows and digest 2e0b55d169b4tt2o7gdthbp4mk6.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate boundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:AstGenerated::Scope_#cc08b056/2@b3c2bcsh = HOP boundedFastTC(1,2)
[2025-06-03 11:00:24] (0s)  >>> Relation boundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:AstGenerated::Scope_#cc08b056: 1 rows using 0 MB
[2025-06-03 11:00:24] (0s)  >>> Created relation boundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:AstGenerated::Scope_#cc08b056/2@b3c2bcsh with 1 rows and digest 57035btodt35ck120tnd3akqgmc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _AstGenerated::Scope_#cc08b056_boundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:AstGe__#antijoin_rhs/1@6c8a07la
[2025-06-03 11:00:24] (0s)  >>> Created relation _AstGenerated::Scope_#cc08b056_boundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:AstGe__#antijoin_rhs/1@6c8a07la with 1 rows and digest 8107ec3gq839vplsii8ct5d02h8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate ImportTime::ImportTimeScope#0b3564f8/1@75de987k
[2025-06-03 11:00:24] (0s)  >>> Created relation ImportTime::ImportTimeScope#0b3564f8/1@75de987k with 1 rows and digest d7f3bed32abr7eno8gm5o6iork4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Variables::Variable.getScope/0#dispred#e3b1c704/2@daec9568
[2025-06-03 11:00:24] (0s)  >>> Created relation Variables::Variable.getScope/0#dispred#e3b1c704/2@daec9568 with 7 rows and digest bae6d4iuk1sve2kb8f8fqh1te55.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Variables::Variable.getScope/0#dispred#e3b1c704_10#join_rhs/2@8d77a92p
[2025-06-03 11:00:24] (0s)  >>> Created relation Variables::Variable.getScope/0#dispred#e3b1c704_10#join_rhs/2@8d77a92p with 7 rows and digest 7a31fef6c3lo4iuc55k78ri3usd.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Variables::LocalVariable#4f360e4a/1@a1d768r7
[2025-06-03 11:00:24] (0s)  >>> Created relation Variables::LocalVariable#4f360e4a/1@a1d768r7 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::NonLocalVariable#8768a412/1@9d3a53sp is empty, due to Variables::LocalVariable#4f360e4a/1@a1d768r7.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::FunctionLocalVariable#df373784/1@013c44lh is empty, due to Variables::LocalVariable#4f360e4a/1@a1d768r7.
[2025-06-03 11:00:24] (0s) Inferred that Variables::FastLocalVariable#8c848f80/1@3e18b5od is empty, due to Variables::LocalVariable#4f360e4a/1@a1d768r7.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20/2@b0b438rr is empty, due to Definitions::NonLocalVariable#8768a412/1@9d3a53sp.
[2025-06-03 11:00:24] (0s) Inferred that _Exprs::Name.uses/1#dispred#b73448f6_10#join_rhs_Flow::NameNode#b2c17c8a_Variables::FastLocalVariabl__#shared/2@f4aaf5is is empty, due to Variables::FastLocalVariable#8c848f80/1@3e18b5od.
[2025-06-03 11:00:24] (0s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#4#3/1@b45264ar is empty, due to Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20/2@b0b438rr.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20_10#join_rhs/2@01f3424g is empty, due to Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20/2@b0b438rr.
[2025-06-03 11:00:24] (0s) Inferred that _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Variables::Variable.getScope/0#dispred#e3b1c704____#antijoin_rhs/2@0c00a1la is empty, due to _Exprs::Name.uses/1#dispred#b73448f6_10#join_rhs_Flow::NameNode#b2c17c8a_Variables::FastLocalVariabl__#shared/2@f4aaf5is.
[2025-06-03 11:00:24] (0s) Inferred that Flow::Scopes::non_local/1#5e3b85d8/1@4d5d2cs2 is empty, due to _Exprs::Name.uses/1#dispred#b73448f6_10#join_rhs_Flow::NameNode#b2c17c8a_Variables::FastLocalVariabl__#shared/2@f4aaf5is.
[2025-06-03 11:00:24] (0s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#bounded#2/2@f91f6d2l is empty, due to #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#4#3/1@b45264ar.
[2025-06-03 11:00:24] (0s) Inferred that _Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20_10#join_rhs__Flow::CallNode#c5d1dc__#shared/2@e160d2qb is empty, due to Definitions::NonLocalVariable.scope_as_local_variable/0#f6dddc20_10#join_rhs/2@01f3424g.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Variables::GlobalVariable#28045801/1@3c116fdi
[2025-06-03 11:00:24] (0s)  >>> Created relation Variables::GlobalVariable#28045801/1@3c116fdi with 7 rows and digest c6969ed16ldpg5aqgckcv4mfn8a.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_expr_contexts/3@3945b8md with 6 rows and digest 85ff36piablboc45n81sldd82re.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_expr_contexts_12#join_rhs/2@145e04os
[2025-06-03 11:00:24] (0s)  >>> Created relation py_expr_contexts_12#join_rhs/2@145e04os with 6 rows and digest 85b1a94e9ijink1nos5peocidgb.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Name.isDefinition/0#dispred#35fcd409/1@6959a59b
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Name.isDefinition/0#dispred#35fcd409/1@6959a59b with 2 rows and digest 3bdbc2s8ldve1j9kon9ildl9pd4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Name.defines/1#dispred#729b8d19/2@946b571l
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Name.defines/1#dispred#729b8d19/2@946b571l with 2 rows and digest 07ca6a93uq6qg3a8okjdmkiqn2b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#Exprs::Name.defines/1#dispred#729b8d19/1@52051fpj
[2025-06-03 11:00:24] (0s)  >>> Created relation project#Exprs::Name.defines/1#dispred#729b8d19/1@52051fpj with 2 rows and digest 8f8e54onrf2uc1je6hkkvtpq7ge.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Stmts::Stmt.getScope/0#dispred#1debab62_Variables::Variable.getScope/0#dispred#e3b1c704_10#join_rhs__#shared/1@d740d35m
[2025-06-03 11:00:24] (0s)  >>> Created relation _Stmts::Stmt.getScope/0#dispred#1debab62_Variables::Variable.getScope/0#dispred#e3b1c704_10#join_rhs__#shared/1@d740d35m with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Definitions::SsaSourceVariable.SsaSourceVariable#35012962_Variables::GlobalVariable#28045801#shared/1@82c018pj
[2025-06-03 11:00:24] (0s)  >>> Created relation _Definitions::SsaSourceVariable.SsaSourceVariable#35012962_Variables::GlobalVariable#28045801#shared/1@82c018pj with 7 rows and digest c6969ed16ldpg5aqgckcv4mfn8a.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::ModuleVariable#27049d81/1@243bffti
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::ModuleVariable#27049d81/1@243bffti with 4 rows and digest 3d62baf4820i95bh5tik074d29a.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Name.uses/1#dispred#b73448f6/2@4b69b9i6
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Name.uses/1#dispred#b73448f6/2@4b69b9i6 with 4 rows and digest 4c9b09jqv6indqv6fbku1pm2u2b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs/1@eb95435e
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs/1@eb95435e with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Flow::AttrNode#0f9cc644/1@3a2488v2 is empty, due to _py_exprs_10#join_rhs#antijoin_rhs/1@eb95435e.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3/1@471cf87v
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3/1@471cf87v with 4 rows and digest 588dedkgu31rqrh8ga8mcp51nl9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::NameNode.defines/1#dispred#000af72d/2@b5969db9
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::NameNode.defines/1#dispred#000af72d/2@b5969db9 with 2 rows and digest d30de6fv6tlfequs463dic0675f.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Definitions::ModuleVariable#27049d81_Flow::NameNode.defines/1#dispred#000af72d#shared/2@b4b667at
[2025-06-03 11:00:24] (0s)  >>> Created relation _Definitions::ModuleVariable#27049d81_Flow::NameNode.defines/1#dispred#000af72d#shared/2@b4b667at with 2 rows and digest aaf111ojgno6s22av04sl40ie70.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Variables::Variable.getScope/0#dispred#e3b1c704____#antijoin_rhs#1/2@24c29c0i
[2025-06-03 11:00:24] (0s)  >>> Created relation _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Variables::Variable.getScope/0#dispred#e3b1c704____#antijoin_rhs#1/2@24c29c0i with 2 rows and digest aaf111ojgno6s22av04sl40ie70.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::variable_or_attribute_defined_out_of_scope/1#fc011a99#b/1@61a058gi
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::variable_or_attribute_defined_out_of_scope/1#fc011a99#b/1@61a058gi with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::EscapingGlobalVariable#8f7a8d1d/1@a020c99i is empty, due to Definitions::variable_or_attribute_defined_out_of_scope/1#fc011a99#b/1@61a058gi.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::EscapingGlobalVariable.scope_as_global_variable/0#2cfc0b40/2@d32219bj is empty, due to Definitions::EscapingGlobalVariable#8f7a8d1d/1@a020c99i.
[2025-06-03 11:00:24] (0s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#3#3/1@341d5031 is empty, due to Definitions::EscapingGlobalVariable.scope_as_global_variable/0#2cfc0b40/2@d32219bj.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::EscapingGlobalVariable.scope_as_global_variable/0#2cfc0b40_10#join_rhs/2@908ae7qj is empty, due to Definitions::EscapingGlobalVariable.scope_as_global_variable/0#2cfc0b40/2@d32219bj.
[2025-06-03 11:00:24] (0s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#bounded/2@a4cbaaeb is empty, due to #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#3#3/1@341d5031.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::EscapingGlobalVariable.innerScope/0#dispred#91670a16/2@59ab707m is empty, due to Definitions::EscapingGlobalVariable.scope_as_global_variable/0#2cfc0b40_10#join_rhs/2@908ae7qj.
[2025-06-03 11:00:24] (0s) Inferred that __Flow::CallNode#c5d1dc47_Flow::ControlFlowNode.getScope/0#dispred#b061daac#shared_doublyBoundedFast__#shared/2@6c0f1cul is empty, due to doublyBoundedFastTC:Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52:_#Scope::Scope.getScope/0#dispred#055e5112Plus#sourceBound#2#3#higher_order_body:#Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#3#3/2@a4cbaaeb.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::CallNode#c5d1dc47/1@5509a280
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::CallNode#c5d1dc47/1@5509a280 with 3 rows and digest 44c563j0lccveb28rkf3ejtq18f.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_scope_flow/3@3a9c65tj with 7 rows and digest 5bd99egp1gtr1tmkmlpt8ce1qha.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_scope_flow_201#join_rhs/3@f0a7617n
[2025-06-03 11:00:24] (0s)  >>> Created relation py_scope_flow_201#join_rhs/3@f0a7617n with 7 rows and digest d35c1d9qu9cvvn55aios1p2chg5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Scope::Scope.getANormalExit/0#dispred#1288c0a8/2@95b0a3la
[2025-06-03 11:00:24] (0s)  >>> Created relation Scope::Scope.getANormalExit/0#dispred#1288c0a8/2@95b0a3la with 2 rows and digest 897d1d6d5pov661lnh61vqkpp1b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Variables::Variable.getId/0#dispred#33796d42__Definitions::SsaSourceVariable.SsaSourceVariable#3501__#shared/1@477f3733
[2025-06-03 11:00:24] (0s)  >>> Created relation _Variables::Variable.getId/0#dispred#33796d42__Definitions::SsaSourceVariable.SsaSourceVariable#3501__#shared/1@477f3733 with 3 rows and digest 336c10qd84ies5acli842topbvf.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs/2@d7176491
[2025-06-03 11:00:24] (0s)  >>> Created relation Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs/2@d7176491 with 4 rows and digest 7227ad75dtomcr2570jvn7986u0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs_Variables::Variable.getScope/0#dispred#e3b1c704__#antijoin_rhs/1@ea1035ad
[2025-06-03 11:00:24] (0s)  >>> Created relation _Stmts::Stmt.getScope/0#dispred#1debab62_10#join_rhs_Variables::Variable.getScope/0#dispred#e3b1c704__#antijoin_rhs/1@ea1035ad with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable#e2234121/1@67ab17fk
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable#e2234121/1@67ab17fk with 8 rows and digest a79750fd5019u4cq5ta8t4un892.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getScope/0#dispred#eef6cd59/2@220968be
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.getScope/0#dispred#eef6cd59/2@220968be with 8 rows and digest f39177kh4pc352nooe721opokae.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getScope/0#dispred#eef6cd59_10#join_rhs/2@f536c8ii
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.getScope/0#dispred#eef6cd59_10#join_rhs/2@f536c8ii with 8 rows and digest 995142lcvljlt60qqdfbfkfmbr6.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::ControlFlowNode.getScope/0#dispred#b061daac_10#join_rhs/2@b78f7dap
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::ControlFlowNode.getScope/0#dispred#b061daac_10#join_rhs/2@b78f7dap with 16 rows and digest 823a8deet35deevhuk72kv0c4t0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::implicit_definition/1#10d63d77/1@19a9f4tf
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::implicit_definition/1#10d63d77/1@19a9f4tf with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_scope_flow_120#join_rhs/3@c0dfe07u
[2025-06-03 11:00:24] (0s)  >>> Created relation py_scope_flow_120#join_rhs/3@c0dfe07u with 7 rows and digest 6bf4ccciuppn130bqoi29b0eajf.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::ModuleVariable.scope_as_global_variable/0#51a92cfd/2@249fd6l2
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::ModuleVariable.scope_as_global_variable/0#51a92cfd/2@249fd6l2 with 4 rows and digest 02b0bffsmsfnmj4uch0bs38eula.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::ModuleVariable.scope_as_global_variable/0#51a92cfd_10#join_rhs/2@31f16bbr
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::ModuleVariable.scope_as_global_variable/0#51a92cfd_10#join_rhs/2@31f16bbr with 4 rows and digest 88c8a6vff83p4rscq7617e49mc6.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Flow::CallNode#c5d1dc47_Flow::ControlFlowNode.getScope/0#dispred#b061daac#shared/2@f358463l
[2025-06-03 11:00:24] (0s)  >>> Created relation _Flow::CallNode#c5d1dc47_Flow::ControlFlowNode.getScope/0#dispred#b061daac#shared/2@f358463l with 3 rows and digest 7e87015q89v6dn730373atrtiv6.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#3/1@948b37j5
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#3/1@948b37j5 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Flow::ImportMemberNode#7f288801/1@c3fd6bqv is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#3/1@948b37j5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::ModuleVariable.global_variable_callnode/0#0a973b2a/2@19ac6bth
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::ModuleVariable.global_variable_callnode/0#0a973b2a/2@19ac6bth with 8 rows and digest 07674cfbpvboifh7gt3rmb5hqff.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::ModuleVariable_not_Definitions::EscapingGlobalVariable#1c8befd9/1@7fc1b5a3
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::ModuleVariable_not_Definitions::EscapingGlobalVariable#1c8befd9/1@7fc1b5a3 with 4 rows and digest 3d62baf4820i95bh5tik074d29a.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::NameNode.deletes/1#dispred#2c693720/2@82a0a0b4
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::NameNode.deletes/1#dispred#2c693720/2@82a0a0b4 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::deletion_definition/2#e74a17e3/2@4dc9d10j is empty, due to Flow::NameNode.deletes/1#dispred#2c693720/2@82a0a0b4.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::deletion_definition/2#e74a17e3/2@a934fde8 is empty, due to SsaDefinitions::SsaSource::deletion_definition/2#e74a17e3/2@4dc9d10j.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_flow_bb_node_1023#join_rhs/4@a7f552tj
[2025-06-03 11:00:24] (0s)  >>> Created relation py_flow_bb_node_1023#join_rhs/4@a7f552tj with 16 rows and digest 0bbd57ppluhbgfk0kaa99psko8a.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstExtended::AstNode#8b357598/1@8ce7eav4
[2025-06-03 11:00:24] (0s)  >>> Created relation AstExtended::AstNode#8b357598/1@8ce7eav4 with 18 rows and digest 1e53ear9ouci8731a4q61it1op5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#6/1@708131a3
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#6/1@708131a3 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#2/1@5223c9t1
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#2/1@5223c9t1 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstExtended::AstNode_not_Exprs::Attribute_Exprs::Call_Exprs::IfExp_Import::ImportMember_Exprs::Name_Exprs::NameConstant_Exprs::PlaceHolder_Exprs::Subscript#9990a81b/1@bdc6fc0u
[2025-06-03 11:00:24] (0s)  >>> Created relation AstExtended::AstNode_not_Exprs::Attribute_Exprs::Call_Exprs::IfExp_Import::ImportMember_Exprs::Name_Exprs::NameConstant_Exprs::PlaceHolder_Exprs::Subscript#9990a81b/1@bdc6fc0u with 9 rows and digest c6779cpgpcqlaop7a2m5isekn77.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Name.getAFlowNode/0#d7472f1f/2@c5cefarl
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Name.getAFlowNode/0#d7472f1f/2@c5cefarl with 6 rows and digest c2b359jim28098ipkbcnc7mf2k3.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee/2@1996e76t
[2025-06-03 11:00:24] (0s)  >>> Created relation AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee/2@1996e76t with 16 rows and digest fb8e7apompi19oo74usm5ktdcn7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Expr.getScope/0#dispred#71058b3b_10#join_rhs/2@244cddr1
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Expr.getScope/0#dispred#71058b3b_10#join_rhs/2@244cddr1 with 12 rows and digest 71fb01knfhlid8po05hhtsusqp3.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::list_or_tuple_nested_element/1#8389faf7/2@i1#4d325enu (iteration 1)
[2025-06-03 11:00:24] (0s) Empty delta for Flow::list_or_tuple_nested_element/1#8389faf7_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::list_or_tuple_nested_element/1#8389faf7/2@4d325enu with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::DefinitionNode#e8809c3b/1@8e449cle
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::DefinitionNode#e8809c3b/1@8e449cle with 2 rows and digest c9d174819odgmg5hji15hokpml9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate m#Flow::DefinitionNode.getValue/0#dispred#e8c0c58e#bf/1@e65adeet
[2025-06-03 11:00:24] (0s)  >>> Created relation m#Flow::DefinitionNode.getValue/0#dispred#e8c0c58e#bf/1@e65adeet with 2 rows and digest c9d174819odgmg5hji15hokpml9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate m#Flow::assigned_value/1#97af2d4b#bf/1@6303b6t9
[2025-06-03 11:00:24] (0s)  >>> Created relation m#Flow::assigned_value/1#97af2d4b#bf/1@6303b6t9 with 2 rows and digest 3bdbc2s8ldve1j9kon9ildl9pd4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _AstGenerated::Assign_.getATarget/0#dispred#8e860934_AstGenerated::Assign_.getValue/0#dispred#53d00b__#shared/2@6a6f9cjr
[2025-06-03 11:00:24] (0s)  >>> Created relation _AstGenerated::Assign_.getATarget/0#dispred#8e860934_AstGenerated::Assign_.getValue/0#dispred#53d00b__#shared/2@6a6f9cjr with 2 rows and digest 7969e6fndkcm0h9b01m6ul8ebud.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Assign_.getATarget/0#dispred#8e860934_10#join_rhs/2@3f9060gb
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Assign_.getATarget/0#dispred#8e860934_10#join_rhs/2@3f9060gb with 2 rows and digest c85bb7docp46j6oflfm490877g1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaDefinitions::SsaSource::parameter_definition/2#40b2fede/2@80e70ess
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaDefinitions::SsaSource::parameter_definition/2#40b2fede/2@80e70ess with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::parameter_definition/2#40b2fede/2@9409b5ba is empty, due to SsaDefinitions::SsaSource::parameter_definition/2#40b2fede/2@80e70ess.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate CachedStages::Stages::AST::ref/0#e52d92b5/0@72b8f244
[2025-06-03 11:00:24] (0s)  >>> Created relation CachedStages::Stages::AST::ref/0#e52d92b5/0@72b8f244 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate CachedStages::Stages::AST::ref/0#e52d92b5  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_CachedStages::Stages::AST::ref/0#e52d92b5/0@c876115v with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_CachedStages::Stages::AST::backref/0#dddb3000/0@a80d27a0 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate Flow::DefinitionNode#e8809c3b  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_Flow::DefinitionNode#e8809c3b/1@a95b912i with 2 rows and digest c9d174819odgmg5hji15hokpml9.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee/2@89ae53vo with 16 rows and digest fb8e7apompi19oo74usm5ktdcn7.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate Flow::ControlFlowNode.getScope/0#dispred#b061daac  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_Flow::ControlFlowNode.getScope/0#dispred#b061daac/2@afe6a2k8 with 16 rows and digest a76bd1d1ul5231k557a93t6qfj1.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate AstExtended::AstNode.getAChildNode/0#dispred#a130356d  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_AstExtended::AstNode.getAChildNode/0#dispred#a130356d/2@4a4f6brg with 17 rows and digest 438bcdpkfrs7vbb7843f48av692.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate AstExtended::AstNode.getParentNode/0#dispred#c159e3ee  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_AstExtended::AstNode.getParentNode/0#dispred#c159e3ee/2@699751d5 with 17 rows and digest d0a6b1mcqk4k410991cousjues8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_scope_flow_02#join_rhs/2@687f8fu4
[2025-06-03 11:00:24] (0s)  >>> Created relation py_scope_flow_02#join_rhs/2@687f8fu4 with 7 rows and digest 64330ei8pi2bu5egijqk6m5nghd.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#Scope::Scope.getANormalExit/0#dispred#1288c0a8/1@78a541r8
[2025-06-03 11:00:24] (0s)  >>> Created relation project#Scope::Scope.getANormalExit/0#dispred#1288c0a8/1@78a541r8 with 2 rows and digest 28c516heqo4qbcbhj0lpamesmbd.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _project#Scope::Scope.getANormalExit/0#dispred#1288c0a8_py_flow_bb_node_py_scope_flow_02#join_rhs#antijoin_rhs/2@67e3d421
[2025-06-03 11:00:24] (0s)  >>> Created relation _project#Scope::Scope.getANormalExit/0#dispred#1288c0a8_py_flow_bb_node_py_scope_flow_02#join_rhs#antijoin_rhs/2@67e3d421 with 4 rows and digest 10c4a7g42m5u221o4f7fevc5u32.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate variable_201#join_rhs/3@0467c2qr
[2025-06-03 11:00:24] (0s)  >>> Created relation variable_201#join_rhs/3@0467c2qr with 8 rows and digest 025b4eoqbmf7nrrdjhjniahhk90.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Name.getId/0#dispred#4fa5460e#fb/2@b3e4919s
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Name.getId/0#dispred#4fa5460e#fb/2@b3e4919s with 1 rows and digest 9f7bdelb0l6te42njaelvj4bor0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs/2@b9a83bie
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs/2@b9a83bie with 1 rows and digest 2c26fd2r3g45f88aheov2rdrtn2.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_exprs_032#join_rhs/3@dc21c4iv
[2025-06-03 11:00:24] (0s)  >>> Created relation py_exprs_032#join_rhs/3@dc21c4iv with 12 rows and digest 7cb688ofvfj0s6gr1sqn0hp9era.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared/2@a58601vs
[2025-06-03 11:00:24] (0s)  >>> Created relation _Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared/2@a58601vs with 1 rows and digest 84bc197btbv6dt4bi4v2gblb6uc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::Module.getFile/0#dispred#53eb9b1b/2@648ff7sb
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::Module.getFile/0#dispred#53eb9b1b/2@648ff7sb with 1 rows and digest 58d110scfk169u0vappqlnmo2c3.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::Module.getFile/0#dispred#53eb9b1b_0#antijoin_rhs/1@b54f01qv
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::Module.getFile/0#dispred#53eb9b1b_0#antijoin_rhs/1@b54f01qv with 1 rows and digest d7f3bed32abr7eno8gm5o6iork4.
[2025-06-03 11:00:24] (0s)  >>> Created relation locations_default/6@2b5cc3pr with 67 rows and digest 0bf8acc2k4nohltuqbmaa0n17f1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate locations_ast_102345#join_rhs/6@ba5e2d5g
[2025-06-03 11:00:24] (0s)  >>> Created relation locations_ast_102345#join_rhs/6@ba5e2d5g with 20 rows and digest 422c3cv5d4gsc1i7u0sdbkjaude.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::Location.hasLocationInfo/5#dispred#143bb906/6@5c85b9n0
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::Location.hasLocationInfo/5#dispred#143bb906/6@5c85b9n0 with 87 rows and digest cb80308vmtlk1vcm0s2jdff64s7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate __Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared_py_exprs#shared/1@7421accn
[2025-06-03 11:00:24] (0s)  >>> Created relation __Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared_py_exprs#shared/1@7421accn with 1 rows and digest 1af716dgmv6tisa7rfru9mdlg9b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#jo__#shared/2@1d8233bm
[2025-06-03 11:00:24] (0s)  >>> Created relation _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#jo__#shared/2@1d8233bm with 1 rows and digest d516afu12gdapk7i8g6dkd9dg0b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Expr_.getLocation/0#dispred#3637884c#bf/2@21ddc67t
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Expr_.getLocation/0#dispred#3637884c#bf/2@21ddc67t with 1 rows and digest 72f6d1e9f1imrjde2ul1c7qtqs8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _AstGenerated::Expr_.getLocation/0#dispred#3637884c#bf___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#__#shared/2@6fbb28fv
[2025-06-03 11:00:24] (0s)  >>> Created relation _AstGenerated::Expr_.getLocation/0#dispred#3637884c#bf___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#__#shared/2@6fbb28fv with 1 rows and digest d516afu12gdapk7i8g6dkd9dg0b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#Files::Location.hasLocationInfo/5#dispred#143bb906/1@2da765ju
[2025-06-03 11:00:24] (0s)  >>> Created relation project#Files::Location.hasLocationInfo/5#dispred#143bb906/1@2da765ju with 87 rows and digest acc395pkrek50ji5tug712sh94c.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate __AstExtended::AstNode.getLocation/0#dispred#6b4dcb62___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#j__#antijoin_rhs/2@f709a4ph
[2025-06-03 11:00:24] (0s)  >>> Created relation __AstExtended::AstNode.getLocation/0#dispred#6b4dcb62___Exprs::Name.getId/0#dispred#4fa5460e#fb_10#j__#antijoin_rhs/2@f709a4ph with 1 rows and digest 3df38b6njemhr9dvgsoet0h0jge.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate __Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared___AstExtended::As__#shared/2@861abc2t
[2025-06-03 11:00:24] (0s)  >>> Created relation __Exprs::Name.getId/0#dispred#4fa5460e#fb_10#join_rhs_py_exprs_032#join_rhs#shared___AstExtended::As__#shared/2@861abc2t with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Files::Location.hasLocationInfo/5#dispred#143bb906__AstExtended::AstNode.getLocation/0#dispred#6b4d__#shared/6@4b3f37ea
[2025-06-03 11:00:24] (0s)  >>> Created relation _Files::Location.hasLocationInfo/5#dispred#143bb906__AstExtended::AstNode.getLocation/0#dispred#6b4d__#shared/6@4b3f37ea with 1 rows and digest 50d88ehmm018iaobapnj7uv42p9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_120#join_rhs/3@a583e3e5
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_120#join_rhs/3@a583e3e5 with 16 rows and digest 4d67ecf70t8uk2mj49rjjdsu3h9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_20#join_rhs/2@896ce886
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985_20#join_rhs/2@896ce886 with 16 rows and digest 33c5019jt8qlhh5g96bf8av79he.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate m#Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb/1@e69a47jq
[2025-06-03 11:00:24] (0s)  >>> Created relation m#Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb/1@e69a47jq with 7 rows and digest 0a89976nkkto3dtf4mptuebhac0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb/2@f469e8en
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb/2@f469e8en with 2 rows and digest a5beff3t9hphdmpuv20hetibrq0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb_10#join_rhs/2@bc9afava
[2025-06-03 11:00:24] (0s)  >>> Created relation Files::Impl::Container.getAFile/0#dispred#9d20c6c3#fb_10#join_rhs/2@bc9afava with 2 rows and digest 80b125aoi8t7o475420guo7b2nd.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::isRegularPackage/2#c6677254/2@d08fb5sh
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::isRegularPackage/2#c6677254/2@d08fb5sh with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::isPackage/2#0f4cbd4a/2@76e0a6r5
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::isPackage/2#0f4cbd4a/2@76e0a6r5 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that _Module::isPackage/2#0f4cbd4a_containerparent_10#join_rhs#antijoin_rhs/2@2760b4b8 is empty, due to Module::isPackage/2#0f4cbd4a/2@76e0a6r5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::moduleNameFromFile/1#a01d5f51/2@i1#78e23656 (iteration 1)
[2025-06-03 11:00:24] (0s) Empty delta for Module::moduleNameFromFile/1#a01d5f51_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::moduleNameFromFile/1#a01d5f51/2@78e23656 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that cached_Module::moduleNameFromFile/1#a01d5f51/2@010e4f4r is empty, due to Module::moduleNameFromFile/1#a01d5f51/2@78e23656.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::Module.getName/0#dispred#45a481b3/2@f0a518q4
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::Module.getName/0#dispred#45a481b3/2@f0a518q4 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Module::Module.getName/0#dispred#45a481b3_10#join_rhs/2@751ef928 is empty, due to Module::Module.getName/0#dispred#45a481b3/2@f0a518q4.
[2025-06-03 11:00:24] (0s) Inferred that Module::Module.getPackageName/0#dispred#bb0c3872/2@0be635sq is empty, due to Module::Module.getName/0#dispred#45a481b3/2@f0a518q4.
[2025-06-03 11:00:24] (0s) Inferred that project#Module::Module.getName/0#dispred#45a481b3/1@ef10afqb is empty, due to Module::Module.getName/0#dispred#45a481b3/2@f0a518q4.
[2025-06-03 11:00:24] (0s) Inferred that project#Module::Module.getName/0#dispred#45a481b3#2/1@e54441pb is empty, due to Module::Module.getName/0#dispred#45a481b3/2@f0a518q4.
[2025-06-03 11:00:24] (0s) Inferred that Module::Module.getInitModule/0#dispred#450b78aa/2@948ab521 is empty, due to Module::Module.getName/0#dispred#45a481b3_10#join_rhs/2@751ef928.
[2025-06-03 11:00:24] (0s) Inferred that Module::Module.getPackage/0#dispred#0378d53e/2@b6130els is empty, due to Module::Module.getPackageName/0#dispred#bb0c3872/2@0be635sq.
[2025-06-03 11:00:24] (0s) Inferred that Import::ImportExpr.basePackageName/1#eaca2912/3@23cd72ks is empty, due to Module::Module.getPackageName/0#dispred#bb0c3872/2@0be635sq.
[2025-06-03 11:00:24] (0s) Inferred that _project#Module::Module.getName/0#dispred#45a481b3_py_cobjectnames_py_cobjecttypes_py_special_object__#antijoin_rhs/1@c4124aft is empty, due to project#Module::Module.getName/0#dispred#45a481b3/1@ef10afqb.
[2025-06-03 11:00:24] (0s) Inferred that project#Module::Module.getInitModule/0#dispred#450b78aa/1@be95a16s is empty, due to Module::Module.getInitModule/0#dispred#450b78aa/2@948ab521.
[2025-06-03 11:00:24] (0s) Inferred that Module::Module.getSubModule/1#dispred#e658b323/3@1c4040ub is empty, due to Module::Module.getPackage/0#dispred#0378d53e/2@b6130els.
[2025-06-03 11:00:24] (0s) Inferred that Base::import_from_dot_in_init/1#f6b55db3/1@45b9fe17 is empty, due to project#Module::Module.getInitModule/0#dispred#450b78aa/1@be95a16s.
[2025-06-03 11:00:24] (0s) Inferred that project#Module::Module.getSubModule/1#dispred#e658b323/1@4ae2c5tj is empty, due to Module::Module.getSubModule/1#dispred#e658b323/3@1c4040ub.
[2025-06-03 11:00:24] (0s) Inferred that project#Module::Module.getSubModule/1#dispred#e658b323#3/2@0a7227fj is empty, due to Module::Module.getSubModule/1#dispred#e658b323/3@1c4040ub.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::SsaSourceVariable.getName/0#dispred#6acf6d7c#fb/2@41ee66kv is empty, due to project#Module::Module.getSubModule/1#dispred#e658b323/1@4ae2c5tj.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::init_module_submodule_defn/2#64b1af7d/2@7b14fb10 is empty, due to project#Module::Module.getSubModule/1#dispred#e658b323#3/2@0a7227fj.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::SsaSourceVariable.getName/0#dispred#6acf6d7c#fb_10#join_rhs/2@f2a8f4se is empty, due to Definitions::SsaSourceVariable.getName/0#dispred#6acf6d7c#fb/2@41ee66kv.
[2025-06-03 11:00:24] (0s) Inferred that Module::Module.isPackageInit/0#dispred#56393e5c#b/1@fb3f2580 is empty, due to Definitions::SsaSourceVariable.getName/0#dispred#6acf6d7c#fb_10#join_rhs/2@f2a8f4se.
[2025-06-03 11:00:24] (0s) Inferred that Essa::ImplicitSubModuleDefinition#5d33e2dd/1@f1a14cu2 is empty, due to SsaDefinitions::SsaSource::init_module_submodule_defn/2#64b1af7d/2@7b14fb10.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::init_module_submodule_defn/2#64b1af7d/2@2761bd9c is empty, due to SsaDefinitions::SsaSource::init_module_submodule_defn/2#64b1af7d/2@7b14fb10.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Module::Module.toString/0#dispred#96ef98d9/2@eb913cp1
[2025-06-03 11:00:24] (0s)  >>> Created relation Module::Module.toString/0#dispred#96ef98d9/2@eb913cp1 with 1 rows and digest 45e982o9jicnj7qdbrnlaubniqa.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Expr.toString/0#dispred#7435dfcd/2@i1#77d4ax0j (iteration 1)
[2025-06-03 11:00:24] (0s) 			 - Exprs::Expr.toString/0#dispred#7435dfcd_delta has 9 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::AstNode_.toString/0#dispred#192f27b5/2@i1#77d4aw0j (iteration 1)
[2025-06-03 11:00:24] (0s) 			 - AstGenerated::AstNode_.toString/0#dispred#192f27b5_delta has 6 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Expr.toString/0#dispred#7435dfcd/2@i2#77d4ax0j (iteration 2)
[2025-06-03 11:00:24] (0s) 			 - Exprs::Expr.toString/0#dispred#7435dfcd_delta has 3 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::AstNode_.toString/0#dispred#192f27b5/2@i2#77d4aw0j (iteration 2)
[2025-06-03 11:00:24] (0s) 			 - AstGenerated::AstNode_.toString/0#dispred#192f27b5_delta has 9 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Expr.toString/0#dispred#7435dfcd/2@i3#77d4ax0j (iteration 3)
[2025-06-03 11:00:24] (0s) Empty delta for Exprs::Expr.toString/0#dispred#7435dfcd_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::AstNode_.toString/0#dispred#192f27b5/2@i3#77d4aw0j (iteration 3)
[2025-06-03 11:00:24] (0s) 			 - AstGenerated::AstNode_.toString/0#dispred#192f27b5_delta has 3 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Expr.toString/0#dispred#7435dfcd/2@i4#77d4ax0j (iteration 4)
[2025-06-03 11:00:24] (0s) Empty delta for Exprs::Expr.toString/0#dispred#7435dfcd_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::AstNode_.toString/0#dispred#192f27b5/2@77d4aw0j with 18 rows and digest 48de7dnsrr6onrp48url3cd3fi6.
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Expr.toString/0#dispred#7435dfcd/2@77d4ax0j with 12 rows and digest 9a76d6d3fc58rsp8so6h6g69o5b.
[2025-06-03 11:00:24] (0s) Promoting strings for predicate Exprs::Expr.toString/0#dispred#7435dfcd
[2025-06-03 11:00:24] (0s) Promoted strings in predicate Exprs::Expr.toString/0#dispred#7435dfcd in memory, took 0ms
[2025-06-03 11:00:24] (0s) Saving stringpool to save strings from predicate Exprs::Expr.toString/0#dispred#7435dfcd
[2025-06-03 11:00:24] (0s) Saved stringpool to save strings from predicate Exprs::Expr.toString/0#dispred#7435dfcd, took 0ms
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_Exprs::Expr.toString/0#dispred#7435dfcd/2@a8c03872 with 12 rows and digest 9a76d6d3fc58rsp8so6h6g69o5b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#Exprs::Expr.toString/0#dispred#7435dfcd/1@68a913ck
[2025-06-03 11:00:24] (0s)  >>> Created relation project#Exprs::Expr.toString/0#dispred#7435dfcd/1@68a913ck with 12 rows and digest 1736dbj1l3ikjj3khg0j5sr5jh1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/2@0e8f5fja
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/2@0e8f5fja with 1 rows and digest 21b539r9gnpj3aqlakr76v3fade.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/1@f1729a8s
[2025-06-03 11:00:24] (0s)  >>> Created relation project#AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/1@f1729a8s with 1 rows and digest 1af716dgmv6tisa7rfru9mdlg9b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate __Files::Location.hasLocationInfo/5#dispred#143bb906__AstExtended::AstNode.getLocation/0#dispred#6b4__#antijoin_rhs/7@2cfac9ig
[2025-06-03 11:00:24] (0s)  >>> Created relation __Files::Location.hasLocationInfo/5#dispred#143bb906__AstExtended::AstNode.getLocation/0#dispred#6b4__#antijoin_rhs/7@2cfac9ig with 1 rows and digest 373e06h6k2e0lcshtkkcfbhnuj7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate #select#query/8@2b028ee8
[2025-06-03 11:00:24] (0s)  >>> Created relation #select#query/8@2b028ee8 with 1 rows and digest 8edc81i52engfj2fvo3eit9ljrd.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Scope_.toString/0#dispred#8b6e5a05/2@bb0e1096
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Scope_.toString/0#dispred#8b6e5a05/2@bb0e1096 with 2 rows and digest 100c26l9u38v2ndvnh1pc7rpl62.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#AstGenerated::Scope_.toString/0#dispred#8b6e5a05/1@73d698f9
[2025-06-03 11:00:24] (0s)  >>> Created relation project#AstGenerated::Scope_.toString/0#dispred#8b6e5a05/1@73d698f9 with 2 rows and digest 5a3e3bu55kd2p0hdc2avgtn4cd0.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_idoms/2@410143e0 with 14 rows and digest b8ff4ctm1agnlduchh0k0d26me1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::ControlFlowNode.getBasicBlock/0#dispred#32502ca1/2@7bb6d8k0
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::ControlFlowNode.getBasicBlock/0#dispred#32502ca1/2@7bb6d8k0 with 16 rows and digest d4dbcf9oga3cm5lb0cudcoatmd0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@0669956t
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@0669956t with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that #Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ecPlus/2@efd62daj is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@0669956t.
[2025-06-03 11:00:24] (0s) Inferred that Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec_10#higher_order_body/2@7dc1daln is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@0669956t.
[2025-06-03 11:00:24] (0s) Inferred that cached_Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@408ca88v is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@0669956t.
[2025-06-03 11:00:24] (0s) Inferred that Flow::BasicBlock.strictlyDominates/1#dispred#65ae2ca3/2@b57cb342 is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec_10#higher_order_body/2@7dc1daln.
[2025-06-03 11:00:24] (0s) Inferred that boundedFastTC:Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec_10#higher_order_body:project#py_flow_bb_node/2@fb072d0h is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec_10#higher_order_body/2@7dc1daln.
[2025-06-03 11:00:24] (0s) Inferred that SsaCompute::SsaDefinitions::reachesEndOfBlockRec/4#63bb2cd4/4@7f2bex0h is empty, due to Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec_10#higher_order_body/2@7dc1daln.
[2025-06-03 11:00:24] (0s) Inferred that cached_Flow::BasicBlock.strictlyDominates/1#dispred#65ae2ca3/2@40c1c68m is empty, due to Flow::BasicBlock.strictlyDominates/1#dispred#65ae2ca3/2@b57cb342.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_successors/2@6aebe9dg with 14 rows and digest 36f72fggi7kglbca2fs2t4qdcla.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_successors_10#join_rhs/2@3d6d1bpm
[2025-06-03 11:00:24] (0s)  >>> Created relation py_successors_10#join_rhs/2@3d6d1bpm with 14 rows and digest b8ff4ctm1agnlduchh0k0d26me1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_flow_bb_node_23#max_range/2@58ea4f9h
[2025-06-03 11:00:24] (0s)  >>> Created relation py_flow_bb_node_23#max_range/2@58ea4f9h with 16 rows and digest 41dfd2of5vkis75lo113ulsot64.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_flow_bb_node_233#max_term/3@38e43dst
[2025-06-03 11:00:24] (0s)  >>> Created relation py_flow_bb_node_233#max_term/3@38e43dst with 16 rows and digest 16c1c18ikjucqoj7l4dttmumqd3.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::BasicBlock.getLastNode/0#dispred#6e185cc5/2@70ffa0i0
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::BasicBlock.getLastNode/0#dispred#6e185cc5/2@70ffa0i0 with 2 rows and digest 475decq5narp4dpvtqrm986stra.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::BasicBlock.getLastNode/0#dispred#6e185cc5_10#join_rhs/2@1540a3i5
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::BasicBlock.getLastNode/0#dispred#6e185cc5_10#join_rhs/2@1540a3i5 with 2 rows and digest de746c9n2p509g1h5cjhmu41tpa.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that #Flow::BasicBlock.getASuccessor/0#dispred#7249dd96Plus/2@aa36c8a7 is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:00:24] (0s) Inferred that Flow::BasicBlock.dominanceFrontier/1#309d1dd6/2@b6351eam is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:00:24] (0s) Inferred that project#Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/1@616cb54h is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:00:24] (0s) Inferred that Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs/2@b268c00t is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:00:24] (0s) Inferred that SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@9473awuj is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:00:24] (0s) Inferred that cached_Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@debdc7ao is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@49db7am6.
[2025-06-03 11:00:24] (0s) Inferred that cached_#Flow::BasicBlock.getASuccessor/0#dispred#7249dd96Plus/2@4ae642b8 is empty, due to #Flow::BasicBlock.getASuccessor/0#dispred#7249dd96Plus/2@aa36c8a7.
[2025-06-03 11:00:24] (0s) Inferred that SsaCompute::AdjacentUses::blockPrecedesVar/2#b4e71f74#fb/2@77197fkc is empty, due to project#Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/1@616cb54h.
[2025-06-03 11:00:24] (0s) Inferred that _Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs_Flow::ControlFlowNode.getBasicBlock/0__#shared/2@8e989ea9 is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs/2@b268c00t.
[2025-06-03 11:00:24] (0s) Inferred that SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@34c6exe2 is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs/2@b268c00t.
[2025-06-03 11:00:24] (0s) Inferred that Essa::PhiFunction.pred_var/1#da33d336/3@3b88044n is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs/2@b268c00t.
[2025-06-03 11:00:24] (0s) Inferred that SsaCompute::AdjacentUses::varBlockReaches/3#1824ad86/3@259e0cno is empty, due to Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs/2@b268c00t.
[2025-06-03 11:00:24] (0s) Inferred that Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp is empty, due to SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@9473awuj.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@ce8e8e2f is empty, due to SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@9473awuj.
[2025-06-03 11:00:24] (0s) Inferred that SsaCompute::AdjacentUses::blockPrecedesVar/2#b4e71f74#fb_10#join_rhs/2@4c76a4dd is empty, due to SsaCompute::AdjacentUses::blockPrecedesVar/2#b4e71f74#fb/2@77197fkc.
[2025-06-03 11:00:24] (0s) Inferred that _Flow::BasicBlock.dominates/1#dispred#47f3be21_Flow::ControlFlowNode.getBasicBlock/0#dispred#32502ca__#antijoin_rhs/2@1f8868kr is empty, due to _Flow::BasicBlock.getASuccessor/0#dispred#7249dd96_10#join_rhs_Flow::ControlFlowNode.getBasicBlock/0__#shared/2@8e989ea9.
[2025-06-03 11:00:24] (0s) Inferred that SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@7f2bew0h is empty, due to SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@34c6exe2.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@00f3ba04 is empty, due to SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@34c6exe2.
[2025-06-03 11:00:24] (0s) Inferred that Essa::PhiFunction.pred_var/1#da33d336_120#join_rhs/3@d1fdaft4 is empty, due to Essa::PhiFunction.pred_var/1#da33d336/3@3b88044n.
[2025-06-03 11:00:24] (0s) Inferred that Essa::PhiFunction.getScope/0#dispred#2fe03abc/2@0cda4bmj is empty, due to Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp.
[2025-06-03 11:00:24] (0s) Inferred that Essa::PhiFunction.inputEdgeRefinement/1#dispred#c0feabdb/3@db6addbv is empty, due to Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp.
[2025-06-03 11:00:24] (0s) Inferred that Essa::TPhiFunction#dfb6fe3b_2#join_rhs/1@b12267pb is empty, due to Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp.
[2025-06-03 11:00:24] (0s) Inferred that Essa::PhiFunction.getInput/1#dispred#f797e1b9/3@76e3f3vc is empty, due to Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp.
[2025-06-03 11:00:24] (0s) Inferred that cached_Essa::TPhiFunction#dfb6fe3b/3@2bce46ul is empty, due to Essa::TPhiFunction#dfb6fe3b/3@7b9d17qp.
[2025-06-03 11:00:24] (0s) Inferred that Essa::EssaDefinition.reachesEndOfBlock/1#dispred#b258d4ae/2@2759b57c is empty, due to SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@7f2bew0h.
[2025-06-03 11:00:24] (0s) Inferred that project#SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/2@570bf0jq is empty, due to SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@7f2bew0h.
[2025-06-03 11:00:24] (0s) Inferred that SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902_0312#join_rhs/4@21d3baee is empty, due to SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@7f2bew0h.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@8d3536mv is empty, due to SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@7f2bew0h.
[2025-06-03 11:00:24] (0s) Inferred that Essa::PhiFunction.inputEdgeRefinement/1#dispred#c0feabdb_01#antijoin_rhs/2@9056d0ou is empty, due to Essa::PhiFunction.inputEdgeRefinement/1#dispred#c0feabdb/3@db6addbv.
[2025-06-03 11:00:24] (0s) Inferred that cached_Essa::PhiFunction.getInput/1#dispred#f797e1b9/3@8f4ad7c4 is empty, due to Essa::PhiFunction.getInput/1#dispred#f797e1b9/3@76e3f3vc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::NameNode.getId/0#dispred#330d952b/2@21c76cvl
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::NameNode.getId/0#dispred#330d952b/2@21c76cvl with 6 rows and digest 6fa4849srssha41euk95pq7a0gf.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3_Flow::NameNode.getId/0#dispred#330d952b_Flow::Scope__#shared/2@f0a715ke
[2025-06-03 11:00:24] (0s)  >>> Created relation _Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3_Flow::NameNode.getId/0#dispred#330d952b_Flow::Scope__#shared/2@f0a715ke with 4 rows and digest c87431hdarj8j7ekulekqof47ae.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3_Flow::Scopes::non_local/1#5e3b85d8#shared/1@ca65d1o3
[2025-06-03 11:00:24] (0s)  >>> Created relation _Flow::ControlFlowNode.isLoad/0#dispred#ea0a60b3_Flow::Scopes::non_local/1#5e3b85d8#shared/1@ca65d1o3 with 4 rows and digest 588dedkgu31rqrh8ga8mcp51nl9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs_py_flow_bb_node_10#join_rhs#shared#1/1@f244abi2
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs_py_flow_bb_node_10#join_rhs#shared#1/1@f244abi2 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_ssa_use/2@75fd04ej with 5 rows and digest b627b4nnviabmi77b0bdohou6d0.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_ssa_var/2@ff6793u4 with 4 rows and digest d0f441s6is91fnn5o6i2gi20o0b.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_ssa_phi/2@6444c77f with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that SSA::SsaVariable.getAPhiInput/0#dispred#de01a47b/2@bedcdfct is empty, due to py_ssa_phi/2@6444c77f.
[2025-06-03 11:00:24] (0s) Inferred that project#py_ssa_phi/1@2c8f17nc is empty, due to py_ssa_phi/2@6444c77f.
[2025-06-03 11:00:24] (0s) Inferred that project#SSA::SsaVariable.getAPhiInput/0#dispred#de01a47b/1@eeeb222m is empty, due to SSA::SsaVariable.getAPhiInput/0#dispred#de01a47b/2@bedcdfct.
[2025-06-03 11:00:24] (0s) Inferred that SSA::SsaVariable.getAPhiInput/0#dispred#de01a47b_10#join_rhs/2@d9d87cid is empty, due to SSA::SsaVariable.getAPhiInput/0#dispred#de01a47b/2@bedcdfct.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_ssa_defn/2@8908c5g8 with 2 rows and digest 4b6315dg5psas5l6k8chk3ri024.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SSA::SsaVariable.getDefinition/0#dispred#0643efdb/2@5cb908cb
[2025-06-03 11:00:24] (0s)  >>> Created relation SSA::SsaVariable.getDefinition/0#dispred#0643efdb/2@5cb908cb with 2 rows and digest 4b6315dg5psas5l6k8chk3ri024.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SSA::SsaVariable.getDefinition/0#dispred#0643efdb_0#antijoin_rhs/1@f32b0c5v
[2025-06-03 11:00:24] (0s)  >>> Created relation SSA::SsaVariable.getDefinition/0#dispred#0643efdb_0#antijoin_rhs/1@f32b0c5v with 2 rows and digest a541bcm8ipnooham8us029m1nrd.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::Scopes::maybe_undefined/1#564dc356/1@i1#9336ebcg (iteration 1)
[2025-06-03 11:00:24] (0s) 			 - Flow::Scopes::maybe_undefined/1#564dc356_delta has 2 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::Scopes::maybe_undefined/1#564dc356/1@i2#9336ebcg (iteration 2)
[2025-06-03 11:00:24] (0s) Empty delta for Flow::Scopes::maybe_undefined/1#564dc356_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::Scopes::maybe_undefined/1#564dc356/1@9336ebcg with 2 rows and digest 861e1ctbmma9aulht8vtc500foc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate py_ssa_var_10#join_rhs/2@1a66c0th
[2025-06-03 11:00:24] (0s)  >>> Created relation py_ssa_var_10#join_rhs/2@1a66c0th with 4 rows and digest 69a2edkmfku4b4hv50d304ecpca.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SSA::SsaVariable.getVariable/0#dispred#27f5054d/2@1be7adin
[2025-06-03 11:00:24] (0s)  >>> Created relation SSA::SsaVariable.getVariable/0#dispred#27f5054d/2@1be7adin with 4 rows and digest d0f441s6is91fnn5o6i2gi20o0b.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Flow::Scopes::maybe_undefined/1#564dc356_SSA::Ssa__#antijoin_rhs/1@89dc2bhf
[2025-06-03 11:00:24] (0s)  >>> Created relation _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Flow::Scopes::maybe_undefined/1#564dc356_SSA::Ssa__#antijoin_rhs/1@89dc2bhf with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate m#Flow::ControlFlowNode.getEnclosingModule/0#dispred#8908be76#bf/1@c3129cdg
[2025-06-03 11:00:24] (0s)  >>> Created relation m#Flow::ControlFlowNode.getEnclosingModule/0#dispred#8908be76#bf/1@c3129cdg with 4 rows and digest 588dedkgu31rqrh8ga8mcp51nl9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52_10#join_rhs/2@962a72kh
[2025-06-03 11:00:24] (0s)  >>> Created relation Scope::Scope.getEnclosingScope/0#dispred#8b0b5c52_10#join_rhs/2@962a72kh with 1 rows and digest 698238m8l8m1edppgd1i0t8tccf.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce/2@i1#e3be43eh (iteration 1)
[2025-06-03 11:00:24] (0s) 			 - Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce_delta has 1 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce/2@i2#e3be43eh (iteration 2)
[2025-06-03 11:00:24] (0s) 			 - Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce_delta has 1 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce/2@i3#e3be43eh (iteration 3)
[2025-06-03 11:00:24] (0s) Empty delta for Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Created relation Scope::Scope.getEnclosingModule/0#dispred#2a7dc9ce/2@e3be43eh with 2 rows and digest 5c27411ftq1hfn4n0jithooe3df.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::ControlFlowNode.getEnclosingModule/0#dispred#8908be76#bf/2@1ad5c47n
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::ControlFlowNode.getEnclosingModule/0#dispred#8908be76#bf/2@1ad5c47n with 4 rows and digest 1acaa7l72qo4o4pnc1ioqjmhokf.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Flow::Scopes::maybe_undefined/1#564dc356_SSA::Ssa__#antijoin_rhs#1/2@93c886rg
[2025-06-03 11:00:24] (0s)  >>> Created relation _Flow::ControlFlowNode.getScope/0#dispred#b061daac_Flow::Scopes::maybe_undefined/1#564dc356_SSA::Ssa__#antijoin_rhs#1/2@93c886rg with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::Scopes::use_of_global_variable/3#a7f259dd/3@1bf1cdua
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::Scopes::use_of_global_variable/3#a7f259dd/3@1bf1cdua with 4 rows and digest 9680fa30k17mglgq2gt6cuukh92.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::Scopes::use_of_global_variable/3#a7f259dd_120#join_rhs/3@7baf52g8
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::Scopes::use_of_global_variable/3#a7f259dd_120#join_rhs/3@7baf52g8 with 4 rows and digest 882d7d2apotq93ra2qu5qbi7csb.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::NameNode.uses/1#dispred#c58344eb/2@50caaf5s
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::NameNode.uses/1#dispred#c58344eb/2@50caaf5s with 4 rows and digest fb2755pqcbv54gnu0btqtaduku8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::NameNode.uses/1#dispred#c58344eb_10#join_rhs/2@643c36mi
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::NameNode.uses/1#dispred#c58344eb_10#join_rhs/2@643c36mi with 4 rows and digest d8959745evfc5mai91rqic5j9tc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaDefinitions::SsaSource::import_star_refinement/3#1675f8e2/3@990b93tb
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaDefinitions::SsaSource::import_star_refinement/3#1675f8e2/3@990b93tb with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::import_star_refinement/3#1675f8e2/3@39fa47h0 is empty, due to SsaDefinitions::SsaSource::import_star_refinement/3#1675f8e2/3@990b93tb.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::ModuleVariable.getAnImplicitUse/0#59874ece/2@ca93366q
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::ModuleVariable.getAnImplicitUse/0#59874ece/2@ca93366q with 8 rows and digest 07674cfbpvboifh7gt3rmb5hqff.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getASourceUse/0#dispred#8daf149a/2@492bd0ae
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.getASourceUse/0#dispred#8daf149a/2@492bd0ae with 4 rows and digest d8959745evfc5mai91rqic5j9tc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getAUse/0#dispred#84f49ef1/2@5962a68o
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.getAUse/0#dispred#84f49ef1/2@5962a68o with 20 rows and digest 379f30ht2fqmh89jg1dmrau2jp2.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getAUse/0#dispred#84f49ef1_10#join_rhs/2@5398fd51
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.getAUse/0#dispred#84f49ef1_10#join_rhs/2@5398fd51 with 20 rows and digest 101a27kbupv4un29q7r2uk3r2m0.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_false_successors/2@470a4ecp with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Flow::BasicBlock.getAFalseSuccessor/0#dispred#4022ff35/2@de4321i8 is empty, due to py_false_successors/2@470a4ecp.
[2025-06-03 11:00:24] (0s)  >>> Created relation py_true_successors/2@db6ceaic with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Flow::BasicBlock.getATrueSuccessor/0#dispred#dce507ff/2@7b4d7dld is empty, due to py_true_successors/2@db6ceaic.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::ControlFlowNode.isBranch/0#dispred#7bdac72a/1@db5705km
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::ControlFlowNode.isBranch/0#dispred#7bdac72a/1@db5705km with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that #Flow::ControlFlowNode.getAChild/0#dispred#610e0803Plus#bf/2@0260a9a3 is empty, due to Flow::ControlFlowNode.isBranch/0#dispred#7bdac72a/1@db5705km.
[2025-06-03 11:00:24] (0s) Inferred that Base::test_contains/2#8f99225e/2@6b9502dt is empty, due to Flow::ControlFlowNode.isBranch/0#dispred#7bdac72a/1@db5705km.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::test_refinement/3#2cbdf557/3@e584a6c6 is empty, due to Flow::ControlFlowNode.isBranch/0#dispred#7bdac72a/1@db5705km.
[2025-06-03 11:00:24] (0s) Inferred that project#Base::test_contains/2#8f99225e/1@58089flt is empty, due to Base::test_contains/2#8f99225e/2@6b9502dt.
[2025-06-03 11:00:24] (0s) Inferred that Base::test_contains/2#8f99225e_10#join_rhs/2@74835br1 is empty, due to Base::test_contains/2#8f99225e/2@6b9502dt.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::test_refinement/3#2cbdf557/3@c646bdum is empty, due to SsaDefinitions::SsaSource::test_refinement/3#2cbdf557/3@e584a6c6.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::SsaSourceVariable.hasRefinementEdge/3#dispred#d08844b4/4@f74ef2bl is empty, due to Base::test_contains/2#8f99225e_10#join_rhs/2@74835br1.
[2025-06-03 11:00:24] (0s) Inferred that project#Definitions::SsaSourceVariable.hasRefinementEdge/3#dispred#d08844b4/3@4bc039v4 is empty, due to Definitions::SsaSourceVariable.hasRefinementEdge/3#dispred#d08844b4/4@f74ef2bl.
[2025-06-03 11:00:24] (0s) Inferred that SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@0cb54aqn is empty, due to project#Definitions::SsaSourceVariable.hasRefinementEdge/3#dispred#d08844b4/3@4bc039v4.
[2025-06-03 11:00:24] (0s) Inferred that Essa::TEssaEdgeDefinition#be4738bc/4@81064dgf is empty, due to SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@0cb54aqn.
[2025-06-03 11:00:24] (0s) Inferred that project#SsaCompute::EssaDefinitions::piNode/3#b1917f93/2@eacc13qp is empty, due to SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@0cb54aqn.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@3204beti is empty, due to SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@0cb54aqn.
[2025-06-03 11:00:24] (0s) Inferred that cached_Essa::TEssaEdgeDefinition#be4738bc/4@2da913f5 is empty, due to Essa::TEssaEdgeDefinition#be4738bc/4@81064dgf.
[2025-06-03 11:00:24] (0s) Inferred that project#SsaCompute::EssaDefinitions::piNode/3#b1917f93_10#join_rhs/2@65def8pf is empty, due to project#SsaCompute::EssaDefinitions::piNode/3#b1917f93/2@eacc13qp.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::ModuleVariable.getScopeEntryDefinition/0#71ddbc6f/2@788c8ebq
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::ModuleVariable.getScopeEntryDefinition/0#71ddbc6f/2@788c8ebq with 4 rows and digest 0dba01g4ivdphruqbt7ad1rhh21.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getASourceUse/0#dispred#8daf149a_10#join_rhs/2@5f403a1i
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.getASourceUse/0#dispred#8daf149a_10#join_rhs/2@5f403a1i with 4 rows and digest fb2755pqcbv54gnu0btqtaduku8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19/4@939457gr
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19/4@939457gr with 4 rows and digest e6615eck5kp4fufbdrbrqo905g3.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19/4@271bba4s with 4 rows and digest e6615eck5kp4fufbdrbrqo905g3.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _Flow::CallNode#c5d1dc47_Flow::ControlFlowNode.getBasicBlock/0#dispred#32502ca1_py_flow_bb_node#shared/3@e69f40fg
[2025-06-03 11:00:24] (0s)  >>> Created relation _Flow::CallNode#c5d1dc47_Flow::ControlFlowNode.getBasicBlock/0#dispred#32502ca1_py_flow_bb_node#shared/3@e69f40fg with 3 rows and digest 94a3d1ns4c2ln50a5arfbhal0s4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::BasicBlock.dominates/1#dispred#47f3be21/2@5e4ac3fn
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::BasicBlock.dominates/1#dispred#47f3be21/2@5e4ac3fn with 2 rows and digest 724af9o4m9ubpbph2tbvo5m2ql7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::CallNode.getArg/1#dispred#044295cc/3@f46861pq
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::CallNode.getArg/1#dispred#044295cc/3@f46861pq with 3 rows and digest 330e453ifu6h80eb0svf1q5gg35.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::CallNode.getArg/1#dispred#044295cc_102#join_rhs/3@06a9058a
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::CallNode.getArg/1#dispred#044295cc_102#join_rhs/3@06a9058a with 3 rows and digest 6a1fc75mh51qs2sneq1icoecj8a.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaDefinitions::SsaSource::argument_refinement/3#615fb3a9/3@c0a40blj
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaDefinitions::SsaSource::argument_refinement/3#615fb3a9/3@c0a40blj with 1 rows and digest a93564oqls7eb1kr6u2n1ivvha5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs_py_flow_bb_node_10#join_rhs#shared/1@a937a3qn
[2025-06-03 11:00:24] (0s)  >>> Created relation _py_exprs_10#join_rhs_py_flow_bb_node_10#join_rhs#shared/1@a937a3qn with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Flow::ListNode#fccc80d7/1@273596u4 is empty, due to _py_exprs_10#join_rhs_py_flow_bb_node_10#join_rhs#shared/1@a937a3qn.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate m#Flow::SequenceNode.SequenceNode#b8039542#b/1@0ef2ddc9
[2025-06-03 11:00:24] (0s)  >>> Created relation m#Flow::SequenceNode.SequenceNode#b8039542#b/1@0ef2ddc9 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that Flow::SequenceNode.SequenceNode#b8039542#b/1@7920bcg1 is empty, due to m#Flow::SequenceNode.SequenceNode#b8039542#b/1@0ef2ddc9.
[2025-06-03 11:00:24] (0s) Inferred that Flow::SequenceNode.getElement/1#dispred#4cc4068f/3@49e480so is empty, due to Flow::SequenceNode.SequenceNode#b8039542#b/1@7920bcg1.
[2025-06-03 11:00:24] (0s) Inferred that Flow::SequenceNode.getElement/1#dispred#4cc4068f_201#join_rhs/3@968118v4 is empty, due to Flow::SequenceNode.getElement/1#dispred#4cc4068f/3@49e480so.
[2025-06-03 11:00:24] (0s) Inferred that cached_Flow::SequenceNode.getElement/1#dispred#4cc4068f/3@cc5e78ka is empty, due to Flow::SequenceNode.getElement/1#dispred#4cc4068f/3@49e480so.
[2025-06-03 11:00:24] (0s) Inferred that SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/4@36bf64pa is empty, due to Flow::SequenceNode.getElement/1#dispred#4cc4068f_201#join_rhs/3@968118v4.
[2025-06-03 11:00:24] (0s) Inferred that project#SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/2@704beekm is empty, due to SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/4@36bf64pa.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/4@89e653d3 is empty, due to SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/4@36bf64pa.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaDefinitions::SsaSource::argument_refinement/3#615fb3a9  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaDefinitions::SsaSource::argument_refinement/3#615fb3a9/3@2d844778 with 1 rows and digest a93564oqls7eb1kr6u2n1ivvha5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#py_scope_flow/1@48c253lo
[2025-06-03 11:00:24] (0s)  >>> Created relation project#py_scope_flow/1@48c253lo with 2 rows and digest 5a3e3bu55kd2p0hdc2avgtn4cd0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entity/2@24aee2sg
[2025-06-03 11:00:24] (0s)  >>> Created relation disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entity/2@24aee2sg with 17 rows and digest 573f3d91dsj0repbc8fcc5jg3k2.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate #disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entityPlus#bf/2@i1#04af58l7 (iteration 1)
[2025-06-03 11:00:24] (0s) 			 - #disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entityPlus#bf_delta has 1 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate #disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entityPlus#bf/2@i2#04af58l7 (iteration 2)
[2025-06-03 11:00:24] (0s) Empty delta for #disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entityPlus#bf_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Created relation #disj_Scope::Scope.getScope/0#dispred#055e5112_AstExtended::AstNode.getScope/0#dispred#9b09b826_entity_entityPlus#bf/2@04af58l7 with 1 rows and digest 57035btodt35ck120tnd3akqgmc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.getScopeEntryDefinition/0#dispred#6adeb52f/2@c3444fid
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.getScopeEntryDefinition/0#dispred#6adeb52f/2@c3444fid with 5 rows and digest ac5857a9skogv90smkidungojn0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Exprs::Expr.getASubExpression/0#dispred#443395f9_10#join_rhs/2@f87c9dtu
[2025-06-03 11:00:24] (0s)  >>> Created relation Exprs::Expr.getASubExpression/0#dispred#443395f9_10#join_rhs/2@f87c9dtu with 6 rows and digest e40e5bimtcs258vu7jgu3bavp0f.
[2025-06-03 11:00:24] (0s)  >>> Created relation m##Exprs::Expr.getASubExpression/0#dispred#443395f9Plus#fb/1@640ea5i9 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that #Exprs::Expr.getASubExpression/0#dispred#443395f9Plus#fb#flipped/2@95abd0rl is empty, due to m##Exprs::Expr.getASubExpression/0#dispred#443395f9Plus#fb/1@640ea5i9.
[2025-06-03 11:00:24] (0s) Inferred that #Exprs::Expr.getASubExpression/0#dispred#443395f9Plus#fb#flipped_10#join_rhs/2@852481hd is empty, due to #Exprs::Expr.getASubExpression/0#dispred#443395f9Plus#fb#flipped/2@95abd0rl.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::nested_sequence_assign/4#08cf2807/4@i1#9bd516ft (iteration 1)
[2025-06-03 11:00:24] (0s) Empty delta for Flow::nested_sequence_assign/4#08cf2807_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::nested_sequence_assign/4#08cf2807/4@9bd516ft with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::assigned_value/1#97af2d4b#bf/2@2fecfdv1
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::assigned_value/1#97af2d4b#bf/2@2fecfdv1 with 2 rows and digest 7969e6fndkcm0h9b01m6ul8ebud.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee_10#join_rhs/2@e86b40f3
[2025-06-03 11:00:24] (0s)  >>> Created relation AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee_10#join_rhs/2@e86b40f3 with 16 rows and digest f046bei687v6bg0fjsta3msqsn2.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::DefinitionNode.getValue/0#dispred#e8c0c58e#bf/2@3244560u
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::DefinitionNode.getValue/0#dispred#e8c0c58e#bf/2@3244560u with 2 rows and digest e7f2220phprqon2obnuaihnuqdf.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/3@d174ac60
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/3@d174ac60 with 2 rows and digest 676ee26601k6ksfp4jn66fdhev9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/2@8ef90csr
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/2@8ef90csr with 2 rows and digest aaf111ojgno6s22av04sl40ie70.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482/2@6968db4t
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482/2@6968db4t with 7 rows and digest c113ebmfgfv7fen3npfqcoaqced.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482_10#join_rhs/2@aacec1qn
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482_10#join_rhs/2@aacec1qn with 7 rows and digest 3094e9d6t0s2u4jsedeqmopobm9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableDefine/4#79261f91/4@f9882707
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::variableDefine/4#79261f91/4@f9882707 with 7 rows and digest 092bba80tq1u78ol0repcorp8q9.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::variableDefine/4#79261f91  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::variableDefine/4#79261f91/4@1e9ed9n1 with 7 rows and digest 092bba80tq1u78ol0repcorp8q9.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/3@5c11868o with 2 rows and digest 676ee26601k6ksfp4jn66fdhev9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482/1@e5a0e8v6
[2025-06-03 11:00:24] (0s)  >>> Created relation project#Definitions::SsaSourceVariable.hasDefiningNode/1#dispred#688e3482/1@e5a0e8v6 with 5 rows and digest 2363e7tivn6cbqesnp14kh9qqjc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SpecialSsaSourceVariable.scope_as_global_variable/0#d3f1993c/2@c09632d0
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SpecialSsaSourceVariable.scope_as_global_variable/0#d3f1993c/2@c09632d0 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#5#3/1@a1e01c00 is empty, due to Definitions::SpecialSsaSourceVariable.scope_as_global_variable/0#d3f1993c/2@c09632d0.
[2025-06-03 11:00:24] (0s) Inferred that Definitions::SpecialSsaSourceVariable.scope_as_global_variable/0#d3f1993c_10#join_rhs/2@c21d86ob is empty, due to Definitions::SpecialSsaSourceVariable.scope_as_global_variable/0#d3f1993c/2@c09632d0.
[2025-06-03 11:00:24] (0s) Inferred that #Scope::Scope.getScope/0#dispred#055e5112Plus#bounded#3/2@f1a718de is empty, due to #Scope::Scope.getScope/0#dispred#055e5112Plus#sinkBound#5#3/1@a1e01c00.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.redefinedAtCallSite/0#dispred#2b69e25a/2@016dcfh8
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.redefinedAtCallSite/0#dispred#2b69e25a/2@016dcfh8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::refinement/3#4b377430/3@fc74a9bs
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::refinement/3#4b377430/3@fc74a9bs with 1 rows and digest a93564oqls7eb1kr6u2n1ivvha5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd/3@6452e6kk
[2025-06-03 11:00:24] (0s)  >>> Created relation Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd/3@6452e6kk with 1 rows and digest a93564oqls7eb1kr6u2n1ivvha5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd#2/2@a5046a55
[2025-06-03 11:00:24] (0s)  >>> Created relation project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd#2/2@a5046a55 with 1 rows and digest f17fa7idegsgskhblt5ci19jp47.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd#2_10#join_rhs/2@a13ecfhj
[2025-06-03 11:00:24] (0s)  >>> Created relation project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd#2_10#join_rhs/2@a13ecfhj with 1 rows and digest 07f1a73u7d0dgpfr7lt4r813me7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableUse/4#da62dc30/4@f2c3b95u
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::variableUse/4#da62dc30/4@f2c3b95u with 20 rows and digest 1df1dbt6d4usf41b2t262g8vchc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd/2@a9dc37nb
[2025-06-03 11:00:24] (0s)  >>> Created relation project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd/2@a9dc37nb with 1 rows and digest 0f5940ikqhfmuvn3ltb4noeoan5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd_10#join_rhs/2@6f6e5a5o
[2025-06-03 11:00:24] (0s)  >>> Created relation project#Definitions::SsaSourceVariable.hasRefinement/2#dispred#b79d94bd_10#join_rhs/2@6f6e5a5o with 1 rows and digest 7c3a00s8fji3q6vt9oc2a7r1bk5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableRefine/4#b20a3c26/4@fe03852h
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::variableRefine/4#b20a3c26/4@fe03852h with 1 rows and digest 25c6bdfuslr7rg5gc8veckciai1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e/4@a68ed5g3
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e/4@a68ed5g3 with 8 rows and digest 5004c4bo7mbf5ud2fcdlgb3g574.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e/4@8a1c0f76 with 8 rows and digest 5004c4bo7mbf5ud2fcdlgb3g574.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::variableRefine/4#b20a3c26  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::variableRefine/4#b20a3c26/4@209e7767 with 1 rows and digest 25c6bdfuslr7rg5gc8veckciai1.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::variableUse/4#da62dc30  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::variableUse/4#da62dc30/4@ce02f549 with 20 rows and digest 1df1dbt6d4usf41b2t262g8vchc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e_SsaCompute::SsaComputeImpl::variableUse/4#da62dc__#rank_range/3@890f13f9
[2025-06-03 11:00:24] (0s)  >>> Created relation _SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e_SsaCompute::SsaComputeImpl::variableUse/4#da62dc__#rank_range/3@890f13f9 with 28 rows and digest 4520dalt8iqtaqvhrhndl29fke3.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e_SsaCompute::SsaComputeImpl::variableUse/4#da62dc__#rank_term/4@5c208a60
[2025-06-03 11:00:24] (0s)  >>> Created relation _SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e_SsaCompute::SsaComputeImpl::variableUse/4#da62dc__#rank_term/4@5c208a60 with 28 rows and digest a4cba0s2bg1hcnpf3t8d7ije7m8.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/4@b3befalb
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/4@b3befalb with 28 rows and digest 0417e35kkqc0rg6tp70ed6vmkoc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs/4@c21f6564
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs/4@c21f6564 with 28 rows and digest 7004efmbufu8fg18o8a6962tt27.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#3/3@b3c54bs6
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#3/3@b3c54bs6 with 28 rows and digest 3af1940t0jvlajspainfqcgnk39.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::defRank/4#f608ea69/4@da81e32q
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::defRank/4#f608ea69/4@da81e32q with 8 rows and digest 839f054q5dm2v2eu5dn89o50f2f.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variabl__#shared#1/6@8e8ed026
[2025-06-03 11:00:24] (0s)  >>> Created relation _SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variabl__#shared#1/6@8e8ed026 with 1 rows and digest ccc377mph10pljo1jt8lho27be4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defRank/4#f608ea69#2/3@2a826fo0
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defRank/4#f608ea69#2/3@2a826fo0 with 8 rows and digest bc1eb3s9ou5fg19p7q86l9dmfpf.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#2/5@812be6br
[2025-06-03 11:00:24] (0s)  >>> Created relation __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#2/5@812be6br with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_201#join_rhs/3@ccdbd5s2
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_201#join_rhs/3@ccdbd5s2 with 28 rows and digest eb72c4cq7e025tbotsqq2g7i6o3.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/2@28e2d9fe
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/2@28e2d9fe with 9 rows and digest 154706ulkd0oc6a4c6ce39dnqp7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#2/2@e6c50ejn
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#2/2@e6c50ejn with 9 rows and digest 154706ulkd0oc6a4c6ce39dnqp7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::defRank/4#f608ea69_201#join_rhs/3@9e56cd2f
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::defRank/4#f608ea69_201#join_rhs/3@9e56cd2f with 8 rows and digest 0013639r898udmv0rglaajsfcl0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defRank/4#f608ea69/2@acb191tk
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defRank/4#f608ea69/2@acb191tk with 5 rows and digest ac5857a9skogv90smkidungojn0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@i1#34c6exe2 (iteration 1)
[2025-06-03 11:00:24] (0s) Empty delta for SsaCompute::Liveness::liveAtExit/2#b6aa63f4_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::Liveness::liveAtEntry/2#bab3ea7c/2@i1#34c6ewe2 (iteration 1)
[2025-06-03 11:00:24] (0s) 			 - SsaCompute::Liveness::liveAtEntry/2#bab3ea7c_delta has 4 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@i2#34c6exe2 (iteration 2)
[2025-06-03 11:00:24] (0s) Empty delta for SsaCompute::Liveness::liveAtExit/2#b6aa63f4_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::Liveness::liveAtEntry/2#bab3ea7c/2@34c6ewe2 with 4 rows and digest eb5423eqpgrlj4t7aqcr9p123g1.
[2025-06-03 11:00:24] (0s)  >>> Discarded freshly computed SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@34c6exe2, preempted by a cache hit with digest THIS-RELATION-HAS-NO-TUPLES
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#3/5@4e74ffee
[2025-06-03 11:00:24] (0s)  >>> Created relation __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#3/5@4e74ffee with 1 rows and digest 80625fj4f6cvgpuoejikcnhtsb9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::EssaDefinitions::variableRefinement/5#4e891107/5@7592ec5c
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::EssaDefinitions::variableRefinement/5#4e891107/5@7592ec5c with 1 rows and digest b3d7de0gs9kop771cl5nf6ki0ub.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Essa::TEssaNodeRefinement#dom#8a14bc22/3@96c416k8
[2025-06-03 11:00:24] (0s)  >>> Created relation Essa::TEssaNodeRefinement#dom#8a14bc22/3@96c416k8 with 1 rows and digest a470acck2h7vpqtffkb45kf2ln9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Essa::TEssaNodeRefinement#8de2de42/4@1c2186sa
[2025-06-03 11:00:24] Evaluating HOP construct<Essa#24e22a14::TEssaDefinition,1> with inputs:
                        1 tuples in Essa::TEssaNodeRefinement#dom#8a14bc22/3@96c416k8
[2025-06-03 11:00:24] (0s)  >>> Created relation Essa::TEssaNodeRefinement#8de2de42/4@1c2186sa with 1 rows and digest 2f6ae1f2ph7h4qua0geof0vcnh9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variabl__#shared/6@531e5ab9
[2025-06-03 11:00:24] (0s)  >>> Created relation _SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variabl__#shared/6@531e5ab9 with 7 rows and digest d01fdavv6sho9utkniltopiuh7f.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs/5@bef0ecvk
[2025-06-03 11:00:24] (0s)  >>> Created relation __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs/5@bef0ecvk with 1 rows and digest 2f9568e16nquq395pq19na4l9o9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#1/5@9354301l
[2025-06-03 11:00:24] (0s)  >>> Created relation __SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48_0132#join_rhs_SsaCompute::SsaComputeImpl::variab__#antijoin_rhs#1/5@9354301l with 7 rows and digest aadc7arvmr942t8ds4etv4gjgoc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::EssaDefinitions::variableDefinition/5#32b5d84f/5@a9149de0
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::EssaDefinitions::variableDefinition/5#32b5d84f/5@a9149de0 with 6 rows and digest d4771e0f1l5ejuplaa8p29flca9.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Essa::TEssaNodeDefinition#dom#e1989adc/3@8ace0c61
[2025-06-03 11:00:24] (0s)  >>> Created relation Essa::TEssaNodeDefinition#dom#e1989adc/3@8ace0c61 with 6 rows and digest f0ae4821iuc5eti4cfoeulp6pk6.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Essa::TEssaNodeDefinition#96e9ebfe/4@db33fact
[2025-06-03 11:00:24] Evaluating HOP construct<Essa#24e22a14::TEssaDefinition,0> with inputs:
                        6 tuples in Essa::TEssaNodeDefinition#dom#e1989adc/3@8ace0c61
[2025-06-03 11:00:24] (0s)  >>> Created relation Essa::TEssaNodeDefinition#96e9ebfe/4@db33fact with 6 rows and digest 3430a5ho681ieqv8l6fg6gvojre.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/5@457885ml
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/5@457885ml with 7 rows and digest 446a77g6lv59knpt231sioqjl9d.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/2@4f1599to
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/2@4f1599to with 5 rows and digest ac5857a9skogv90smkidungojn0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b/2@i1#9473axuj (iteration 1)
[2025-06-03 11:00:24] (0s) 			 - SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b_delta has 5 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@i1#9473awuj (iteration 1)
[2025-06-03 11:00:24] (0s) Empty delta for SsaCompute::EssaDefinitions::phiNode/2#4864b30d_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@i2#9473awuj (iteration 2)
[2025-06-03 11:00:24] (0s) Empty delta for SsaCompute::EssaDefinitions::phiNode/2#4864b30d_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Discarded freshly computed SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@9473awuj, preempted by a cache hit with digest THIS-RELATION-HAS-NO-TUPLES
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b/2@9473axuj with 5 rows and digest ac5857a9skogv90smkidungojn0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Essa::TEssaDefinition#4c062f56/1@8d6b97js
[2025-06-03 11:00:24] (0s)  >>> Created relation Essa::TEssaDefinition#4c062f56/1@8d6b97js with 7 rows and digest 2681fe98brgn6oaonf7raenuo1a.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate Essa::TEssaDefinition#4c062f56  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_Essa::TEssaDefinition#4c062f56/1@918595pb with 7 rows and digest 2681fe98brgn6oaonf7raenuo1a.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefRank/4#ea13f11c/4@91d805ha
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::ssaDefRank/4#ea13f11c/4@91d805ha with 7 rows and digest eeeea0c0924l5a6tq27rt822cde.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#3_0122#max_term/4@4143aaec
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48#3_0122#max_term/4@4143aaec with 28 rows and digest 17d8e629jot1m2kncmophoba8nc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::lastRank/2#6cfcb19d/3@53614d7d
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::lastRank/2#6cfcb19d/3@53614d7d with 9 rows and digest 81fbbecb70mfu542uqsr150sk97.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::ssaDefRank/4#ea13f11c/3@9824f5bv
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaCompute::SsaComputeImpl::ssaDefRank/4#ea13f11c/3@9824f5bv with 7 rows and digest 7b2414bmuv9jr6g409s4n48nls4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@i1#8372bcfa (iteration 1)
[2025-06-03 11:00:24] (0s) 			 - SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_delta has 7 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@i2#8372bcfa (iteration 2)
[2025-06-03 11:00:24] (0s) 			 - SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_delta has 7 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@i3#8372bcfa (iteration 3)
[2025-06-03 11:00:24] (0s) 			 - SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_delta has 4 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@i4#8372bcfa (iteration 4)
[2025-06-03 11:00:24] (0s) 			 - SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_delta has 3 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@i5#8372bcfa (iteration 5)
[2025-06-03 11:00:24] (0s) Empty delta for SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@8372bcfa with 21 rows and digest 08d3f2g7feth8bcn1f31ihb5475.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_0132#join_rhs/4@b09cddj8
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee_0132#join_rhs/4@b09cddj8 with 21 rows and digest 95c7ceqvjvkah6vutfe5vspfd57.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Essa::TEssaNodeDefinition#96e9ebfe_1203#join_rhs/4@ad9db5t4
[2025-06-03 11:00:24] (0s)  >>> Created relation Essa::TEssaNodeDefinition#96e9ebfe_1203#join_rhs/4@ad9db5t4 with 6 rows and digest af7c16me0jsnsv7cu3edpusu7r1.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Essa::EssaNodeDefinition.definedBy/2#dispred#88969aa9/3@76067dst
[2025-06-03 11:00:24] (0s)  >>> Created relation Essa::EssaNodeDefinition.definedBy/2#dispred#88969aa9/3@76067dst with 6 rows and digest daa08duhr29l7cvv5h1s2ucdif5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::definesAt/4#68130204/4@8c44424q
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::AdjacentUses::definesAt/4#68130204/4@8c44424q with 6 rows and digest ecf754n7djv07tflaa42v6u4r40.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::Liveness::liveAtEntry/2#bab3ea7c  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::Liveness::liveAtEntry/2#bab3ea7c/2@6601ffvo with 4 rows and digest eb5423eqpgrlj4t7aqcr9p123g1.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/4@a2d696uu with 28 rows and digest 0417e35kkqc0rg6tp70ed6vmkoc.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::defRank/4#f608ea69  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::defRank/4#f608ea69/4@7f1611ue with 8 rows and digest 839f054q5dm2v2eu5dn89o50f2f.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::lastRank/2#6cfcb19d  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::lastRank/2#6cfcb19d/3@0891d874 with 9 rows and digest 81fbbecb70mfu542uqsr150sk97.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b/2@ae9182bm with 5 rows and digest ac5857a9skogv90smkidungojn0.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/5@2ba91fut with 7 rows and digest 446a77g6lv59knpt231sioqjl9d.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::EssaDefinitions::variableDefinition/5#32b5d84f  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::EssaDefinitions::variableDefinition/5#32b5d84f/5@5f1279gb with 6 rows and digest d4771e0f1l5ejuplaa8p29flca9.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::EssaDefinitions::variableRefinement/5#4e891107  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::EssaDefinitions::variableRefinement/5#4e891107/5@95e977ab with 1 rows and digest b3d7de0gs9kop771cl5nf6ki0ub.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@404efe5b with 21 rows and digest 08d3f2g7feth8bcn1f31ihb5475.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate Essa::TEssaNodeDefinition#96e9ebfe  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_Essa::TEssaNodeDefinition#96e9ebfe/4@a811d0mp with 6 rows and digest 3430a5ho681ieqv8l6fg6gvojre.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate Essa::TEssaNodeRefinement#8de2de42  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_Essa::TEssaNodeRefinement#8de2de42/4@3787696p with 1 rows and digest 2f6ae1f2ph7h4qua0geof0vcnh9.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::AdjacentUses::definesAt/4#68130204  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::AdjacentUses::definesAt/4#68130204/4@388909lj with 6 rows and digest ecf754n7djv07tflaa42v6u4r40.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/4@7fee275b
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/4@7fee275b with 14 rows and digest 4b2205876ddupaaa98an5i9uev0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/3@5a2c648u
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/3@5a2c648u with 14 rows and digest 8ba035pkll3p13f1ffap7qu1ti5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableUse/4#da62dc30_201#join_rhs/3@f3e250ah
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::variableUse/4#da62dc30_201#join_rhs/3@f3e250ah with 20 rows and digest 0e1124mro34t9p0vep9h2cprb77.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6/4@01ed91u6
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6/4@01ed91u6 with 14 rows and digest 4b2205876ddupaaa98an5i9uev0.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6/4@2dbd699l with 14 rows and digest 4b2205876ddupaaa98an5i9uev0.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/4@f3e4d889 with 14 rows and digest 4b2205876ddupaaa98an5i9uev0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19_SsaCompute::SsaComputeImpl::variableDefine/4__#rank_range/3@c11f6av9
[2025-06-03 11:00:24] (0s)  >>> Created relation _SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19_SsaCompute::SsaComputeImpl::variableDefine/4__#rank_range/3@c11f6av9 with 11 rows and digest ff576b86b97i71coisv33rr8cnc.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19_SsaCompute::SsaComputeImpl::variableDefine/4__#rank_term/4@4acd0dnd
[2025-06-03 11:00:24] (0s)  >>> Created relation _SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19_SsaCompute::SsaComputeImpl::variableDefine/4__#rank_term/4@4acd0dnd with 11 rows and digest 716185n6kshmdc29qhpno27jti0.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/4@da356cil
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/4@da356cil with 11 rows and digest 3d32b8ptan339jadclutbcq2hj7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/3@2ff57dsj
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/3@2ff57dsj with 11 rows and digest af87bc4qjjf8b0pmqavq7q1kp7e.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate project#SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb_0122#max_term/4@a05a2ed4
[2025-06-03 11:00:24] (0s)  >>> Created relation project#SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb_0122#max_term/4@a05a2ed4 with 11 rows and digest 25b6a7fu4b272ehhtqha2d221e7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::lastSourceUseRank/2#062a0026/3@dd0b0c0d
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::AdjacentUses::lastSourceUseRank/2#062a0026/3@dd0b0c0d with 8 rows and digest 742754fn4ut1mm1ujk7issr59j3.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5/5@62aca43k
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5/5@62aca43k with 3 rows and digest f98b46cnjp924q5buggn2r49m0d.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::definesAt/4#68130204_1230#join_rhs/4@8e8a3ds2
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::AdjacentUses::definesAt/4#68130204_1230#join_rhs/4@8e8a3ds2 with 6 rows and digest 3430a5ho681ieqv8l6fg6gvojre.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5_03412#join_rhs/5@ef7769gk
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5_03412#join_rhs/5@ef7769gk with 3 rows and digest 3ccf0emhajknpkqsouuqqpcds98.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::firstUse/2#ca053ce0/2@i1#e0b6f1ue (iteration 1)
[2025-06-03 11:00:24] (0s) 			 - SsaCompute::AdjacentUses::firstUse/2#ca053ce0_delta has 1 rows (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::firstUse/2#ca053ce0/2@i2#e0b6f1ue (iteration 2)
[2025-06-03 11:00:24] (0s) Empty delta for SsaCompute::AdjacentUses::firstUse/2#ca053ce0_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:00:24] (0s) Accumulating deltas
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::AdjacentUses::firstUse/2#ca053ce0/2@e0b6f1ue with 1 rows and digest e6d714nr9036rh85dhevjukh6r7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::adjacentRefUse/4#6e481bc3/4@9b26d33c
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::AdjacentUses::adjacentRefUse/4#6e481bc3/4@9b26d33c with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that SsaCompute::AdjacentUses::adjacentUseUseSameVar/2#2eafd7db/2@7f67651f is empty, due to SsaCompute::AdjacentUses::adjacentRefUse/4#6e481bc3/4@9b26d33c.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaCompute::AdjacentUses::adjacentUseUseSameVar/2#2eafd7db/2@0dcc16hn is empty, due to SsaCompute::AdjacentUses::adjacentUseUseSameVar/2#2eafd7db/2@7f67651f.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@c7a44a78
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@c7a44a78 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:00:24] (0s) Inferred that boundedFastTC:SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e:SsaCompute::AdjacentUses::firstUse/2#ca053ce0_1#higher_order_body/2@06714dia is empty, due to SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@c7a44a78.
[2025-06-03 11:00:24] (0s) Inferred that cached_SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@1fd2fe9p is empty, due to SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@c7a44a78.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5/5@4359219o with 3 rows and digest f98b46cnjp924q5buggn2r49m0d.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/4@99c70bvp with 11 rows and digest 3d32b8ptan339jadclutbcq2hj7.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::AdjacentUses::firstUse/2#ca053ce0  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::AdjacentUses::firstUse/2#ca053ce0/2@b3437a3s with 1 rows and digest e6d714nr9036rh85dhevjukh6r7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::AdjacentUses::useOfDef/2#16a7ee50/2@6b079csq
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::AdjacentUses::useOfDef/2#16a7ee50/2@6b079csq with 1 rows and digest e6d714nr9036rh85dhevjukh6r7.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::AdjacentUses::useOfDef/2#16a7ee50/2@c0f71efh with 1 rows and digest e6d714nr9036rh85dhevjukh6r7.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_#concat_range/3@9d27916p
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_#concat_range/3@9d27916p with 2 rows and digest c2fa7doddscmvuh5cimu6mfavj5.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_1_#concat_term/5@8c3c3eg0
[2025-06-03 11:00:24] (0s)  >>> Created relation AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_1_#concat_term/5@8c3c3eg0 with 2 rows and digest 3423fe7sis7nfqfc5e19j99dnk4.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate _AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_#concat_range_AstGenerated::Scope_.toString/0#d__#join_rhs/2@3d8906nc
[2025-06-03 11:00:24] (0s)  >>> Created relation _AstGenerated::Scope_.toString/0#dispred#8b6e5a05_01_#concat_range_AstGenerated::Scope_.toString/0#d__#join_rhs/2@3d8906nc with 2 rows and digest 100c26l9u38v2ndvnh1pc7rpl62.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate Flow::ControlFlowNode.toString/0#dispred#e1af144b/2@2e40ddqr
[2025-06-03 11:00:24] (0s)  >>> Created relation Flow::ControlFlowNode.toString/0#dispred#e1af144b/2@2e40ddqr with 16 rows and digest 3812beo9i3tjlu7bncdslkfm9bf.
[2025-06-03 11:00:24] (0s) Promoting strings for predicate Flow::ControlFlowNode.toString/0#dispred#e1af144b
[2025-06-03 11:00:24] (0s) Promoted strings in predicate Flow::ControlFlowNode.toString/0#dispred#e1af144b in memory, took 0ms
[2025-06-03 11:00:24] (0s) Saving stringpool to save strings from predicate Flow::ControlFlowNode.toString/0#dispred#e1af144b
[2025-06-03 11:00:24] (0s) Saved stringpool to save strings from predicate Flow::ControlFlowNode.toString/0#dispred#e1af144b, took 0ms
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_Flow::ControlFlowNode.toString/0#dispred#e1af144b/2@54b666h0 with 16 rows and digest 3812beo9i3tjlu7bncdslkfm9bf.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaComputeImpl::variableUse/4#da62dc30_0213#join_rhs/4@ebef90hv
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaComputeImpl::variableUse/4#da62dc30_0213#join_rhs/4@ebef90hv with 20 rows and digest 21376bt3c48ct1ocq1taod5mb90.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6_0312#join_rhs/4@dedab2lb
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6_0312#join_rhs/4@dedab2lb with 14 rows and digest cae94ellfuneennvjcakk8glfle.
[2025-06-03 11:00:24] (0s) Starting to evaluate predicate SsaCompute::SsaDefinitions::reachesExit/3#a534505a/3@a990156h
[2025-06-03 11:00:24] (0s)  >>> Created relation SsaCompute::SsaDefinitions::reachesExit/3#a534505a/3@a990156h with 5 rows and digest dce384d4jqh5q6li32amivi3fbf.
[2025-06-03 11:00:24] (0s) No need to promote strings for predicate SsaCompute::SsaDefinitions::reachesExit/3#a534505a  as it does not contain computed strings.
[2025-06-03 11:00:24] (0s)  >>> Created relation cached_SsaCompute::SsaDefinitions::reachesExit/3#a534505a/3@cbea635k with 5 rows and digest dce384d4jqh5q6li32amivi3fbf.
[2025-06-03 11:00:24] (0s) Query done
[2025-06-03 11:00:24] (0s) Sequence stamp origin is -6042294888314672310
[2025-06-03 11:00:24] (0s) Pausing evaluation to sync to disk at sequence stamp o+0
[2025-06-03 11:00:24] (0s) Unpausing evaluation
[2025-06-03 11:00:24] Evaluation of E:\advance_javascript\codeQL\7\queries\python-problm-1.ql produced BQRS results.
[2025-06-03 11:00:24] [PROGRESS] execute queries> [1/1 eval 873ms] Evaluation done; writing results to python-security-queries\queries\python-problm-1.bqrs.
[2025-06-03 11:00:24] [PROGRESS] execute queries> Shutting down query evaluator.
[2025-06-03 11:00:24] Pausing evaluation to close the cache at sequence stamp o+1
[2025-06-03 11:00:24] Doing closing disk-cache trim now.
[2025-06-03 11:00:24] After trimming, disk cache uses 20.02kiB.
[2025-06-03 11:00:24] Unpausing evaluation
[2025-06-03 11:00:24] Exiting with code 0
