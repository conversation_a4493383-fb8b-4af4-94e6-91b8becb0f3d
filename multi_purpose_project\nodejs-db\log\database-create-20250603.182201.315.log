[2025-06-03 18:22:01] This is codeql database create nodejs-db --language=javascript --source-root=e:\advance_javascript\codeQL\7\multi_purpose_project --overwrite
[2025-06-03 18:22:01] Log file was started late.
[2025-06-03 18:22:01] [PROGRESS] database create> Initializing database at E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db.
[2025-06-03 18:22:01] Running plumbing command: codeql database init --overwrite --language=javascript --extractor-options-verbosity=1 --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --source-root=E:\advance_javascript\codeQL\7\multi_purpose_project --allow-missing-source-root=false --allow-already-existing -- E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db
[2025-06-03 18:22:01] Calling plumbing command: codeql resolve languages --extractor-options-verbosity=1 --format=betterjson
[2025-06-03 18:22:01] [DETAILS] resolve languages> Scanning for [codeql-extractor.yml] from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\.codeqlmanifest.json
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\actions\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\cpp\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csv\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\go\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\html\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\java\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\properties\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\ruby\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\swift\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\xml\codeql-extractor.yml.
[2025-06-03 18:22:01] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\codeql-extractor.yml.
[2025-06-03 18:22:02] Plumbing command codeql resolve languages completed:
                      {
                        "aliases" : {
                          "c" : "cpp",
                          "c++" : "cpp",
                          "c-c++" : "cpp",
                          "c-cpp" : "cpp",
                          "c#" : "csharp",
                          "java-kotlin" : "java",
                          "kotlin" : "java",
                          "javascript-typescript" : "javascript",
                          "typescript" : "javascript"
                        },
                        "extractors" : {
                          "actions" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\actions",
                              "extractor_options" : { }
                            }
                          ],
                          "cpp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\cpp",
                              "extractor_options" : {
                                "scale_timeouts" : {
                                  "title" : "Value to scale compiler introspection timeouts with",
                                  "description" : "The extractor attempts to determine what compiler the source code being extracted is compiled with. To this end the extractor makes additional calls to the compiler, some of which are expected to return within a certain fixed time (either 10s or 15s). On some systems that are under high load this time might be too short, and can be scaled up using this option.\n",
                                  "type" : "string",
                                  "pattern" : "[0-9]+"
                                },
                                "log_verbosity" : {
                                  "title" : "Verbosity of the extractor logging",
                                  "description" : "Set the verbosity of the extractor logging to 'quiet' (0), 'normal' (1), 'chatty' (2), or 'noisy' (3). The default is 'normal'.\n",
                                  "type" : "string",
                                  "pattern" : "[0-3]"
                                }
                              }
                            }
                          ],
                          "csharp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csharp",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'brotli' (the default, to write brotli-compressed TRAP), 'gzip', and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip|brotli)$"
                                    }
                                  }
                                },
                                "buildless" : {
                                  "title" : "DEPRECATED - Whether to use buildless (standalone) extraction.",
                                  "description" : "DEPRECATED: Use `--build-mode none` instead.\nA value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the extractor. The supported levels are (in order of increasing verbosity):\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "binlog" : {
                                  "title" : "Binlog",
                                  "description" : "[EXPERIMENTAL] The value is a path to the MsBuild binary log file that should be extracted. This option only works when `--build-mode none` is also specified.\n",
                                  "type" : "array"
                                }
                              }
                            }
                          ],
                          "csv" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csv"
                            }
                          ],
                          "go" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\go",
                              "extractor_options" : {
                                "extract_tests" : {
                                  "title" : "Whether to include Go test files in the CodeQL database.",
                                  "description" : "A value indicating whether Go test files should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "extract_vendor_dirs" : {
                                  "title" : "Whether to include Go vendor directories in the CodeQL database.",
                                  "description" : "A value indicating whether Go vendor directories should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "html" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\html"
                            }
                          ],
                          "java" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\java",
                              "extractor_options" : {
                                "exclude" : {
                                  "title" : "A glob excluding files from analysis.",
                                  "description" : "A glob indicating what files to exclude from the analysis. This accepts glob patterns that are supported by Java's 'getPathMatcher' implementation.\n",
                                  "type" : "string"
                                },
                                "add_prefer_source" : {
                                  "title" : "Whether to always prefer source files over class files.",
                                  "description" : "A value indicating whether source files should be preferred over class files. If set to 'true', the extraction adds '-Xprefer:source' to the javac command line. If set to 'false', the extraction uses the default javac behavior ('-Xprefer:newer'). The default is 'true'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless" : {
                                  "title" : "Whether to use buildless (standalone) extraction (experimental).",
                                  "description" : "A value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless_dependency_dir" : {
                                  "title" : "The path where buildless (standalone) extraction should keep dependencies.",
                                  "description" : "If set, the buildless (standalone) extractor will store dependencies in this directory.\n",
                                  "type" : "string"
                                },
                                "minimize_dependency_jars" : {
                                  "title" : "Whether to rewrite and minimize downloaded JAR dependencies (experimental).",
                                  "description" : "If 'true', JAR dependencies downloaded during extraction will be rewritten to remove unneeded data, such as method bodies. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "javascript" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\javascript",
                              "extractor_options" : {
                                "skip_types" : {
                                  "title" : "Skip type extraction for TypeScript",
                                  "description" : "Whether to skip the extraction of types in a TypeScript application",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "properties" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\properties"
                            }
                          ],
                          "python" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\python",
                              "extractor_options" : {
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Python extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the CodeQL Python extractor.\nThe supported levels are (in order of increasing verbosity):\n\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "python_executable_name" : {
                                  "title" : "Controls the name of the Python executable used by the Python extractor.",
                                  "description" : "The Python extractor uses platform-dependent heuristics to determine the name of the Python executable to use. Specifying a value for this option overrides the name of the Python executable used by the extractor. Accepted values are py, python and python3. Use this setting with caution, the Python extractor requires Python 3 to run.\n",
                                  "type" : "string",
                                  "pattern" : "^(py|python|python3)$"
                                }
                              }
                            }
                          ],
                          "ruby" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\ruby",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'gzip' (the default, to write gzip-compressed TRAP) and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip)$"
                                    }
                                  }
                                }
                              }
                            }
                          ],
                          "swift" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\swift"
                            }
                          ],
                          "xml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\xml"
                            }
                          ],
                          "yaml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\yaml"
                            }
                          ]
                        }
                      }
[2025-06-03 18:22:02] [PROGRESS] database init> Calculating baseline information in E:\advance_javascript\codeQL\7\multi_purpose_project
[2025-06-03 18:22:02] [SPAMMY] database init> Ignoring the following directories when processing baseline information: .git, .hg, .svn.
[2025-06-03 18:22:02] [DETAILS] database init> Running command in E:\advance_javascript\codeQL\7\multi_purpose_project: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\scc.exe --by-file --exclude-dir .git,.hg,.svn --format json --no-large --no-min .
[2025-06-03 18:22:02] [SPAMMY] database init> Found 1 baseline files for csharp.
[2025-06-03 18:22:02] [PROGRESS] database init> Calculated baseline information for languages: csharp (133ms).
[2025-06-03 18:22:02] [PROGRESS] database init> Resolving extractor javascript.
[2025-06-03 18:22:02] [DETAILS] database init> Found candidate extractor root for javascript: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript.
[2025-06-03 18:22:02] [PROGRESS] database init> Successfully loaded extractor JavaScript/TypeScript (javascript) from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript.
[2025-06-03 18:22:02] [PROGRESS] database init> Created skeleton CodeQL database at E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db. This in-progress database is ready to be populated by an extractor.
[2025-06-03 18:22:02] Plumbing command codeql database init completed.
[2025-06-03 18:22:02] [PROGRESS] database create> Running build command: []
[2025-06-03 18:22:02] Running plumbing command: codeql database trace-command --working-dir=E:\advance_javascript\codeQL\7\multi_purpose_project --index-traceless-dbs --no-db-cluster -- E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db
[2025-06-03 18:22:02] Using autobuild script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\autobuild.cmd.
[2025-06-03 18:22:02] [PROGRESS] database trace-command> Running command in E:\advance_javascript\codeQL\7\multi_purpose_project: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\tools\autobuild.cmd]
[2025-06-03 18:22:03] [build-stdout] Single-threaded extraction.
[2025-06-03 18:22:03] [build-stdout] Extracting E:\advance_javascript\codeQL\7\multi_purpose_project\queries\qlpack.yml
[2025-06-03 18:22:03] [build-stdout] Done extracting E:\advance_javascript\codeQL\7\multi_purpose_project\queries\qlpack.yml (94 ms)
[2025-06-03 18:22:03] [build-stderr] Only found JavaScript or TypeScript files that were empty or contained syntax errors.
[2025-06-03 18:22:03] Plumbing command codeql database trace-command completed.
[2025-06-03 18:22:03] [PROGRESS] database create> Finalizing database at E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db.
[2025-06-03 18:22:03] Running plumbing command: codeql database finalize --no-db-cluster -- E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db
[2025-06-03 18:22:03] [PROGRESS] database finalize> Running TRAP import for CodeQL database at E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db...
[2025-06-03 18:22:03] Running plumbing command: codeql dataset import --dbscheme=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\semmlecode.javascript.dbscheme -- E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\db-javascript E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\trap\javascript
[2025-06-03 18:22:04] Clearing disk cache since the version file E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\db-javascript\default\cache\version does not exist
[2025-06-03 18:22:04] Tuple pool not found. Clearing relations with cached strings
[2025-06-03 18:22:04] Trimming disk cache at E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\db-javascript\default\cache in mode clear.
[2025-06-03 18:22:04] Sequence stamp origin is -6042180933940666027
[2025-06-03 18:22:04] Pausing evaluation to hard-clear memory at sequence stamp o+0
[2025-06-03 18:22:04] Unpausing evaluation
[2025-06-03 18:22:04] Pausing evaluation to quickly trim disk at sequence stamp o+1
[2025-06-03 18:22:04] Unpausing evaluation
[2025-06-03 18:22:04] Pausing evaluation to zealously trim disk at sequence stamp o+2
[2025-06-03 18:22:04] Unpausing evaluation
[2025-06-03 18:22:04] Trimming completed (15ms): Purged everything.
[2025-06-03 18:22:04] Scanning for files in E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\trap\javascript
[2025-06-03 18:22:04] Found 2 TRAP files (1.03 KiB)
[2025-06-03 18:22:04] [PROGRESS] dataset import> Importing TRAP files
[2025-06-03 18:22:04] Importing qlpack.yml.trap.gz (1 of 2)
[2025-06-03 18:22:04] Importing metadata.trap.gz (2 of 2)
[2025-06-03 18:22:04] [PROGRESS] dataset import> Merging relations
[2025-06-03 18:22:04] Merging 1 fragment for 'files'.
[2025-06-03 18:22:04] Merged 28 bytes for 'files'.
[2025-06-03 18:22:04] Merging 1 fragment for 'folders'.
[2025-06-03 18:22:04] Merged 36 bytes for 'folders'.
[2025-06-03 18:22:04] Merging 1 fragment for 'containerparent'.
[2025-06-03 18:22:04] Merged 37 bytes for 'containerparent'.
[2025-06-03 18:22:04] Merging 1 fragment for 'locations_default'.
[2025-06-03 18:22:04] Merged 128 bytes for 'locations_default'.
[2025-06-03 18:22:04] Merging 1 fragment for 'hasLocation'.
[2025-06-03 18:22:04] Merged 27 bytes for 'hasLocation'.
[2025-06-03 18:22:04] Merging 1 fragment for 'extraction_data'.
[2025-06-03 18:22:04] Merged 47 bytes for 'extraction_data'.
[2025-06-03 18:22:04] Merging 1 fragment for 'yaml_scalars'.
[2025-06-03 18:22:04] Merged 61 bytes for 'yaml_scalars'.
[2025-06-03 18:22:04] Merging 1 fragment for 'yaml'.
[2025-06-03 18:22:04] Merged 121 bytes for 'yaml'.
[2025-06-03 18:22:04] Merging 1 fragment for 'yaml_locations'.
[2025-06-03 18:22:04] Merged 50 bytes for 'yaml_locations'.
[2025-06-03 18:22:04] Merging 1 fragment for 'numlines'.
[2025-06-03 18:22:04] Merged 47 bytes for 'numlines'.
[2025-06-03 18:22:04] Merging 1 fragment for 'filetype'.
[2025-06-03 18:22:04] Merged 27 bytes for 'filetype'.
[2025-06-03 18:22:04] Merging 1 fragment for 'extraction_time'.
[2025-06-03 18:22:04] Merged 131 bytes for 'extraction_time'.
[2025-06-03 18:22:04] Merging 1 fragment for 'sourceLocationPrefix'.
[2025-06-03 18:22:04] Merged 17 bytes for 'sourceLocationPrefix'.
[2025-06-03 18:22:04] Saving string and id pools to disk.
[2025-06-03 18:22:04] Finished importing TRAP files.
[2025-06-03 18:22:04] Read 4.05 KiB of uncompressed TRAP data.
[2025-06-03 18:22:04] Uncompressed relation data size: 1.23 KiB
[2025-06-03 18:22:04] Relation data size: 757.00 B (merge rate: 14.10 KiB/s)
[2025-06-03 18:22:04] String pool size: 2.05 MiB
[2025-06-03 18:22:04] ID pool size: 1.02 MiB
[2025-06-03 18:22:04] [PROGRESS] dataset import> Finished writing database (relations: 757.00 B; string pool: 2.05 MiB).
[2025-06-03 18:22:04] Pausing evaluation to close the cache at sequence stamp o+29
[2025-06-03 18:22:04] The disk cache is freshly trimmed; leave it be.
[2025-06-03 18:22:04] Unpausing evaluation
[2025-06-03 18:22:04] Plumbing command codeql dataset import completed.
[2025-06-03 18:22:04] [PROGRESS] database finalize> TRAP import complete (1.2s).
[2025-06-03 18:22:04] Running plumbing command: codeql database cleanup -- E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db
[2025-06-03 18:22:05] [PROGRESS] database cleanup> Cleaning up existing TRAP files after import...
[2025-06-03 18:22:05] [PROGRESS] database cleanup> TRAP files cleaned up (6ms).
[2025-06-03 18:22:05] [PROGRESS] database cleanup> Cleaning up scratch directory...
[2025-06-03 18:22:05] [PROGRESS] database cleanup> Scratch directory cleaned up (1ms).
[2025-06-03 18:22:05] Running plumbing command: codeql dataset cleanup -- E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\db-javascript
[2025-06-03 18:22:05] [PROGRESS] dataset cleanup> Cleaning up dataset in E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\db-javascript.
[2025-06-03 18:22:05] Trimming disk cache at E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\db-javascript\default\cache in mode trim.
[2025-06-03 18:22:05] Sequence stamp origin is -6042180929783367035
[2025-06-03 18:22:05] Pausing evaluation to quickly trim memory at sequence stamp o+0
[2025-06-03 18:22:05] Unpausing evaluation
[2025-06-03 18:22:05] Pausing evaluation to zealously trim disk at sequence stamp o+1
[2025-06-03 18:22:05] Unpausing evaluation
[2025-06-03 18:22:05] Trimming completed (2ms): Trimmed disposable data from cache.
[2025-06-03 18:22:05] Pausing evaluation to close the cache at sequence stamp o+2
[2025-06-03 18:22:05] The disk cache is freshly trimmed; leave it be.
[2025-06-03 18:22:05] Unpausing evaluation
[2025-06-03 18:22:05] [PROGRESS] dataset cleanup> Trimmed disposable data from cache.
[2025-06-03 18:22:05] [PROGRESS] dataset cleanup> Finalizing dataset in E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\db-javascript
[2025-06-03 18:22:05] [DETAILS] dataset cleanup> Finished deleting ID pool from E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\db-javascript (1ms).
[2025-06-03 18:22:05] Plumbing command codeql dataset cleanup completed.
[2025-06-03 18:22:05] Plumbing command codeql database cleanup completed with status 0.
[2025-06-03 18:22:05] [PROGRESS] database finalize> Finished zipping source archive (377.00 B).
[2025-06-03 18:22:05] Plumbing command codeql database finalize completed.
[2025-06-03 18:22:05] [PROGRESS] database create> Successfully created database at E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db.
[2025-06-03 18:22:05] Terminating normally.
