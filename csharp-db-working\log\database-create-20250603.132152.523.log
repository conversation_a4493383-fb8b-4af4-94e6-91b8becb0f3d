[2025-06-03 13:21:52] This is codeql database create csharp-db-working --language=csharp --source-root=CSharpTest --command=dotnet clean && dotnet build --overwrite
[2025-06-03 13:21:52] Log file was started late.
[2025-06-03 13:21:52] [PROGRESS] database create> Initializing database at E:\advance_javascript\codeQL\7\csharp-db-working.
[2025-06-03 13:21:52] Running plumbing command: codeql database init --overwrite --language=csharp --extractor-options-verbosity=1 --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --source-root=E:\advance_javascript\codeQL\7\CSharpTest --allow-missing-source-root=false --allow-already-existing -- E:\advance_javascript\codeQL\7\csharp-db-working
[2025-06-03 13:21:52] Calling plumbing command: codeql resolve languages --extractor-options-verbosity=1 --format=betterjson
[2025-06-03 13:21:52] [DETAILS] resolve languages> Scanning for [codeql-extractor.yml] from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\.codeqlmanifest.json
[2025-06-03 13:21:52] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\actions\codeql-extractor.yml.
[2025-06-03 13:21:52] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\cpp\codeql-extractor.yml.
[2025-06-03 13:21:52] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\codeql-extractor.yml.
[2025-06-03 13:21:52] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csv\codeql-extractor.yml.
[2025-06-03 13:21:52] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\go\codeql-extractor.yml.
[2025-06-03 13:21:52] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\html\codeql-extractor.yml.
[2025-06-03 13:21:52] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\java\codeql-extractor.yml.
[2025-06-03 13:21:52] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\codeql-extractor.yml.
[2025-06-03 13:21:53] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\properties\codeql-extractor.yml.
[2025-06-03 13:21:53] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\codeql-extractor.yml.
[2025-06-03 13:21:53] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\ruby\codeql-extractor.yml.
[2025-06-03 13:21:53] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\swift\codeql-extractor.yml.
[2025-06-03 13:21:53] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\xml\codeql-extractor.yml.
[2025-06-03 13:21:53] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\codeql-extractor.yml.
[2025-06-03 13:21:53] Plumbing command codeql resolve languages completed:
                      {
                        "aliases" : {
                          "c" : "cpp",
                          "c++" : "cpp",
                          "c-c++" : "cpp",
                          "c-cpp" : "cpp",
                          "c#" : "csharp",
                          "java-kotlin" : "java",
                          "kotlin" : "java",
                          "javascript-typescript" : "javascript",
                          "typescript" : "javascript"
                        },
                        "extractors" : {
                          "actions" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\actions",
                              "extractor_options" : { }
                            }
                          ],
                          "cpp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\cpp",
                              "extractor_options" : {
                                "scale_timeouts" : {
                                  "title" : "Value to scale compiler introspection timeouts with",
                                  "description" : "The extractor attempts to determine what compiler the source code being extracted is compiled with. To this end the extractor makes additional calls to the compiler, some of which are expected to return within a certain fixed time (either 10s or 15s). On some systems that are under high load this time might be too short, and can be scaled up using this option.\n",
                                  "type" : "string",
                                  "pattern" : "[0-9]+"
                                },
                                "log_verbosity" : {
                                  "title" : "Verbosity of the extractor logging",
                                  "description" : "Set the verbosity of the extractor logging to 'quiet' (0), 'normal' (1), 'chatty' (2), or 'noisy' (3). The default is 'normal'.\n",
                                  "type" : "string",
                                  "pattern" : "[0-3]"
                                }
                              }
                            }
                          ],
                          "csharp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csharp",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'brotli' (the default, to write brotli-compressed TRAP), 'gzip', and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip|brotli)$"
                                    }
                                  }
                                },
                                "buildless" : {
                                  "title" : "DEPRECATED - Whether to use buildless (standalone) extraction.",
                                  "description" : "DEPRECATED: Use `--build-mode none` instead.\nA value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the extractor. The supported levels are (in order of increasing verbosity):\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "binlog" : {
                                  "title" : "Binlog",
                                  "description" : "[EXPERIMENTAL] The value is a path to the MsBuild binary log file that should be extracted. This option only works when `--build-mode none` is also specified.\n",
                                  "type" : "array"
                                }
                              }
                            }
                          ],
                          "csv" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csv"
                            }
                          ],
                          "go" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\go",
                              "extractor_options" : {
                                "extract_tests" : {
                                  "title" : "Whether to include Go test files in the CodeQL database.",
                                  "description" : "A value indicating whether Go test files should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "extract_vendor_dirs" : {
                                  "title" : "Whether to include Go vendor directories in the CodeQL database.",
                                  "description" : "A value indicating whether Go vendor directories should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "html" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\html"
                            }
                          ],
                          "java" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\java",
                              "extractor_options" : {
                                "exclude" : {
                                  "title" : "A glob excluding files from analysis.",
                                  "description" : "A glob indicating what files to exclude from the analysis. This accepts glob patterns that are supported by Java's 'getPathMatcher' implementation.\n",
                                  "type" : "string"
                                },
                                "add_prefer_source" : {
                                  "title" : "Whether to always prefer source files over class files.",
                                  "description" : "A value indicating whether source files should be preferred over class files. If set to 'true', the extraction adds '-Xprefer:source' to the javac command line. If set to 'false', the extraction uses the default javac behavior ('-Xprefer:newer'). The default is 'true'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless" : {
                                  "title" : "Whether to use buildless (standalone) extraction (experimental).",
                                  "description" : "A value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless_dependency_dir" : {
                                  "title" : "The path where buildless (standalone) extraction should keep dependencies.",
                                  "description" : "If set, the buildless (standalone) extractor will store dependencies in this directory.\n",
                                  "type" : "string"
                                },
                                "minimize_dependency_jars" : {
                                  "title" : "Whether to rewrite and minimize downloaded JAR dependencies (experimental).",
                                  "description" : "If 'true', JAR dependencies downloaded during extraction will be rewritten to remove unneeded data, such as method bodies. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "javascript" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\javascript",
                              "extractor_options" : {
                                "skip_types" : {
                                  "title" : "Skip type extraction for TypeScript",
                                  "description" : "Whether to skip the extraction of types in a TypeScript application",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "properties" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\properties"
                            }
                          ],
                          "python" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\python",
                              "extractor_options" : {
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Python extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the CodeQL Python extractor.\nThe supported levels are (in order of increasing verbosity):\n\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "python_executable_name" : {
                                  "title" : "Controls the name of the Python executable used by the Python extractor.",
                                  "description" : "The Python extractor uses platform-dependent heuristics to determine the name of the Python executable to use. Specifying a value for this option overrides the name of the Python executable used by the extractor. Accepted values are py, python and python3. Use this setting with caution, the Python extractor requires Python 3 to run.\n",
                                  "type" : "string",
                                  "pattern" : "^(py|python|python3)$"
                                }
                              }
                            }
                          ],
                          "ruby" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\ruby",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'gzip' (the default, to write gzip-compressed TRAP) and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip)$"
                                    }
                                  }
                                }
                              }
                            }
                          ],
                          "swift" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\swift"
                            }
                          ],
                          "xml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\xml"
                            }
                          ],
                          "yaml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\yaml"
                            }
                          ]
                        }
                      }
[2025-06-03 13:21:53] [PROGRESS] database init> Calculating baseline information in E:\advance_javascript\codeQL\7\CSharpTest
[2025-06-03 13:21:53] [SPAMMY] database init> Ignoring the following directories when processing baseline information: .git, .hg, .svn.
[2025-06-03 13:21:53] [DETAILS] database init> Running command in E:\advance_javascript\codeQL\7\CSharpTest: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\scc.exe --by-file --exclude-dir .git,.hg,.svn --format json --no-large --no-min .
[2025-06-03 13:21:53] [SPAMMY] database init> Found 4 baseline files for csharp.
[2025-06-03 13:21:53] [PROGRESS] database init> Calculated baseline information for languages: csharp (109ms).
[2025-06-03 13:21:53] [PROGRESS] database init> Resolving extractor csharp.
[2025-06-03 13:21:53] [DETAILS] database init> Found candidate extractor root for csharp: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp.
[2025-06-03 13:21:53] [PROGRESS] database init> Successfully loaded extractor C# (csharp) from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp.
[2025-06-03 13:21:53] [PROGRESS] database init> Created skeleton CodeQL database at E:\advance_javascript\codeQL\7\csharp-db-working. This in-progress database is ready to be populated by an extractor.
[2025-06-03 13:21:53] Plumbing command codeql database init completed.
[2025-06-03 13:21:53] [PROGRESS] database create> Running build command: [dotnet, clean, &&, dotnet, build]
[2025-06-03 13:21:53] Running plumbing command: codeql database trace-command --working-dir=E:\advance_javascript\codeQL\7\CSharpTest --index-traceless-dbs --no-db-cluster -- E:\advance_javascript\codeQL\7\csharp-db-working dotnet clean && dotnet build
[2025-06-03 13:21:53] [PROGRESS] database trace-command> Running command in E:\advance_javascript\codeQL\7\CSharpTest: [dotnet, clean, &&, dotnet, build]
[2025-06-03 13:21:54] [build-stdout] Build started 03-06-2025 13:21:54.
[2025-06-03 13:21:54] [build-stdout]      1>Project "E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj" on node 1 (Clean target(s)).
[2025-06-03 13:21:54] [build-stdout]      1>CoreClean:
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\bin\Debug\net8.0\CSharpTest.exe".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\bin\Debug\net8.0\CSharpTest.deps.json".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\bin\Debug\net8.0\CSharpTest.runtimeconfig.json".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\bin\Debug\net8.0\CSharpTest.dll".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\bin\Debug\net8.0\CSharpTest.pdb".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\CSharpTest.GeneratedMSBuildEditorConfig.editorconfig".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\CSharpTest.AssemblyInfoInputs.cache".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\CSharpTest.AssemblyInfo.cs".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\CSharpTest.csproj.CoreCompileInputs.cache".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\CSharpTest.dll".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\refint\CSharpTest.dll".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\CSharpTest.pdb".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\CSharpTest.genruntimeconfig.cache".
[2025-06-03 13:21:54] [build-stdout]          Deleting file "E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\ref\CSharpTest.dll".
[2025-06-03 13:21:54] [build-stdout]      1>Done Building Project "E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj" (Clean target(s)).
[2025-06-03 13:21:54] [build-stdout] Build succeeded.
[2025-06-03 13:21:54] [build-stdout]     0 Warning(s)
[2025-06-03 13:21:54] [build-stdout]     0 Error(s)
[2025-06-03 13:21:54] [build-stdout] Time Elapsed 00:00:00.44
[2025-06-03 13:21:55] [build-stdout]   Determining projects to restore...
[2025-06-03 13:21:55] [build-stdout]   All projects are up-to-date for restore.
[2025-06-03 13:22:03] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(20,16): warning CS0219: The variable 'jwt_secret' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:03] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(21,16): warning CS0219: The variable 'encryption_key' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:03] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(24,16): warning CS0219: The variable 'passwordPrompt' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:03] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(25,16): warning CS0219: The variable 'userInput' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:03] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(7,27): warning CS0414: The field 'Program.apiKey' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:03] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(6,27): warning CS0414: The field 'Program.password' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:03] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(8,27): warning CS0414: The field 'Program.secret_token' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:14] [build-stdout]   CSharpTest -> E:\advance_javascript\codeQL\7\CSharpTest\bin\Debug\net8.0\CSharpTest.dll
[2025-06-03 13:22:14] [build-stdout] Build succeeded.
[2025-06-03 13:22:14] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(20,16): warning CS0219: The variable 'jwt_secret' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:14] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(21,16): warning CS0219: The variable 'encryption_key' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:14] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(24,16): warning CS0219: The variable 'passwordPrompt' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:14] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(25,16): warning CS0219: The variable 'userInput' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:14] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(7,27): warning CS0414: The field 'Program.apiKey' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:14] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(6,27): warning CS0414: The field 'Program.password' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:14] [build-stdout] E:\advance_javascript\codeQL\7\CSharpTest\Program.cs(8,27): warning CS0414: The field 'Program.secret_token' is assigned but its value is never used [E:\advance_javascript\codeQL\7\CSharpTest\CSharpTest.csproj]
[2025-06-03 13:22:14] [build-stdout]     7 Warning(s)
[2025-06-03 13:22:14] [build-stdout]     0 Error(s)
[2025-06-03 13:22:14] [build-stdout] Time Elapsed 00:00:19.62
[2025-06-03 13:22:14] Plumbing command codeql database trace-command completed.
[2025-06-03 13:22:14] [PROGRESS] database create> Finalizing database at E:\advance_javascript\codeQL\7\csharp-db-working.
[2025-06-03 13:22:14] Running plumbing command: codeql database finalize --no-db-cluster -- E:\advance_javascript\codeQL\7\csharp-db-working
[2025-06-03 13:22:14] Using pre-finalize script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\tools\pre-finalize.cmd.
[2025-06-03 13:22:14] [PROGRESS] database finalize> Running pre-finalize script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\tools\pre-finalize.cmd in E:\advance_javascript\codeQL\7\CSharpTest.
[2025-06-03 13:22:14] Running plumbing command: codeql database trace-command --working-dir=E:\advance_javascript\codeQL\7\CSharpTest --no-tracing -- E:\advance_javascript\codeQL\7\csharp-db-working C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\tools\pre-finalize.cmd
[2025-06-03 13:22:14] [PROGRESS] database trace-command> Running command in E:\advance_javascript\codeQL\7\CSharpTest: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\tools\pre-finalize.cmd]
[2025-06-03 13:22:17] Plumbing command codeql database trace-command completed.
[2025-06-03 13:22:17] [PROGRESS] database finalize> Running TRAP import for CodeQL database at E:\advance_javascript\codeQL\7\csharp-db-working...
[2025-06-03 13:22:17] Running plumbing command: codeql dataset import --dbscheme=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\semmlecode.csharp.dbscheme -- E:\advance_javascript\codeQL\7\csharp-db-working\db-csharp E:\advance_javascript\codeQL\7\csharp-db-working\trap\csharp
[2025-06-03 13:22:18] Clearing disk cache since the version file E:\advance_javascript\codeQL\7\csharp-db-working\db-csharp\default\cache\version does not exist
[2025-06-03 13:22:18] Tuple pool not found. Clearing relations with cached strings
[2025-06-03 13:22:18] Trimming disk cache at E:\advance_javascript\codeQL\7\csharp-db-working\db-csharp\default\cache in mode clear.
[2025-06-03 13:22:18] Sequence stamp origin is -6042258183278665703
[2025-06-03 13:22:18] Pausing evaluation to hard-clear memory at sequence stamp o+0
[2025-06-03 13:22:18] Unpausing evaluation
[2025-06-03 13:22:18] Pausing evaluation to quickly trim disk at sequence stamp o+1
[2025-06-03 13:22:18] Unpausing evaluation
[2025-06-03 13:22:18] Pausing evaluation to zealously trim disk at sequence stamp o+2
[2025-06-03 13:22:18] Unpausing evaluation
[2025-06-03 13:22:18] Trimming completed (16ms): Purged everything.
[2025-06-03 13:22:18] Scanning for files in E:\advance_javascript\codeQL\7\csharp-db-working\trap\csharp
[2025-06-03 13:22:18] Found 171 TRAP files (7.63 MiB)
[2025-06-03 13:22:18] [PROGRESS] dataset import> Importing TRAP files
[2025-06-03 13:22:18] Importing Microsoft.CSharp.dll.trap.br (1 of 171)
[2025-06-03 13:22:18] Importing Microsoft.VisualBasic.Core.dll.trap.br (2 of 171)
[2025-06-03 13:22:19] Importing Microsoft.VisualBasic.dll.trap.br (3 of 171)
[2025-06-03 13:22:19] Importing Microsoft.Win32.Primitives.dll.trap.br (4 of 171)
[2025-06-03 13:22:19] Importing Microsoft.Win32.Registry.dll.trap.br (5 of 171)
[2025-06-03 13:22:19] Importing System.AppContext.dll.trap.br (6 of 171)
[2025-06-03 13:22:19] Importing System.Buffers.dll.trap.br (7 of 171)
[2025-06-03 13:22:19] Importing System.Collections.Concurrent.dll.trap.br (8 of 171)
[2025-06-03 13:22:19] Importing System.Collections.Immutable.dll.trap.br (9 of 171)
[2025-06-03 13:22:19] Importing System.Collections.NonGeneric.dll.trap.br (10 of 171)
[2025-06-03 13:22:19] Importing System.Collections.Specialized.dll.trap.br (11 of 171)
[2025-06-03 13:22:19] Importing System.Collections.dll.trap.br (12 of 171)
[2025-06-03 13:22:19] Importing System.ComponentModel.Annotations.dll.trap.br (13 of 171)
[2025-06-03 13:22:19] Importing System.ComponentModel.DataAnnotations.dll.trap.br (14 of 171)
[2025-06-03 13:22:19] Importing System.ComponentModel.EventBasedAsync.dll.trap.br (15 of 171)
[2025-06-03 13:22:19] Importing System.ComponentModel.Primitives.dll.trap.br (16 of 171)
[2025-06-03 13:22:19] Importing System.ComponentModel.TypeConverter.dll.trap.br (17 of 171)
[2025-06-03 13:22:19] Importing System.ComponentModel.dll.trap.br (18 of 171)
[2025-06-03 13:22:19] Importing System.Configuration.dll.trap.br (19 of 171)
[2025-06-03 13:22:19] Importing System.Console.dll.trap.br (20 of 171)
[2025-06-03 13:22:19] Importing System.Core.dll.trap.br (21 of 171)
[2025-06-03 13:22:19] Importing System.Data.Common.dll.trap.br (22 of 171)
[2025-06-03 13:22:19] Importing System.Data.DataSetExtensions.dll.trap.br (23 of 171)
[2025-06-03 13:22:19] Importing System.Data.dll.trap.br (24 of 171)
[2025-06-03 13:22:19] Importing System.Diagnostics.Contracts.dll.trap.br (25 of 171)
[2025-06-03 13:22:19] Importing System.Diagnostics.Debug.dll.trap.br (26 of 171)
[2025-06-03 13:22:19] Importing System.Diagnostics.DiagnosticSource.dll.trap.br (27 of 171)
[2025-06-03 13:22:19] Importing System.Diagnostics.FileVersionInfo.dll.trap.br (28 of 171)
[2025-06-03 13:22:19] Importing System.Diagnostics.Process.dll.trap.br (29 of 171)
[2025-06-03 13:22:19] Importing System.Diagnostics.StackTrace.dll.trap.br (30 of 171)
[2025-06-03 13:22:19] Importing System.Diagnostics.TextWriterTraceListener.dll.trap.br (31 of 171)
[2025-06-03 13:22:19] Importing System.Diagnostics.Tools.dll.trap.br (32 of 171)
[2025-06-03 13:22:19] Importing System.Diagnostics.TraceSource.dll.trap.br (33 of 171)
[2025-06-03 13:22:19] Importing System.Diagnostics.Tracing.dll.trap.br (34 of 171)
[2025-06-03 13:22:19] Importing System.Drawing.Primitives.dll.trap.br (35 of 171)
[2025-06-03 13:22:19] Importing System.Drawing.dll.trap.br (36 of 171)
[2025-06-03 13:22:19] Importing System.Dynamic.Runtime.dll.trap.br (37 of 171)
[2025-06-03 13:22:19] Importing System.Formats.Asn1.dll.trap.br (38 of 171)
[2025-06-03 13:22:19] Importing System.Formats.Tar.dll.trap.br (39 of 171)
[2025-06-03 13:22:19] Importing System.Globalization.Calendars.dll.trap.br (40 of 171)
[2025-06-03 13:22:19] Importing System.Globalization.Extensions.dll.trap.br (41 of 171)
[2025-06-03 13:22:19] Importing System.Globalization.dll.trap.br (42 of 171)
[2025-06-03 13:22:19] Importing System.IO.Compression.Brotli.dll.trap.br (43 of 171)
[2025-06-03 13:22:19] Importing System.IO.Compression.FileSystem.dll.trap.br (44 of 171)
[2025-06-03 13:22:19] Importing System.IO.Compression.ZipFile.dll.trap.br (45 of 171)
[2025-06-03 13:22:19] Importing System.IO.Compression.dll.trap.br (46 of 171)
[2025-06-03 13:22:19] Importing System.IO.FileSystem.AccessControl.dll.trap.br (47 of 171)
[2025-06-03 13:22:19] Importing System.IO.FileSystem.DriveInfo.dll.trap.br (48 of 171)
[2025-06-03 13:22:19] Importing System.IO.FileSystem.Primitives.dll.trap.br (49 of 171)
[2025-06-03 13:22:19] Importing System.IO.FileSystem.Watcher.dll.trap.br (50 of 171)
[2025-06-03 13:22:19] Importing System.IO.FileSystem.dll.trap.br (51 of 171)
[2025-06-03 13:22:19] Importing System.IO.IsolatedStorage.dll.trap.br (52 of 171)
[2025-06-03 13:22:19] Importing System.IO.MemoryMappedFiles.dll.trap.br (53 of 171)
[2025-06-03 13:22:19] Importing System.IO.Pipes.AccessControl.dll.trap.br (54 of 171)
[2025-06-03 13:22:19] Importing System.IO.Pipes.dll.trap.br (55 of 171)
[2025-06-03 13:22:19] Importing System.IO.UnmanagedMemoryStream.dll.trap.br (56 of 171)
[2025-06-03 13:22:19] Importing System.IO.dll.trap.br (57 of 171)
[2025-06-03 13:22:19] Importing System.Linq.Expressions.dll.trap.br (58 of 171)
[2025-06-03 13:22:19] Importing System.Linq.Parallel.dll.trap.br (59 of 171)
[2025-06-03 13:22:19] Importing System.Linq.Queryable.dll.trap.br (60 of 171)
[2025-06-03 13:22:19] Importing System.Linq.dll.trap.br (61 of 171)
[2025-06-03 13:22:19] Importing System.Memory.dll.trap.br (62 of 171)
[2025-06-03 13:22:19] Importing System.Net.Http.Json.dll.trap.br (63 of 171)
[2025-06-03 13:22:19] Importing System.Net.Http.dll.trap.br (64 of 171)
[2025-06-03 13:22:19] Importing System.Net.HttpListener.dll.trap.br (65 of 171)
[2025-06-03 13:22:19] Importing System.Net.Mail.dll.trap.br (66 of 171)
[2025-06-03 13:22:19] Importing System.Net.NameResolution.dll.trap.br (67 of 171)
[2025-06-03 13:22:19] Importing System.Net.NetworkInformation.dll.trap.br (68 of 171)
[2025-06-03 13:22:19] Importing System.Net.Ping.dll.trap.br (69 of 171)
[2025-06-03 13:22:19] Importing System.Net.Primitives.dll.trap.br (70 of 171)
[2025-06-03 13:22:19] Importing System.Net.Quic.dll.trap.br (71 of 171)
[2025-06-03 13:22:19] Importing System.Net.Requests.dll.trap.br (72 of 171)
[2025-06-03 13:22:19] Importing System.Net.Security.dll.trap.br (73 of 171)
[2025-06-03 13:22:19] Importing System.Net.ServicePoint.dll.trap.br (74 of 171)
[2025-06-03 13:22:19] Importing System.Net.Sockets.dll.trap.br (75 of 171)
[2025-06-03 13:22:19] Importing System.Net.WebClient.dll.trap.br (76 of 171)
[2025-06-03 13:22:19] Importing System.Net.WebHeaderCollection.dll.trap.br (77 of 171)
[2025-06-03 13:22:19] Importing System.Net.WebProxy.dll.trap.br (78 of 171)
[2025-06-03 13:22:19] Importing System.Net.WebSockets.Client.dll.trap.br (79 of 171)
[2025-06-03 13:22:19] Importing System.Net.WebSockets.dll.trap.br (80 of 171)
[2025-06-03 13:22:19] Importing System.Net.dll.trap.br (81 of 171)
[2025-06-03 13:22:19] Importing System.Numerics.Vectors.dll.trap.br (82 of 171)
[2025-06-03 13:22:19] Importing System.Numerics.dll.trap.br (83 of 171)
[2025-06-03 13:22:19] Importing System.ObjectModel.dll.trap.br (84 of 171)
[2025-06-03 13:22:19] Importing System.Reflection.DispatchProxy.dll.trap.br (85 of 171)
[2025-06-03 13:22:19] Importing System.Reflection.Emit.ILGeneration.dll.trap.br (86 of 171)
[2025-06-03 13:22:19] Importing System.Reflection.Emit.Lightweight.dll.trap.br (87 of 171)
[2025-06-03 13:22:19] Importing System.Reflection.Emit.dll.trap.br (88 of 171)
[2025-06-03 13:22:19] Importing System.Reflection.Extensions.dll.trap.br (89 of 171)
[2025-06-03 13:22:19] Importing System.Reflection.Metadata.dll.trap.br (90 of 171)
[2025-06-03 13:22:20] Importing System.Reflection.Primitives.dll.trap.br (91 of 171)
[2025-06-03 13:22:20] Importing System.Reflection.TypeExtensions.dll.trap.br (92 of 171)
[2025-06-03 13:22:20] Importing System.Reflection.dll.trap.br (93 of 171)
[2025-06-03 13:22:20] Importing System.Resources.Reader.dll.trap.br (94 of 171)
[2025-06-03 13:22:20] Importing System.Resources.ResourceManager.dll.trap.br (95 of 171)
[2025-06-03 13:22:20] Importing System.Resources.Writer.dll.trap.br (96 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.CompilerServices.Unsafe.dll.trap.br (97 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.CompilerServices.VisualC.dll.trap.br (98 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.Extensions.dll.trap.br (99 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.Handles.dll.trap.br (100 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.InteropServices.JavaScript.dll.trap.br (101 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.InteropServices.RuntimeInformation.dll.trap.br (102 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.InteropServices.dll.trap.br (103 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.Intrinsics.dll.trap.br (104 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.Loader.dll.trap.br (105 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.Numerics.dll.trap.br (106 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.Serialization.Formatters.dll.trap.br (107 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.Serialization.Json.dll.trap.br (108 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.Serialization.Primitives.dll.trap.br (109 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.Serialization.Xml.dll.trap.br (110 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.Serialization.dll.trap.br (111 of 171)
[2025-06-03 13:22:20] Importing System.Runtime.dll.trap.br (112 of 171)
[2025-06-03 13:22:20] Importing System.Security.AccessControl.dll.trap.br (113 of 171)
[2025-06-03 13:22:20] Importing System.Security.Claims.dll.trap.br (114 of 171)
[2025-06-03 13:22:20] Importing System.Security.Cryptography.Algorithms.dll.trap.br (115 of 171)
[2025-06-03 13:22:20] Importing System.Security.Cryptography.Cng.dll.trap.br (116 of 171)
[2025-06-03 13:22:20] Importing System.Security.Cryptography.Csp.dll.trap.br (117 of 171)
[2025-06-03 13:22:20] Importing System.Security.Cryptography.Encoding.dll.trap.br (118 of 171)
[2025-06-03 13:22:20] Importing System.Security.Cryptography.OpenSsl.dll.trap.br (119 of 171)
[2025-06-03 13:22:20] Importing System.Security.Cryptography.Primitives.dll.trap.br (120 of 171)
[2025-06-03 13:22:20] Importing System.Security.Cryptography.X509Certificates.dll.trap.br (121 of 171)
[2025-06-03 13:22:20] Importing System.Security.Cryptography.dll.trap.br (122 of 171)
[2025-06-03 13:22:21] Importing System.Security.Principal.Windows.dll.trap.br (123 of 171)
[2025-06-03 13:22:21] Importing System.Security.Principal.dll.trap.br (124 of 171)
[2025-06-03 13:22:21] Importing System.Security.SecureString.dll.trap.br (125 of 171)
[2025-06-03 13:22:21] Importing System.Security.dll.trap.br (126 of 171)
[2025-06-03 13:22:21] Importing System.ServiceModel.Web.dll.trap.br (127 of 171)
[2025-06-03 13:22:21] Importing System.ServiceProcess.dll.trap.br (128 of 171)
[2025-06-03 13:22:21] Importing System.Text.Encoding.CodePages.dll.trap.br (129 of 171)
[2025-06-03 13:22:21] Importing System.Text.Encoding.Extensions.dll.trap.br (130 of 171)
[2025-06-03 13:22:21] Importing System.Text.Encoding.dll.trap.br (131 of 171)
[2025-06-03 13:22:21] Importing System.Text.Encodings.Web.dll.trap.br (132 of 171)
[2025-06-03 13:22:21] Importing System.Text.Json.dll.trap.br (133 of 171)
[2025-06-03 13:22:21] Importing System.Text.RegularExpressions.dll.trap.br (134 of 171)
[2025-06-03 13:22:21] Importing System.Threading.Channels.dll.trap.br (135 of 171)
[2025-06-03 13:22:21] Importing System.Threading.Overlapped.dll.trap.br (136 of 171)
[2025-06-03 13:22:21] Importing System.Threading.Tasks.Dataflow.dll.trap.br (137 of 171)
[2025-06-03 13:22:21] Importing System.Threading.Tasks.Extensions.dll.trap.br (138 of 171)
[2025-06-03 13:22:21] Importing System.Threading.Tasks.Parallel.dll.trap.br (139 of 171)
[2025-06-03 13:22:21] Importing System.Threading.Tasks.dll.trap.br (140 of 171)
[2025-06-03 13:22:21] Importing System.Threading.Thread.dll.trap.br (141 of 171)
[2025-06-03 13:22:21] Importing System.Threading.ThreadPool.dll.trap.br (142 of 171)
[2025-06-03 13:22:21] Importing System.Threading.Timer.dll.trap.br (143 of 171)
[2025-06-03 13:22:21] Importing System.Threading.dll.trap.br (144 of 171)
[2025-06-03 13:22:21] Importing System.Transactions.Local.dll.trap.br (145 of 171)
[2025-06-03 13:22:21] Importing System.Transactions.dll.trap.br (146 of 171)
[2025-06-03 13:22:21] Importing System.ValueTuple.dll.trap.br (147 of 171)
[2025-06-03 13:22:21] Importing System.Web.HttpUtility.dll.trap.br (148 of 171)
[2025-06-03 13:22:21] Importing System.Web.dll.trap.br (149 of 171)
[2025-06-03 13:22:21] Importing System.Windows.dll.trap.br (150 of 171)
[2025-06-03 13:22:21] Importing System.Xml.Linq.dll.trap.br (151 of 171)
[2025-06-03 13:22:21] Importing System.Xml.ReaderWriter.dll.trap.br (152 of 171)
[2025-06-03 13:22:21] Importing System.Xml.Serialization.dll.trap.br (153 of 171)
[2025-06-03 13:22:21] Importing System.Xml.XDocument.dll.trap.br (154 of 171)
[2025-06-03 13:22:21] Importing System.Xml.XPath.XDocument.dll.trap.br (155 of 171)
[2025-06-03 13:22:21] Importing System.Xml.XPath.dll.trap.br (156 of 171)
[2025-06-03 13:22:21] Importing System.Xml.XmlDocument.dll.trap.br (157 of 171)
[2025-06-03 13:22:21] Importing System.Xml.XmlSerializer.dll.trap.br (158 of 171)
[2025-06-03 13:22:21] Importing System.Xml.dll.trap.br (159 of 171)
[2025-06-03 13:22:21] Importing System.dll.trap.br (160 of 171)
[2025-06-03 13:22:21] Importing WindowsBase.dll.trap.br (161 of 171)
[2025-06-03 13:22:21] Importing mscorlib.dll.trap.br (162 of 171)
[2025-06-03 13:22:21] Importing netstandard.dll.trap.br (163 of 171)
[2025-06-03 13:22:21] Importing CSharpTest.csproj.trap.gz (164 of 171)
[2025-06-03 13:22:21] Importing Program.cs.trap.br (165 of 171)
[2025-06-03 13:22:21] Importing CSharpTest.csproj.nuget.g.props.trap.gz (166 of 171)
[2025-06-03 13:22:21] Importing .NETCoreApp,Version=v8.0.AssemblyAttributes.cs.trap.br (167 of 171)
[2025-06-03 13:22:21] Importing CSharpTest.AssemblyInfo.cs.trap.br (168 of 171)
[2025-06-03 13:22:21] Importing CSharpTest.GlobalUsings.g.cs.trap.br (169 of 171)
[2025-06-03 13:22:21] Importing CSharpTest.dll.trap.br (170 of 171)
[2025-06-03 13:22:21] Importing metadata.trap.gz (171 of 171)
[2025-06-03 13:22:21] [PROGRESS] dataset import> Merging relations
[2025-06-03 13:22:21] Merging 1 fragment for 'typerefs'.
[2025-06-03 13:22:21] Merged 8511 bytes (8.31 KiB) for 'typerefs'.
[2025-06-03 13:22:21] Merging 1 fragment for 'typeref_type'.
[2025-06-03 13:22:21] Merged 8491 bytes (8.29 KiB) for 'typeref_type'.
[2025-06-03 13:22:21] Merging 1 fragment for 'attributes'.
[2025-06-03 13:22:21] Merged 105460 bytes (102.99 KiB) for 'attributes'.
[2025-06-03 13:22:21] Merging 1 fragment for 'files'.
[2025-06-03 13:22:21] Merged 524 bytes for 'files'.
[2025-06-03 13:22:21] Merging 1 fragment for 'folders'.
[2025-06-03 13:22:21] Merged 73 bytes for 'folders'.
[2025-06-03 13:22:21] Merging 1 fragment for 'containerparent'.
[2025-06-03 13:22:21] Merged 352 bytes for 'containerparent'.
[2025-06-03 13:22:21] Merging 1 fragment for 'file_extraction_mode'.
[2025-06-03 13:22:21] Merged 285 bytes for 'file_extraction_mode'.
[2025-06-03 13:22:21] Merging 1 fragment for 'assemblies'.
[2025-06-03 13:22:21] Merged 1122 bytes (1.10 KiB) for 'assemblies'.
[2025-06-03 13:22:21] Merging 1 fragment for 'attribute_location'.
[2025-06-03 13:22:21] Merged 30699 bytes (29.98 KiB) for 'attribute_location'.
[2025-06-03 13:22:21] Merging 1 fragment for 'expressions'.
[2025-06-03 13:22:21] Merged 154179 bytes (150.57 KiB) for 'expressions'.
[2025-06-03 13:22:21] Merging 1 fragment for 'expr_parent_top_level'.
[2025-06-03 13:22:21] Merged 73794 bytes (72.06 KiB) for 'expr_parent_top_level'.
[2025-06-03 13:22:21] Merging 1 fragment for 'expr_location'.
[2025-06-03 13:22:21] Merged 52749 bytes (51.51 KiB) for 'expr_location'.
[2025-06-03 13:22:21] Merging 1 fragment for 'compiler_generated'.
[2025-06-03 13:22:21] Merged 53280 bytes (52.03 KiB) for 'compiler_generated'.
[2025-06-03 13:22:21] Merging 1 fragment for 'expr_value'.
[2025-06-03 13:22:21] Merged 98722 bytes (96.41 KiB) for 'expr_value'.
[2025-06-03 13:22:21] Merging 1 fragment for 'expr_parent'.
[2025-06-03 13:22:21] Merged 65855 bytes (64.31 KiB) for 'expr_parent'.
[2025-06-03 13:22:21] Merging 1 fragment for 'types'.
[2025-06-03 13:22:21] Merged 76690 bytes (74.89 KiB) for 'types'.
[2025-06-03 13:22:21] Merging 1 fragment for 'parent_namespace'.
[2025-06-03 13:22:21] Merged 49424 bytes (48.27 KiB) for 'parent_namespace'.
[2025-06-03 13:22:21] Merging 1 fragment for 'modifiers'.
[2025-06-03 13:22:21] Merged 56 bytes for 'modifiers'.
[2025-06-03 13:22:21] Merging 2 fragments for 'has_modifiers'.
[2025-06-03 13:22:22] Merged 381522 bytes (372.58 KiB) for 'has_modifiers'.
[2025-06-03 13:22:22] Merging 1 fragment for 'type_location'.
[2025-06-03 13:22:22] Merged 10417 bytes (10.17 KiB) for 'type_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'nullability'.
[2025-06-03 13:22:22] Merged 138 bytes for 'nullability'.
[2025-06-03 13:22:22] Merging 2 fragments for 'type_nullability'.
[2025-06-03 13:22:22] Merged 193247 bytes (188.72 KiB) for 'type_nullability'.
[2025-06-03 13:22:22] Merging 1 fragment for 'params'.
[2025-06-03 13:22:22] Merged 578919 bytes (565.35 KiB) for 'params'.
[2025-06-03 13:22:22] Merging 1 fragment for 'param_location'.
[2025-06-03 13:22:22] Merged 78592 bytes (76.75 KiB) for 'param_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'array_element_type'.
[2025-06-03 13:22:22] Merged 1200 bytes (1.17 KiB) for 'array_element_type'.
[2025-06-03 13:22:22] Merging 1 fragment for 'nullability_parent'.
[2025-06-03 13:22:22] Merged 359 bytes for 'nullability_parent'.
[2025-06-03 13:22:22] Merging 1 fragment for 'methods'.
[2025-06-03 13:22:22] Merged 181466 bytes (177.21 KiB) for 'methods'.
[2025-06-03 13:22:22] Merging 1 fragment for 'method_location'.
[2025-06-03 13:22:22] Merged 38017 bytes (37.13 KiB) for 'method_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'fields'.
[2025-06-03 13:22:22] Merged 35542 bytes (34.71 KiB) for 'fields'.
[2025-06-03 13:22:22] Merging 1 fragment for 'constant_value'.
[2025-06-03 13:22:22] Merged 13604 bytes (13.29 KiB) for 'constant_value'.
[2025-06-03 13:22:22] Merging 1 fragment for 'field_location'.
[2025-06-03 13:22:22] Merged 7087 bytes (6.92 KiB) for 'field_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'constructors'.
[2025-06-03 13:22:22] Merged 25038 bytes (24.45 KiB) for 'constructors'.
[2025-06-03 13:22:22] Merging 1 fragment for 'constructor_location'.
[2025-06-03 13:22:22] Merged 6192 bytes (6.05 KiB) for 'constructor_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'numlines'.
[2025-06-03 13:22:22] Merged 1591 bytes (1.55 KiB) for 'numlines'.
[2025-06-03 13:22:22] Merging 1 fragment for 'expr_argument_name'.
[2025-06-03 13:22:22] Merged 5073 bytes (4.95 KiB) for 'expr_argument_name'.
[2025-06-03 13:22:22] Merging 1 fragment for 'namespaces'.
[2025-06-03 13:22:22] Merged 448 bytes for 'namespaces'.
[2025-06-03 13:22:22] Merging 1 fragment for 'enum_underlying_type'.
[2025-06-03 13:22:22] Merged 890 bytes for 'enum_underlying_type'.
[2025-06-03 13:22:22] Merging 1 fragment for 'constructed_generic'.
[2025-06-03 13:22:22] Merged 41671 bytes (40.69 KiB) for 'constructed_generic'.
[2025-06-03 13:22:22] Merging 1 fragment for 'type_arguments'.
[2025-06-03 13:22:22] Merged 56020 bytes (54.71 KiB) for 'type_arguments'.
[2025-06-03 13:22:22] Merging 1 fragment for 'extend'.
[2025-06-03 13:22:22] Merged 13709 bytes (13.39 KiB) for 'extend'.
[2025-06-03 13:22:22] Merging 1 fragment for 'implement'.
[2025-06-03 13:22:22] Merged 62781 bytes (61.31 KiB) for 'implement'.
[2025-06-03 13:22:22] Merging 1 fragment for 'indexers'.
[2025-06-03 13:22:22] Merged 3205 bytes (3.13 KiB) for 'indexers'.
[2025-06-03 13:22:22] Merging 1 fragment for 'indexer_location'.
[2025-06-03 13:22:22] Merged 915 bytes for 'indexer_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'accessors'.
[2025-06-03 13:22:22] Merged 64967 bytes (63.44 KiB) for 'accessors'.
[2025-06-03 13:22:22] Merging 1 fragment for 'accessor_location'.
[2025-06-03 13:22:22] Merged 13544 bytes (13.23 KiB) for 'accessor_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'properties'.
[2025-06-03 13:22:22] Merged 61186 bytes (59.75 KiB) for 'properties'.
[2025-06-03 13:22:22] Merging 1 fragment for 'property_location'.
[2025-06-03 13:22:22] Merged 10065 bytes (9.83 KiB) for 'property_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'type_parameter_constraints'.
[2025-06-03 13:22:22] Merged 9359 bytes (9.14 KiB) for 'type_parameter_constraints'.
[2025-06-03 13:22:22] Merging 1 fragment for 'type_parameters'.
[2025-06-03 13:22:22] Merged 11448 bytes (11.18 KiB) for 'type_parameters'.
[2025-06-03 13:22:22] Merging 1 fragment for 'nested_types'.
[2025-06-03 13:22:22] Merged 1151 bytes (1.12 KiB) for 'nested_types'.
[2025-06-03 13:22:22] Merging 1 fragment for 'type_annotation'.
[2025-06-03 13:22:22] Merged 3873 bytes (3.78 KiB) for 'type_annotation'.
[2025-06-03 13:22:22] Merging 1 fragment for 'overrides'.
[2025-06-03 13:22:22] Merged 12295 bytes (12.01 KiB) for 'overrides'.
[2025-06-03 13:22:22] Merging 1 fragment for 'destructors'.
[2025-06-03 13:22:22] Merged 321 bytes for 'destructors'.
[2025-06-03 13:22:22] Merging 1 fragment for 'destructor_location'.
[2025-06-03 13:22:22] Merged 143 bytes for 'destructor_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'explicitly_implements'.
[2025-06-03 13:22:22] Merged 12078 bytes (11.79 KiB) for 'explicitly_implements'.
[2025-06-03 13:22:22] Merging 1 fragment for 'scoped_annotation'.
[2025-06-03 13:22:22] Merged 2810 bytes (2.74 KiB) for 'scoped_annotation'.
[2025-06-03 13:22:22] Merging 1 fragment for 'general_type_parameter_constraints'.
[2025-06-03 13:22:22] Merged 445 bytes for 'general_type_parameter_constraints'.
[2025-06-03 13:22:22] Merging 1 fragment for 'delegate_return_type'.
[2025-06-03 13:22:22] Merged 2644 bytes (2.58 KiB) for 'delegate_return_type'.
[2025-06-03 13:22:22] Merging 1 fragment for 'operators'.
[2025-06-03 13:22:22] Merged 19049 bytes (18.60 KiB) for 'operators'.
[2025-06-03 13:22:22] Merging 1 fragment for 'operator_location'.
[2025-06-03 13:22:22] Merged 3082 bytes (3.01 KiB) for 'operator_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'specific_type_parameter_constraints'.
[2025-06-03 13:22:22] Merged 1896 bytes (1.85 KiB) for 'specific_type_parameter_constraints'.
[2025-06-03 13:22:22] Merging 1 fragment for 'specific_type_parameter_nullability'.
[2025-06-03 13:22:22] Merged 1059 bytes (1.03 KiB) for 'specific_type_parameter_nullability'.
[2025-06-03 13:22:22] Merging 1 fragment for 'nullable_underlying_type'.
[2025-06-03 13:22:22] Merged 182 bytes for 'nullable_underlying_type'.
[2025-06-03 13:22:22] Merging 1 fragment for 'tuple_underlying_type'.
[2025-06-03 13:22:22] Merged 506 bytes for 'tuple_underlying_type'.
[2025-06-03 13:22:22] Merging 1 fragment for 'locations_default'.
[2025-06-03 13:22:22] Merged 891 bytes for 'locations_default'.
[2025-06-03 13:22:22] Merging 1 fragment for 'tuple_element'.
[2025-06-03 13:22:22] Merged 2548 bytes (2.49 KiB) for 'tuple_element'.
[2025-06-03 13:22:22] Merging 1 fragment for 'events'.
[2025-06-03 13:22:22] Merged 1112 bytes (1.09 KiB) for 'events'.
[2025-06-03 13:22:22] Merging 1 fragment for 'event_accessors'.
[2025-06-03 13:22:22] Merged 1850 bytes (1.81 KiB) for 'event_accessors'.
[2025-06-03 13:22:22] Merging 1 fragment for 'event_accessor_location'.
[2025-06-03 13:22:22] Merged 463 bytes for 'event_accessor_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'event_location'.
[2025-06-03 13:22:22] Merged 308 bytes for 'event_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'init_only_accessors'.
[2025-06-03 13:22:22] Merged 63 bytes for 'init_only_accessors'.
[2025-06-03 13:22:22] Merging 1 fragment for 'pointer_referent_type'.
[2025-06-03 13:22:22] Merged 200 bytes for 'pointer_referent_type'.
[2025-06-03 13:22:22] Merging 1 fragment for 'function_pointer_calling_conventions'.
[2025-06-03 13:22:22] Merged 32 bytes for 'function_pointer_calling_conventions'.
[2025-06-03 13:22:22] Merging 1 fragment for 'function_pointer_return_type'.
[2025-06-03 13:22:22] Merged 35 bytes for 'function_pointer_return_type'.
[2025-06-03 13:22:22] Merging 1 fragment for 'xmllocations'.
[2025-06-03 13:22:22] Merged 123 bytes for 'xmllocations'.
[2025-06-03 13:22:22] Merging 1 fragment for 'xmlElements'.
[2025-06-03 13:22:22] Merged 140 bytes for 'xmlElements'.
[2025-06-03 13:22:22] Merging 1 fragment for 'xmlAttrs'.
[2025-06-03 13:22:22] Merged 122 bytes for 'xmlAttrs'.
[2025-06-03 13:22:22] Merging 1 fragment for 'xmlChars'.
[2025-06-03 13:22:22] Merged 119 bytes for 'xmlChars'.
[2025-06-03 13:22:22] Merging 1 fragment for 'xmlEncoding'.
[2025-06-03 13:22:22] Merged 33 bytes for 'xmlEncoding'.
[2025-06-03 13:22:22] Merging 1 fragment for 'using_directive_location'.
[2025-06-03 13:22:22] Merged 55 bytes for 'using_directive_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'type_mention'.
[2025-06-03 13:22:22] Merged 120 bytes for 'type_mention'.
[2025-06-03 13:22:22] Merging 1 fragment for 'type_mention_location'.
[2025-06-03 13:22:22] Merged 70 bytes for 'type_mention_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'statements'.
[2025-06-03 13:22:22] Merged 50 bytes for 'statements'.
[2025-06-03 13:22:22] Merging 1 fragment for 'stmt_parent_top_level'.
[2025-06-03 13:22:22] Merged 46 bytes for 'stmt_parent_top_level'.
[2025-06-03 13:22:22] Merging 1 fragment for 'stmt_location'.
[2025-06-03 13:22:22] Merged 53 bytes for 'stmt_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'expr_flowstate'.
[2025-06-03 13:22:22] Merged 59 bytes for 'expr_flowstate'.
[2025-06-03 13:22:22] Merging 1 fragment for 'expr_access'.
[2025-06-03 13:22:22] Merged 56 bytes for 'expr_access'.
[2025-06-03 13:22:22] Merging 1 fragment for 'stmt_parent'.
[2025-06-03 13:22:22] Merged 59 bytes for 'stmt_parent'.
[2025-06-03 13:22:22] Merging 1 fragment for 'expr_argument'.
[2025-06-03 13:22:22] Merged 32 bytes for 'expr_argument'.
[2025-06-03 13:22:22] Merging 1 fragment for 'expr_call'.
[2025-06-03 13:22:22] Merged 48 bytes for 'expr_call'.
[2025-06-03 13:22:22] Merging 1 fragment for 'localvars'.
[2025-06-03 13:22:22] Merged 93 bytes for 'localvars'.
[2025-06-03 13:22:22] Merging 1 fragment for 'localvar_location'.
[2025-06-03 13:22:22] Merged 42 bytes for 'localvar_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'commentblock'.
[2025-06-03 13:22:22] Merged 25 bytes for 'commentblock'.
[2025-06-03 13:22:22] Merging 1 fragment for 'commentblock_location'.
[2025-06-03 13:22:22] Merged 43 bytes for 'commentblock_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'commentblock_binding'.
[2025-06-03 13:22:22] Merged 99 bytes for 'commentblock_binding'.
[2025-06-03 13:22:22] Merging 1 fragment for 'using_namespace_directives'.
[2025-06-03 13:22:22] Merged 65 bytes for 'using_namespace_directives'.
[2025-06-03 13:22:22] Merging 1 fragment for 'commentline'.
[2025-06-03 13:22:22] Merged 97 bytes for 'commentline'.
[2025-06-03 13:22:22] Merging 1 fragment for 'commentline_location'.
[2025-06-03 13:22:22] Merged 59 bytes for 'commentline_location'.
[2025-06-03 13:22:22] Merging 1 fragment for 'commentblock_child'.
[2025-06-03 13:22:22] Merged 107 bytes for 'commentblock_child'.
[2025-06-03 13:22:22] Merging 1 fragment for 'xmlNs'.
[2025-06-03 13:22:22] Merged 51 bytes for 'xmlNs'.
[2025-06-03 13:22:22] Merging 1 fragment for 'xmlHasNs'.
[2025-06-03 13:22:22] Merged 57 bytes for 'xmlHasNs'.
[2025-06-03 13:22:22] Merging 1 fragment for 'using_global'.
[2025-06-03 13:22:22] Merged 27 bytes for 'using_global'.
[2025-06-03 13:22:22] Merging 1 fragment for 'compilations'.
[2025-06-03 13:22:22] Merged 30 bytes for 'compilations'.
[2025-06-03 13:22:22] Merging 1 fragment for 'compilation_assembly'.
[2025-06-03 13:22:22] Merged 30 bytes for 'compilation_assembly'.
[2025-06-03 13:22:22] Merging 1 fragment for 'compilation_args'.
[2025-06-03 13:22:22] Merged 44 bytes for 'compilation_args'.
[2025-06-03 13:22:22] Merging 1 fragment for 'compilation_expanded_args'.
[2025-06-03 13:22:22] Merged 443 bytes for 'compilation_expanded_args'.
[2025-06-03 13:22:22] Merging 1 fragment for 'compilation_compiling_files'.
[2025-06-03 13:22:22] Merged 49 bytes for 'compilation_compiling_files'.
[2025-06-03 13:22:22] Merging 1 fragment for 'compilation_referencing_files'.
[2025-06-03 13:22:22] Merged 513 bytes for 'compilation_referencing_files'.
[2025-06-03 13:22:22] Merging 1 fragment for 'diagnostics'.
[2025-06-03 13:22:22] Merged 123 bytes for 'diagnostics'.
[2025-06-03 13:22:22] Merging 1 fragment for 'diagnostic_for'.
[2025-06-03 13:22:22] Merged 72 bytes for 'diagnostic_for'.
[2025-06-03 13:22:22] Merging 1 fragment for 'compilation_info'.
[2025-06-03 13:22:22] Merged 48 bytes for 'compilation_info'.
[2025-06-03 13:22:22] Merging 1 fragment for 'compilation_time'.
[2025-06-03 13:22:22] Merged 126 bytes for 'compilation_time'.
[2025-06-03 13:22:22] Merging 1 fragment for 'compilation_finished'.
[2025-06-03 13:22:22] Merged 71 bytes for 'compilation_finished'.
[2025-06-03 13:22:22] Merging 1 fragment for 'sourceLocationPrefix'.
[2025-06-03 13:22:22] Merged 19 bytes for 'sourceLocationPrefix'.
[2025-06-03 13:22:22] Saving string and id pools to disk.
[2025-06-03 13:22:23] Finished importing TRAP files.
[2025-06-03 13:22:23] Read 43.44 MiB of uncompressed TRAP data.
[2025-06-03 13:22:23] Uncompressed relation data size: 11.23 MiB
[2025-06-03 13:22:23] Relation data size: 2.63 MiB (merge rate: 2.14 MiB/s)
[2025-06-03 13:22:23] String pool size: 2.78 MiB
[2025-06-03 13:22:23] ID pool size: 13.00 MiB
[2025-06-03 13:22:23] [PROGRESS] dataset import> Finished writing database (relations: 2.63 MiB; string pool: 2.78 MiB).
[2025-06-03 13:22:23] Pausing evaluation to close the cache at sequence stamp o+235
[2025-06-03 13:22:23] The disk cache is freshly trimmed; leave it be.
[2025-06-03 13:22:23] Unpausing evaluation
[2025-06-03 13:22:23] Plumbing command codeql dataset import completed.
[2025-06-03 13:22:23] [PROGRESS] database finalize> TRAP import complete (5.5s).
[2025-06-03 13:22:23] Running plumbing command: codeql database cleanup -- E:\advance_javascript\codeQL\7\csharp-db-working
[2025-06-03 13:22:25] [PROGRESS] database cleanup> Cleaning up existing TRAP files after import...
[2025-06-03 13:22:26] [PROGRESS] database cleanup> TRAP files cleaned up (926ms).
[2025-06-03 13:22:26] [PROGRESS] database cleanup> Cleaning up scratch directory...
[2025-06-03 13:22:26] [PROGRESS] database cleanup> Scratch directory cleaned up (0ms).
[2025-06-03 13:22:26] Running plumbing command: codeql dataset cleanup -- E:\advance_javascript\codeQL\7\csharp-db-working\db-csharp
[2025-06-03 13:22:26] [PROGRESS] dataset cleanup> Cleaning up dataset in E:\advance_javascript\codeQL\7\csharp-db-working\db-csharp.
[2025-06-03 13:22:26] Trimming disk cache at E:\advance_javascript\codeQL\7\csharp-db-working\db-csharp\default\cache in mode trim.
[2025-06-03 13:22:26] Sequence stamp origin is -6042258148719606922
[2025-06-03 13:22:26] Pausing evaluation to quickly trim memory at sequence stamp o+0
[2025-06-03 13:22:26] Unpausing evaluation
[2025-06-03 13:22:26] Pausing evaluation to zealously trim disk at sequence stamp o+1
[2025-06-03 13:22:26] Unpausing evaluation
[2025-06-03 13:22:26] Trimming completed (3ms): Trimmed disposable data from cache.
[2025-06-03 13:22:26] Pausing evaluation to close the cache at sequence stamp o+2
[2025-06-03 13:22:26] The disk cache is freshly trimmed; leave it be.
[2025-06-03 13:22:26] Unpausing evaluation
[2025-06-03 13:22:26] [PROGRESS] dataset cleanup> Trimmed disposable data from cache.
[2025-06-03 13:22:26] [PROGRESS] dataset cleanup> Finalizing dataset in E:\advance_javascript\codeQL\7\csharp-db-working\db-csharp
[2025-06-03 13:22:26] [DETAILS] dataset cleanup> Finished deleting ID pool from E:\advance_javascript\codeQL\7\csharp-db-working\db-csharp (6ms).
[2025-06-03 13:22:26] Plumbing command codeql dataset cleanup completed.
[2025-06-03 13:22:26] Plumbing command codeql database cleanup completed with status 0.
[2025-06-03 13:22:26] [PROGRESS] database finalize> Finished zipping source archive (3.03 KiB).
[2025-06-03 13:22:26] Plumbing command codeql database finalize completed.
[2025-06-03 13:22:26] [PROGRESS] database create> Successfully created database at E:\advance_javascript\codeQL\7\csharp-db-working.
[2025-06-03 13:22:26] Terminating normally.
