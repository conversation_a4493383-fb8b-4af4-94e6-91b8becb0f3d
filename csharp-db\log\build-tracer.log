[T 05:59:39 10972] Initializing tracer.
[T 05:59:39 10972] Initializing tags.
[T 05:59:39 10972] CodeQL CLI version 2.21.3
[T 05:59:39 10972] Initializing tracer.
[T 05:59:39 10972] Initializing tags.
[T 05:59:39 10972] Allocated ID 6393AA6200002ADC_0000000000000001 (parent )
[T 05:59:39 10972] ==== Candidate to intercept: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\runner.exe (9800) ====
[T 05:59:39 10972] Executing the following tracer actions:
[T 05:59:39 10972] Tracer actions:
[T 05:59:39 10972] pre_invocations(0)
[T 05:59:39 10972] post_invocations(0)
[T 05:59:39 10972] trace_languages(1): [csharp]
[T 05:59:40 9800] Initializing tracer.
[T 05:59:40 9800] Initializing tags.
[T 05:59:40 9800] ID set to 6393AA6200002ADC_0000000000000001 (parent root)
[T 05:59:40 9800] Allocated ID 0B3D9A8200002648_0000000000000001 (parent 6393AA6200002ADC_0000000000000001)
[T 05:59:40 9800] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (21008) ====
[T 05:59:40 9800] Executing the following tracer actions:
[T 05:59:40 9800] Tracer actions:
[T 05:59:40 9800] pre_invocations(0)
[T 05:59:40 9800] post_invocations(0)
[T 05:59:40 9800] trace_languages(1): [csharp]
[T 05:59:40 21008] Initializing tracer.
[T 05:59:40 21008] Initializing tags.
[T 05:59:40 21008] ID set to 0B3D9A8200002648_0000000000000001 (parent 6393AA6200002ADC_0000000000000001)
[T 05:59:40 21008] Allocated ID F6FDA54100005210_0000000000000001 (parent 0B3D9A8200002648_0000000000000001)
[T 05:59:40 21008] ==== Candidate to intercept: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\tools\win64\Semmle.Autobuild.CSharp.exe (23816) ====
[T 05:59:40 21008] Executing the following tracer actions:
[T 05:59:40 21008] Tracer actions:
[T 05:59:40 21008] pre_invocations(0)
[T 05:59:40 21008] post_invocations(0)
[T 05:59:40 21008] trace_languages(1): [csharp]
[T 05:59:40 23816] Initializing tracer.
[T 05:59:40 23816] Initializing tags.
[T 05:59:40 23816] ID set to F6FDA54100005210_0000000000000001 (parent 0B3D9A8200002648_0000000000000001)
[T 06:00:01 2016] Initializing tracer.
[T 06:00:01 2016] Initializing tags.
[T 06:00:01 2016] CodeQL CLI version 2.21.3
[T 06:00:01 2016] Initializing tracer.
[T 06:00:01 2016] Initializing tags.
[T 06:00:01 2016] Allocated ID 46942193000007E0_0000000000000001 (parent )
[T 06:00:01 2016] ==== Candidate to intercept: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\runner.exe (26052) ====
[T 06:00:01 2016] Executing the following tracer actions:
[T 06:00:01 2016] Tracer actions:
[T 06:00:01 2016] pre_invocations(0)
[T 06:00:01 2016] post_invocations(0)
[T 06:00:01 2016] trace_languages(1): [csharp]
[T 06:00:01 26052] Initializing tracer.
[T 06:00:01 26052] Initializing tags.
[T 06:00:01 26052] ID set to 46942193000007E0_0000000000000001 (parent root)
[T 06:00:01 26052] Allocated ID A7D27695000065C4_0000000000000001 (parent 46942193000007E0_0000000000000001)
[T 06:00:01 26052] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (8652) ====
[T 06:00:01 26052] Executing the following tracer actions:
[T 06:00:01 26052] Tracer actions:
[T 06:00:01 26052] pre_invocations(0)
[T 06:00:01 26052] post_invocations(0)
[T 06:00:01 26052] trace_languages(1): [csharp]
[T 06:00:01 8652] Initializing tracer.
[T 06:00:01 8652] Initializing tags.
[T 06:00:01 8652] ID set to A7D27695000065C4_0000000000000001 (parent 46942193000007E0_0000000000000001)
[T 06:00:02 8652] Allocated ID A3C18D68000021CC_0000000000000001 (parent A7D27695000065C4_0000000000000001)
[T 06:00:02 8652] ==== Candidate to intercept: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\tools\win64\Semmle.Autobuild.CSharp.exe (16600) ====
[T 06:00:02 8652] Executing the following tracer actions:
[T 06:00:02 8652] Tracer actions:
[T 06:00:02 8652] pre_invocations(0)
[T 06:00:02 8652] post_invocations(0)
[T 06:00:02 8652] trace_languages(1): [csharp]
[T 06:00:02 16600] Initializing tracer.
[T 06:00:02 16600] Initializing tags.
[T 06:00:02 16600] ID set to A3C18D68000021CC_0000000000000001 (parent A7D27695000065C4_0000000000000001)
[T 06:00:03 16600] Allocated ID 035CD9BF000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:03 16600] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (16580) ====
[T 06:00:03 16600] Executing the following tracer actions:
[T 06:00:03 16600] Tracer actions:
[T 06:00:03 16600] pre_invocations(0)
[T 06:00:03 16600] post_invocations(0)
[T 06:00:03 16600] trace_languages(1): [csharp]
[T 06:00:03 16580] Initializing tracer.
[T 06:00:03 16580] Initializing tags.
[T 06:00:03 16580] ID set to 035CD9BF000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:03 16580] Allocated ID A5F18FD9000040C4_0000000000000001 (parent 035CD9BF000040D8_0000000000000001)
[T 06:00:03 16580] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (13020) ====
[T 06:00:03 16580] Executing the following tracer actions:
[T 06:00:03 16580] Tracer actions:
[T 06:00:03 16580] pre_invocations(0)
[T 06:00:03 16580] post_invocations(0)
[T 06:00:03 16580] trace_languages(1): [csharp]
[T 06:00:03 13020] Initializing tracer.
[T 06:00:03 13020] Initializing tags.
[T 06:00:03 13020] ID set to A5F18FD9000040C4_0000000000000001 (parent 035CD9BF000040D8_0000000000000001)
[T 06:00:03 16600] Allocated ID 70589FA8000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:03 16600] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (13868) ====
[T 06:00:03 16600] Executing the following tracer actions:
[T 06:00:03 16600] Tracer actions:
[T 06:00:03 16600] pre_invocations(0)
[T 06:00:03 16600] post_invocations(0)
[T 06:00:03 16600] trace_languages(1): [csharp]
[T 06:00:03 13868] Initializing tracer.
[T 06:00:03 13868] Initializing tags.
[T 06:00:03 13868] ID set to 70589FA8000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:03 13868] Allocated ID 64537C950000362C_0000000000000001 (parent 70589FA8000040D8_0000000000000001)
[T 06:00:03 13868] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (4244) ====
[T 06:00:03 13868] Lua: Execute a .NET SDK command usage detected
[T 06:00:03 13868] Lua: Dotnet subcommand detected: clean
[T 06:00:03 13868] Executing the following tracer actions:
[T 06:00:03 13868] Tracer actions:
[T 06:00:03 13868] pre_invocations(0)
[T 06:00:03 13868] post_invocations(0)
[T 06:00:03 13868] trace_languages(1): [csharp]
[T 06:00:03 4244] Initializing tracer.
[T 06:00:03 4244] Initializing tags.
[T 06:00:03 4244] ID set to 64537C950000362C_0000000000000001 (parent 70589FA8000040D8_0000000000000001)
[T 06:00:03 16600] Allocated ID 9DA69710000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:03 16600] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (19320) ====
[T 06:00:03 16600] Executing the following tracer actions:
[T 06:00:03 16600] Tracer actions:
[T 06:00:03 16600] pre_invocations(0)
[T 06:00:03 16600] post_invocations(0)
[T 06:00:03 16600] trace_languages(1): [csharp]
[T 06:00:03 19320] Initializing tracer.
[T 06:00:03 19320] Initializing tags.
[T 06:00:03 19320] ID set to 9DA69710000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:03 19320] Allocated ID D15538AE00004B78_0000000000000001 (parent 9DA69710000040D8_0000000000000001)
[T 06:00:03 19320] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (22772) ====
[T 06:00:03 19320] Lua: Execute a .NET SDK command usage detected
[T 06:00:03 19320] Lua: Dotnet subcommand detected: restore
[T 06:00:03 19320] Executing the following tracer actions:
[T 06:00:03 19320] Tracer actions:
[T 06:00:03 19320] pre_invocations(0)
[T 06:00:03 19320] post_invocations(0)
[T 06:00:03 19320] trace_languages(1): [csharp]
[T 06:00:03 22772] Initializing tracer.
[T 06:00:03 22772] Initializing tags.
[T 06:00:03 22772] ID set to D15538AE00004B78_0000000000000001 (parent 9DA69710000040D8_0000000000000001)
[T 06:00:04 16600] Allocated ID 35A91460000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:04 16600] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (28132) ====
[T 06:00:04 16600] Executing the following tracer actions:
[T 06:00:04 16600] Tracer actions:
[T 06:00:04 16600] pre_invocations(0)
[T 06:00:04 16600] post_invocations(0)
[T 06:00:04 16600] trace_languages(1): [csharp]
[T 06:00:04 28132] Initializing tracer.
[T 06:00:04 28132] Initializing tags.
[T 06:00:04 28132] ID set to 35A91460000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:04 28132] Allocated ID C2C4AA6100006DE4_0000000000000001 (parent 35A91460000040D8_0000000000000001)
[T 06:00:04 28132] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (20196) ====
[T 06:00:04 28132] Lua: Execute a .NET SDK command usage detected
[T 06:00:04 28132] Lua: Dotnet subcommand detected: build
[T 06:00:04 28132] Lua: === Intercepted call to c:\program files\dotnet\dotnet.exe ===
[T 06:00:04 28132] Executing the following tracer actions:
[T 06:00:04 28132] Tracer actions:
[T 06:00:04 28132] pre_invocations(0)
[T 06:00:04 28132] replacing compiler with:
[T 06:00:04 28132] invocation: c:\program files\dotnet\dotnet.exe, args: build --no-incremental E:\advance_javascript\codeQL\7\SecurityVulnerabilities.csproj -p:UseSharedCompilation=false -p:EmitCompilerGeneratedFiles=true
[T 06:00:04 28132] post_invocations(0)
[T 06:00:04 28132] trace_languages(1): [csharp]
[T 06:00:04 23484] Initializing tracer.
[T 06:00:04 23484] Initializing tags.
[T 06:00:04 23484] ID set to C2C4AA6100006DE4_0000000000000001 (parent 35A91460000040D8_0000000000000001)
[T 06:00:04 16600] Allocated ID C1509ECA000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:04 16600] ==== Candidate to intercept: C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe (21400) ====
[T 06:00:04 16600] Executing the following tracer actions:
[T 06:00:04 16600] Tracer actions:
[T 06:00:04 16600] pre_invocations(0)
[T 06:00:04 16600] post_invocations(0)
[T 06:00:04 16600] trace_languages(1): [csharp]
[T 06:00:05 21400] Initializing tracer.
[T 06:00:05 21400] Initializing tags.
[T 06:00:05 21400] ID set to C1509ECA000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:05 16600] Allocated ID CE639CCF000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:05 16600] ==== Candidate to intercept: C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe (19008) ====
[T 06:00:05 16600] Executing the following tracer actions:
[T 06:00:05 16600] Tracer actions:
[T 06:00:05 16600] pre_invocations(0)
[T 06:00:05 16600] post_invocations(0)
[T 06:00:05 16600] trace_languages(1): [csharp]
[T 06:00:05 19008] Initializing tracer.
[T 06:00:05 19008] Initializing tags.
[T 06:00:05 19008] ID set to CE639CCF000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:05 16600] Allocated ID 77440CE6000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:05 16600] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (17796) ====
[T 06:00:05 16600] Executing the following tracer actions:
[T 06:00:05 16600] Tracer actions:
[T 06:00:05 16600] pre_invocations(0)
[T 06:00:05 16600] post_invocations(0)
[T 06:00:05 16600] trace_languages(1): [csharp]
[T 06:00:05 17796] Initializing tracer.
[T 06:00:05 17796] Initializing tags.
[T 06:00:05 17796] ID set to 77440CE6000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:09 16600] Allocated ID 0C0AD054000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:09 16600] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (15076) ====
[T 06:00:09 16600] Executing the following tracer actions:
[T 06:00:09 16600] Tracer actions:
[T 06:00:09 16600] pre_invocations(0)
[T 06:00:09 16600] post_invocations(0)
[T 06:00:09 16600] trace_languages(1): [csharp]
[T 06:00:09 15076] Initializing tracer.
[T 06:00:09 15076] Initializing tags.
[T 06:00:09 15076] ID set to 0C0AD054000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:09 15076] Allocated ID 2521D24200003AE4_0000000000000001 (parent 0C0AD054000040D8_0000000000000001)
[T 06:00:09 15076] ==== Candidate to intercept: E:\advance_javascript\codeQL\7\csharp-db\working\.nuget\nuget.exe (8248) ====
[T 06:00:09 15076] Executing the following tracer actions:
[T 06:00:09 15076] Tracer actions:
[T 06:00:09 15076] pre_invocations(0)
[T 06:00:09 15076] post_invocations(0)
[T 06:00:09 15076] trace_languages(1): [csharp]
[T 06:00:09 8248] Initializing tracer.
[T 06:00:09 8248] Initializing tags.
[T 06:00:09 8248] ID set to 2521D24200003AE4_0000000000000001 (parent 0C0AD054000040D8_0000000000000001)
[T 06:00:11 8248] Allocated ID DECE6C4A00002038_0000000000000001 (parent 2521D24200003AE4_0000000000000001)
[T 06:00:11 8248] ==== Candidate to intercept: C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe (20160) ====
[T 06:00:11 8248] Lua: === Intercepted call to c:\program files (x86)\microsoft visual studio\2022\buildtools\msbuild\current\bin\msbuild.exe ===
[T 06:00:11 8248] Executing the following tracer actions:
[T 06:00:11 8248] Tracer actions:
[T 06:00:11 8248] pre_invocations(0)
[T 06:00:11 8248] replacing compiler with:
[T 06:00:11 8248] invocation: c:\program files (x86)\microsoft visual studio\2022\buildtools\msbuild\current\bin\msbuild.exe, args: "C:\Users\<USER>\AppData\Local\Temp\NuGetScratch\tuv2ocdq.1il.nugetinputs.targets" /t:GenerateRestoreGraphFile /nologo /nr:false /v:q /p:NuGetPropsFile="C:\Users\<USER>\AppData\Local\Temp\NuGetScratch\fumdygop.gre.nugetrestore.props" /p:NuGetRestoreTargets="C:\Users\<USER>\AppData\Local\Temp\NuGetScratch\ika212t0.t1s.nugetrestore.targets" /p:RestoreUseCustomAfterTargets="True" /p:DisableCheckingDuplicateNuGetItems="True" /p:RestoreTaskAssemblyFile="E:\advance_javascript\codeQL\7\csharp-db\working\.nuget\nuget.exe" /p:UseSharedCompilation=false /p:MvcBuildViews=true /p:EmitCompilerGeneratedFiles=true
[T 06:00:11 8248] post_invocations(0)
[T 06:00:11 8248] trace_languages(1): [csharp]
[T 06:00:11 8596] Initializing tracer.
[T 06:00:11 8596] Initializing tags.
[T 06:00:11 8596] ID set to DECE6C4A00002038_0000000000000001 (parent 2521D24200003AE4_0000000000000001)
[T 06:00:12 16600] Allocated ID B42AD8D8000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:12 16600] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (20960) ====
[T 06:00:12 16600] Executing the following tracer actions:
[T 06:00:12 16600] Tracer actions:
[T 06:00:12 16600] pre_invocations(0)
[T 06:00:12 16600] post_invocations(0)
[T 06:00:12 16600] trace_languages(1): [csharp]
[T 06:00:12 20960] Initializing tracer.
[T 06:00:12 20960] Initializing tags.
[T 06:00:12 20960] ID set to B42AD8D8000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:12 16600] Allocated ID A021C214000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:12 16600] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (23252) ====
[T 06:00:12 16600] Executing the following tracer actions:
[T 06:00:12 16600] Tracer actions:
[T 06:00:12 16600] pre_invocations(0)
[T 06:00:12 16600] post_invocations(0)
[T 06:00:12 16600] trace_languages(1): [csharp]
[T 06:00:12 23252] Initializing tracer.
[T 06:00:12 23252] Initializing tags.
[T 06:00:12 23252] ID set to A021C214000040D8_0000000000000001 (parent A3C18D68000021CC_0000000000000001)
[T 06:00:20 16068] Initializing tracer.
[T 06:00:20 16068] Initializing tags.
[T 06:00:20 16068] CodeQL CLI version 2.21.3
[T 06:00:20 16068] Initializing tracer.
[T 06:00:20 16068] Initializing tags.
[T 06:00:20 16068] Allocated ID C12DF11C00003EC4_0000000000000001 (parent )
[T 06:00:20 16068] ==== Candidate to intercept: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\runner.exe (11228) ====
[T 06:00:20 16068] Executing the following tracer actions:
[T 06:00:20 16068] Tracer actions:
[T 06:00:20 16068] pre_invocations(0)
[T 06:00:20 16068] post_invocations(0)
[T 06:00:20 16068] trace_languages(1): [csharp]
[T 06:00:20 11228] Initializing tracer.
[T 06:00:20 11228] Initializing tags.
[T 06:00:20 11228] ID set to C12DF11C00003EC4_0000000000000001 (parent root)
[T 06:00:21 11228] Allocated ID 88D10D7700002BDC_0000000000000001 (parent C12DF11C00003EC4_0000000000000001)
[T 06:00:21 11228] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (26416) ====
[T 06:00:21 11228] Executing the following tracer actions:
[T 06:00:21 11228] Tracer actions:
[T 06:00:21 11228] pre_invocations(0)
[T 06:00:21 11228] post_invocations(0)
[T 06:00:21 11228] trace_languages(1): [csharp]
[T 06:00:21 26416] Initializing tracer.
[T 06:00:21 26416] Initializing tags.
[T 06:00:21 26416] ID set to 88D10D7700002BDC_0000000000000001 (parent C12DF11C00003EC4_0000000000000001)
[T 06:00:39 13516] Initializing tracer.
[T 06:00:39 13516] Initializing tags.
[T 06:00:39 13516] CodeQL CLI version 2.21.3
[T 06:00:39 13516] Initializing tracer.
[T 06:00:39 13516] Initializing tags.
[T 06:00:39 13516] Allocated ID DC03FFE5000034CC_0000000000000001 (parent )
[T 06:00:39 13516] ==== Candidate to intercept: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\runner.exe (17764) ====
[T 06:00:39 13516] Executing the following tracer actions:
[T 06:00:39 13516] Tracer actions:
[T 06:00:39 13516] pre_invocations(0)
[T 06:00:39 13516] post_invocations(0)
[T 06:00:39 13516] trace_languages(1): [csharp]
[T 06:00:39 17764] Initializing tracer.
[T 06:00:39 17764] Initializing tags.
[T 06:00:39 17764] ID set to DC03FFE5000034CC_0000000000000001 (parent root)
[T 06:00:39 17764] Allocated ID 70816D6D00004564_0000000000000001 (parent DC03FFE5000034CC_0000000000000001)
[T 06:00:39 17764] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (1460) ====
[T 06:00:39 17764] Executing the following tracer actions:
[T 06:00:39 17764] Tracer actions:
[T 06:00:39 17764] pre_invocations(0)
[T 06:00:39 17764] post_invocations(0)
[T 06:00:39 17764] trace_languages(1): [csharp]
[T 06:00:39 1460] Initializing tracer.
[T 06:00:39 1460] Initializing tags.
[T 06:00:39 1460] ID set to 70816D6D00004564_0000000000000001 (parent DC03FFE5000034CC_0000000000000001)
