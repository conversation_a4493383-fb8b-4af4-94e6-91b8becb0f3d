[2025-06-03 11:03:40] This is codeql database index-files --include-extension=.yaml --include-extension=.yml --size-limit=5m --language yaml -- E:\advance_javascript\codeQL\7\python-db-updated
[2025-06-03 11:03:40] Log file was started late.
[2025-06-03 11:03:40] Using index-files script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\tools\index-files.cmd.
[2025-06-03 11:03:40] [PROGRESS] database index-files> Scanning for files in E:\advance_javascript\codeQL\7...
[2025-06-03 11:03:40] Calling plumbing command: codeql resolve files --include-extension=.yaml --include-extension=.yml --size-limit=5m E:\advance_javascript\codeQL\7 --format=json
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\cached-strings...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\cached-strings\pools...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\cached-strings\pools\0...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\cached-strings\pools\0\buckets...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\cached-strings\pools\0\ids1...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\cached-strings\pools\0\indices1...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\cached-strings\pools\0\metadata...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\cached-strings\pools\0\pageDump...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\cached-strings\tuple-pool...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\pages...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\predicates...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\cache\relations...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\pools...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\pools\0...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\pools\0\buckets...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\pools\0\metadata...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\pools\0\pageDump...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\pools\1...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\pools\1\buckets...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\pools\1\ids1...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\pools\1\indices1...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\pools\1\metadata...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\pools\1\pageDump...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\db-python\default\strings...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\diagnostic...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\diagnostic\extractors...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\diagnostic\extractors\python...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\diagnostic\extractors\yaml...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\diagnostic\tracer...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\log...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\results...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\results\python-security-queries...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\results\python-security-queries\queries...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\diagnostic...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\diagnostic\extractors...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\diagnostic\extractors\python...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\diagnostic\tracer...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\log...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\src...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\src\E_...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\src\E_\advance_javascript...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\src\E_\advance_javascript\codeQL...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\src\E_\advance_javascript\codeQL\7...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\trap...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\trap\python...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\working...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\working\trap_cache...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db-updated\working\trap_cache\1...
[2025-06-03 11:03:40] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\queries...
[2025-06-03 11:03:40] Plumbing command codeql resolve files completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\python-db-updated\\codeql-database.yml",
                        "E:\\advance_javascript\\codeQL\\7\\python-db\\codeql-database.yml",
                        "E:\\advance_javascript\\codeQL\\7\\python-db\\results\\run-info-20250603.052958.460.yml",
                        "E:\\advance_javascript\\codeQL\\7\\python-db\\results\\run-info-20250603.053134.924.yml",
                        "E:\\advance_javascript\\codeQL\\7\\qlpack.yml"
                      ]
[2025-06-03 11:03:40] [DETAILS] database index-files> Found 5 files.
[2025-06-03 11:03:40] [PROGRESS] database index-files> E:\advance_javascript\codeQL\7\python-db-updated: Indexing files in in E:\advance_javascript\codeQL\7...
[2025-06-03 11:03:40] Using index-files script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\tools\index-files.cmd.
[2025-06-03 11:03:40] [PROGRESS] database index-files> Running command in E:\advance_javascript\codeQL\7: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\tools\index-files.cmd, E:\advance_javascript\codeQL\7\python-db-updated\working\files-to-index8598505388616343197.list]
[2025-06-03 11:03:41] Terminating normally.
