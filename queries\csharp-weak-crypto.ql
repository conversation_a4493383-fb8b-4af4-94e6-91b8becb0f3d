/**
 * @name Weak cryptographic algorithms in C#
 * @description Detects usage of weak cryptographic algorithms like MD5, SHA1, DES.
 * @kind problem
 * @problem.severity warning
 * @id csharp/weak-crypto
 */

import csharp

from MethodCall call
where
  (
    call.getTarget().getDeclaringType().hasName("MD5") or
    call.getTarget().getDeclaringType().hasName("SHA1") or
    call.getTarget().getDeclaringType().hasName("DES") or
    call.getTarget().getDeclaringType().hasName("RC2") or
    call.getTarget().hasName("MD5") or
    call.getTarget().hasName("SHA1")
  ) and
  (
    call.getTarget().hasName("Create") or
    call.getTarget().hasName("ComputeHash")
  )
select call, "Usage of weak cryptographic algorithm - consider using stronger alternatives like SHA256, SHA512, or AES."
