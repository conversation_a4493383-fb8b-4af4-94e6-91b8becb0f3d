/**
 * @name Hardcoded credentials detection
 * @description Detects hardcoded passwords, API keys, and secrets in variable assignments.
 * @kind problem
 * @problem.severity error
 * @id py/hardcoded-credentials-simple
 */

import python

from Assign assign, StringLiteral value, string varName
where
  assign.getTarget(0).(Name).getId() = varName and
  assign.getValue() = value and
  (
    varName.toLowerCase().matches("%password%") or
    varName.toLowerCase().matches("%passwd%") or
    varName.toLowerCase().matches("%pwd%") or
    varName.toLowerCase().matches("%secret%") or
    varName.toLowerCase().matches("%key%") or
    varName.toLowerCase().matches("%token%")
  ) and
  value.getText().length() > 5 and
  // Exclude obvious non-credentials
  not value.getText().toLowerCase().matches("%enter%") and
  not value.getText().toLowerCase().matches("%input%") and
  not value.getText().toLowerCase().matches("%prompt%") and
  not value.getText() = "" and
  not value.getText().matches("%:%") and
  not value.getText().matches("%?%")
select assign, "Potential hardcoded credential in variable '" + varName + "': " + value.getText()
