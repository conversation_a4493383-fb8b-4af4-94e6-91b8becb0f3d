[2025-06-03 18:07:03] This is codeql execute queries -J-Xmx1346M --verbosity=progress --logdir=E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\results -- E:\advance_javascript\codeQL\7\multi_purpose_project\nodejs-db\db-javascript queries\hardcoded-credentials.ql
[2025-06-03 18:07:04] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --format=json -- queries\hardcoded-credentials.ql
[2025-06-03 18:07:04] [PROGRESS] resolve queries> Recording pack reference nodejs-security-queries at E:\advance_javascript\codeQL\7\multi_purpose_project\queries.
[2025-06-03 18:07:04] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries\\hardcoded-credentials.ql"
                      ]
[2025-06-03 18:07:04] Refusing fancy output: The terminal is not an xterm: 
[2025-06-03 18:07:04] Creating executor with 1 threads.
[2025-06-03 18:07:04] Calling plumbing command: codeql resolve extensions --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --include-extension-row-locations queries\hardcoded-credentials.ql
[2025-06-03 18:07:04] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --allow-library-packs --format startingpacks -- queries\hardcoded-credentials.ql
[2025-06-03 18:07:04] [PROGRESS] resolve queries> Recording pack reference nodejs-security-queries at E:\advance_javascript\codeQL\7\multi_purpose_project\queries.
[2025-06-03 18:07:04] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries"
                      ]
[2025-06-03 18:07:04] Calling plumbing command: codeql resolve extensions-by-pack --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\7\multi_purpose_project\queries
[2025-06-03 18:07:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] nodejs-security-queries: not 1.0.0 {root: nodejs-security-queries@1.0.0}
[2025-06-03 18:07:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] nodejs-security-queries: 1.0.0 {nodejs-security-queries: not 1.0.0 {root: nodejs-security-queries@1.0.0}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] nodejs-security-queries: * [*], codeql/javascript-all: not * [*] {dependency: nodejs-security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 1] nodejs-security-queries: 1.0.0
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/javascript-all: * [*] {nodejs-security-queries: * [*], codeql/javascript-all: not * [*] {dependency: nodejs-security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 18:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\apollo-server.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\aws-sdk.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\axios.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\default-threat-models-fixup.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\hana-db-client.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 2 tuples.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 6 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\make-dir.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 1 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\markdown-table.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 1 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\mkdirp.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\open.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 2 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\react-relay-threat.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 10 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\rimraf.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 3 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\shelljs.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 1 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\tanstack.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 6 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\ext\underscore.string.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 5 tuples.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 20 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NoSQL.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 4 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\NodeJSLib.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\SQL.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 5 tuples.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 4 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\data\internal\empty.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sourceModel: 0 tuples.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 0 tuples.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 0 tuples.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:neutralModel: 0 tuples.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 0 tuples.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 0 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\helmet\Helmet.Required.Setting.model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:requiredHelmetSecuritySetting: 2 tuples.
[2025-06-03 18:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\minimongo\model.yml.
[2025-06-03 18:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 75 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mongodb\model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 26 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 611 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 32 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 102 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mssql\model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 34 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\mysql\model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 57 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\pg\model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 65 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 23 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sequelize\model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:sinkModel: 7 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 248 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 5 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 2 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\spanner\model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 174 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeVariableModel: 5 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\frameworks\sqlite3\model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:typeModel: 15 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:summaryModel: 3 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\IntegrityCheckingRequired\integrity_checking_required.model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isCdnDomainWithCheckingRequired: 3 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\compromised\compromised_domains.model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 1 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmle\javascript\security\domains\untrusted\untrusted_domains.model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/javascript-all:isUntrustedDomain: 6 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-03 18:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-03 18:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-03 18:07:08] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 18:07:08] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries" : [
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\apollo-server.model.yml",
                              "index" : 1,
                              "firstRowId" : 1,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=12+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\aws-sdk.model.yml",
                              "index" : 0,
                              "firstRowId" : 5,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=7*3"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 0,
                              "firstRowId" : 8,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\axios.model.yml",
                              "index" : 1,
                              "firstRowId" : 9,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=12",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 10,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 0,
                              "firstRowId" : 11,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 1,
                              "firstRowId" : 15,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=15+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\hana-db-client.model.yml",
                              "index" : 2,
                              "firstRowId" : 17,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=22+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\make-dir.model.yml",
                              "index" : 0,
                              "firstRowId" : 23,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\markdown-table.model.yml",
                              "index" : 0,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\mkdirp.model.yml",
                              "index" : 0,
                              "firstRowId" : 25,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\open.model.yml",
                              "index" : 0,
                              "firstRowId" : 27,
                              "rowCount" : 2,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\react-relay-threat.model.yml",
                              "index" : 0,
                              "firstRowId" : 29,
                              "rowCount" : 10,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*9",
                                "columnNumbers" : "A=9*10"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\rimraf.model.yml",
                              "index" : 0,
                              "firstRowId" : 39,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\shelljs.model.yml",
                              "index" : 0,
                              "firstRowId" : 42,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\tanstack.model.yml",
                              "index" : 0,
                              "firstRowId" : 43,
                              "rowCount" : 6,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*5",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 0,
                              "firstRowId" : 49,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\ext\\underscore.string.model.yml",
                              "index" : 1,
                              "firstRowId" : 54,
                              "rowCount" : 20,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*19",
                                "columnNumbers" : "A=9*20"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NoSQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 74,
                              "rowCount" : 4,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1*2",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\NodeJSLib.model.yml",
                              "index" : 0,
                              "firstRowId" : 78,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 0,
                              "firstRowId" : 83,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\SQL.model.yml",
                              "index" : 1,
                              "firstRowId" : 88,
                              "rowCount" : 4,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=16+1*3",
                                "columnNumbers" : "A=9*4"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 92,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "requiredHelmetSecuritySetting",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\helmet\\Helmet.Required.Setting.model.yml",
                              "index" : 0,
                              "firstRowId" : 92,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\minimongo\\model.yml",
                              "index" : 0,
                              "firstRowId" : 94,
                              "rowCount" : 75,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*74",
                                "columnNumbers" : "A=9*75"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 0,
                              "firstRowId" : 169,
                              "rowCount" : 26,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*25",
                                "columnNumbers" : "A=9*26"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 1,
                              "firstRowId" : 195,
                              "rowCount" : 611,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=37+1*610",
                                "columnNumbers" : "A=9*611"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 2,
                              "firstRowId" : 806,
                              "rowCount" : 32,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=653+1*31",
                                "columnNumbers" : "A=9*32"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mongodb\\model.yml",
                              "index" : 3,
                              "firstRowId" : 838,
                              "rowCount" : 102,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=690+1*101",
                                "columnNumbers" : "A=9*102"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mssql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 940,
                              "rowCount" : 34,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*33",
                                "columnNumbers" : "A=9*34"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 0,
                              "firstRowId" : 974,
                              "rowCount" : 57,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*56",
                                "columnNumbers" : "A=9*57"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\mysql\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1031,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=68+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1034,
                              "rowCount" : 65,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*64",
                                "columnNumbers" : "A=9*65"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1099,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=76+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\pg\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1104,
                              "rowCount" : 23,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=86+1*22",
                                "columnNumbers" : "A=9*23"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1127,
                              "rowCount" : 7,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*6",
                                "columnNumbers" : "A=9*7"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1134,
                              "rowCount" : 248,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=18+1*247",
                                "columnNumbers" : "A=9*248"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 2,
                              "firstRowId" : 1382,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=271+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sequelize\\model.yml",
                              "index" : 3,
                              "firstRowId" : 1387,
                              "rowCount" : 2,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=281+1",
                                "columnNumbers" : "A=9*2"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1389,
                              "rowCount" : 174,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*173",
                                "columnNumbers" : "A=9*174"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\spanner\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1563,
                              "rowCount" : 5,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=185+1*4",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 0,
                              "firstRowId" : 1568,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*14",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\frameworks\\sqlite3\\model.yml",
                              "index" : 1,
                              "firstRowId" : 1583,
                              "rowCount" : 3,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=26+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isCdnDomainWithCheckingRequired",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\IntegrityCheckingRequired\\integrity_checking_required.model.yml",
                              "index" : 0,
                              "firstRowId" : 1586,
                              "rowCount" : 3,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6+1*2",
                                "columnNumbers" : "A=9*3"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\compromised\\compromised_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1589,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "isUntrustedDomain",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmle\\javascript\\security\\domains\\untrusted\\untrusted_domains.model.yml",
                              "index" : 0,
                              "firstRowId" : 1590,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+1+3+1*3",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 1596,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 1597,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 1612,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 18:07:08] Calling plumbing command: codeql resolve library-path --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\multi_purpose_project\qlconfig.yml --query=E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql --format=json
[2025-06-03 18:07:08] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql.
[2025-06-03 18:07:08] [DETAILS] resolve library-path> Found enclosing pack 'nodejs-security-queries' at E:\advance_javascript\codeQL\7\multi_purpose_project\queries.
[2025-06-03 18:07:08] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-03 18:07:08] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\7\multi_purpose_project\queries\qlpack.yml.
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] nodejs-security-queries: not 1.0.0 {root: nodejs-security-queries@1.0.0}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] nodejs-security-queries: 1.0.0 {nodejs-security-queries: not 1.0.0 {root: nodejs-security-queries@1.0.0}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] nodejs-security-queries: * [*], codeql/javascript-all: not * [*] {dependency: nodejs-security-queries@* [*] requires codeql/javascript-all@*}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 1] nodejs-security-queries: 1.0.0
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] codeql/javascript-all: * [*] {nodejs-security-queries: * [*], codeql/javascript-all: not * [*] {dependency: nodejs-security-queries@* [*] requires codeql/javascript-all@*}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 2] codeql/javascript-all: 2.6.3
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] codeql/yaml: * [*] {codeql/javascript-all: * [*], codeql/yaml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] codeql/xml: * [*] {codeql/javascript-all: * [*], codeql/xml: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] codeql/util: * [*] {codeql/javascript-all: * [*], codeql/util: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] codeql/typetracking: * [*] {codeql/javascript-all: * [*], codeql/typetracking: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] codeql/tutorial: * [*] {codeql/javascript-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] codeql/threat-models: * [*] {codeql/javascript-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] codeql/ssa: * [*] {codeql/javascript-all: * [*], codeql/ssa: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] codeql/regex: * [*] {codeql/javascript-all: * [*], codeql/regex: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] codeql/mad: * [*] {codeql/javascript-all: * [*], codeql/mad: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DERIVATION] codeql/dataflow: * [*] {codeql/javascript-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/javascript-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 18:07:08] [SPAMMY] resolve library-path> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 18:07:08] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\7\multi_purpose_project\queries resolved OK.
[2025-06-03 18:07:08] [DETAILS] resolve library-path> Found dbscheme through QL packs: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\javascript-all\2.6.3\semmlecode.javascript.dbscheme.
[2025-06-03 18:07:08] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\7\\multi_purpose_project\\queries",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\dataflow\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\mad\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\regex\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\ssa\\1.1.2",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\tutorial\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\typetracking\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\xml\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\yaml\\1.0.23"
                        ],
                        "dbscheme" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\javascript-all\\2.6.3\\semmlecode.javascript.dbscheme",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "nodejs-security-queries\\hardcoded-credentials.ql",
                        "qlPackName" : "nodejs-security-queries"
                      }
[2025-06-03 18:07:08] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql.
[2025-06-03 18:07:08] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql.
[2025-06-03 18:07:09] Resolved file set for E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql hashes to 75a10e3ee81824593c407c5f05522d2d.
[2025-06-03 18:07:09] [DETAILS] execute queries> Compilation cache hit for E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql.
[2025-06-03 18:07:09] [SPAMMY] execute queries> No database upgrade/downgrade needed for E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql
[2025-06-03 18:07:10] [PROGRESS] execute queries> [1/1] Found in cache: E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql.
[2025-06-03 18:07:10] [PROGRESS] execute queries> Starting evaluation of nodejs-security-queries\hardcoded-credentials.ql.
[2025-06-03 18:07:10] Starting evaluation of E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql
[2025-06-03 18:07:10] (0s) Start query execution
[2025-06-03 18:07:10] (0s) Beginning execution of E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql
[2025-06-03 18:07:10] (0s)  >>> Created relation exprs/5@a1831agj with 60594 rows and digest d28fd1mgkjp63dcnbu8ttefcmn9.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate exprs_230#join_rhs/3@dfee24vq
[2025-06-03 18:07:10] (0s)  >>> Created relation exprs_230#join_rhs/3@dfee24vq with 60594 rows and digest 3ca753sokp6thig5mff38jdo4v6.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate exprs_10#join_rhs/2@27dbbd1m
[2025-06-03 18:07:10] (0s)  >>> Created relation exprs_10#join_rhs/2@27dbbd1m with 60594 rows and digest aa909ebhcfemqr9cbiocrrbvf48.
[2025-06-03 18:07:10] (0s)  >>> Created relation literals/3@f05366h0 with 34713 rows and digest 2325e3noa4ud07od6r6q1cd7de3.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate literals_20#join_rhs/2@2856f00u
[2025-06-03 18:07:10] (0s)  >>> Created relation literals_20#join_rhs/2@2856f00u with 34713 rows and digest a186d4l52custpcoh67sp71p4e9.
[2025-06-03 18:07:10] (0s)  >>> Created relation typeexprs/5@5ef91d9v with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Inferred that typeexprs_0#antijoin_rhs/1@e1d4c0cm is empty, due to typeexprs/5@5ef91d9v.
[2025-06-03 18:07:10] (0s) Inferred that typeexprs_10#join_rhs/2@af1f3114 is empty, due to typeexprs/5@5ef91d9v.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate @varaccess/1@705690bs
[2025-06-03 18:07:10] (0s)  >>> Created relation @varaccess/1@705690bs with 10045 rows and digest ed5849ogjm7f9v017elkdr7ipvb.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate @varref/1@5caaa75i
[2025-06-03 18:07:10] (0s)  >>> Created relation @varref/1@5caaa75i with 16010 rows and digest 37adc70da3r7277bjh1bkf8cjfb.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::Identifier#299f46d8/1@e1b017pi
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::Identifier#299f46d8/1@e1b017pi with 34518 rows and digest 5eccf4dk3cv8vf48892r9agk0ra.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::Identifier.getName/0#3671b62f/2@904b86vm
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::Identifier.getName/0#3671b62f/2@904b86vm with 34518 rows and digest 92e625reag592qsp3hpddl6ddd4.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Variables::VarRef.getName/0#dispred#287a3fe0/2@0fab3191
[2025-06-03 18:07:10] (0s)  >>> Created relation Variables::VarRef.getName/0#dispred#287a3fe0/2@0fab3191 with 16010 rows and digest bfd4158lpq5ekrvpc0gsufpc641.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::Identifier.getName/0#dispred#1dec9a9a/2@07b00a5l
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::Identifier.getName/0#dispred#1dec9a9a/2@07b00a5l with 34518 rows and digest 92e625reag592qsp3hpddl6ddd4.
[2025-06-03 18:07:10] (0s) No need to promote strings for predicate Expr::Identifier.getName/0#dispred#1dec9a9a  as it does not contain computed strings.
[2025-06-03 18:07:10] (0s)  >>> Created relation cached_Expr::Identifier.getName/0#dispred#1dec9a9a/2@d5770a68 with 34518 rows and digest 92e625reag592qsp3hpddl6ddd4.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate CachedStages::Stages::Ast::backref/0#7ac52a97/0@dab1424s
[2025-06-03 18:07:10] (0s)  >>> Created relation CachedStages::Stages::Ast::backref/0#7ac52a97/0@dab1424s with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate CachedStages::Stages::Ast::ref/0#8de2cf1f/0@72b8f244
[2025-06-03 18:07:10] (0s)  >>> Created relation CachedStages::Stages::Ast::ref/0#8de2cf1f/0@72b8f244 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 18:07:10] (0s) No need to promote strings for predicate CachedStages::Stages::Ast::ref/0#8de2cf1f  as it does not contain computed strings.
[2025-06-03 18:07:10] (0s)  >>> Created relation cached_CachedStages::Stages::Ast::ref/0#8de2cf1f/0@c876115v with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 18:07:10] (0s)  >>> Created relation files/2@ec93749g with 112 rows and digest 0d0708k09m4tpr3u4userl56d5c.
[2025-06-03 18:07:10] (0s)  >>> Created relation locations_default/6@2b5cc3pr with 190466 rows and digest e8bbafhvg1nda5jg3h8urisgii2.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Locations::TDbLocation#dom#677b8f15/1@d1d6f28v
[2025-06-03 18:07:10] (0s)  >>> Created relation Locations::TDbLocation#dom#677b8f15/1@d1d6f28v with 190466 rows and digest bd1a2ehp6qgpdijcvqoiqivc8oe.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate num#Locations::TDbLocation#3d63300d/2@f9f789sa
[2025-06-03 18:07:10] Evaluating HOP construct<Locations#78f633f3::TLocation,0> with inputs:
                        190466 tuples in Locations::TDbLocation#dom#677b8f15/1@d1d6f28v
[2025-06-03 18:07:10] (0s)  >>> Created relation num#Locations::TDbLocation#3d63300d/2@f9f789sa with 190466 rows and digest f0d0f4dbg47eqrjj459b8flm069.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Locations::dbLocationInfo/6#a08cdee9/6@9991d8mh
[2025-06-03 18:07:10] (0s)  >>> Created relation Locations::dbLocationInfo/6#a08cdee9/6@9991d8mh with 190466 rows and digest eba7b1dtj4p41pjtt3k634ujsff.
[2025-06-03 18:07:10] (0s) No need to promote strings for predicate Locations::dbLocationInfo/6#a08cdee9  as it does not contain computed strings.
[2025-06-03 18:07:10] (0s)  >>> Created relation cached_Locations::dbLocationInfo/6#a08cdee9/6@a72875je with 190466 rows and digest eba7b1dtj4p41pjtt3k634ujsff.
[2025-06-03 18:07:10] (0s)  >>> Created relation yaml_locations/2@b504eb06 with 11 rows and digest bb59f0ab5pdsut9ptnkvc6g0lse.
[2025-06-03 18:07:10] (0s)  >>> Created relation json_locations/2@5eb54fmu with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s)  >>> Created relation xmllocations/2@45e0083e with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s)  >>> Created relation hasLocation/2@6de756q0 with 234700 rows and digest bcfe70ea1fmh6dcjf316pmnqrf2.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Locations::getLocatableLocation/1#b180d129/2@d423140t
[2025-06-03 18:07:10] (0s)  >>> Created relation Locations::getLocatableLocation/1#b180d129/2@d423140t with 234711 rows and digest 4b87de7v6jqatoki6mf29cba0jb.
[2025-06-03 18:07:10] (0s) No need to promote strings for predicate Locations::getLocatableLocation/1#b180d129  as it does not contain computed strings.
[2025-06-03 18:07:10] (0s)  >>> Created relation cached_Locations::getLocatableLocation/1#b180d129/2@d8dc0a2h with 234711 rows and digest 4b87de7v6jqatoki6mf29cba0jb.
[2025-06-03 18:07:10] (0s)  >>> Created relation guard_node/3@b8d0317q with 16 rows and digest 0e04189dob9odhm1is68tigardd.
[2025-06-03 18:07:10] (0s)  >>> Created relation properties/5@1908b117 with 95 rows and digest 88cbe3pp7h53ccfghbh5329m2k9.
[2025-06-03 18:07:10] (0s)  >>> Created relation toplevels/2@39c77di1 with 111 rows and digest 66b34crqjavjsa7g6e9nn87gpv4.
[2025-06-03 18:07:10] (0s)  >>> Created relation stmts/5@bcd8ccid with 14072 rows and digest 9f670273mufh4am9uvniu4edak8.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate stmts_10#join_rhs/2@71c68aq8
[2025-06-03 18:07:10] (0s)  >>> Created relation stmts_10#join_rhs/2@71c68aq8 with 14072 rows and digest 812eeegf6p46fjb8qs2mo7euvef.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate properties_10#join_rhs/2@5d3367rj
[2025-06-03 18:07:10] (0s)  >>> Created relation properties_10#join_rhs/2@5d3367rj with 95 rows and digest bbd223ufjv58uqja6ailqndtoh3.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getChild/1#dispred#615b8db2/3@9a4c66g8
[2025-06-03 18:07:10] (0s)  >>> Created relation AST::AstNode.getChild/1#dispred#615b8db2/3@9a4c66g8 with 74761 rows and digest 5745009mr0j0qgkrb92bgmtr4fb.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate _exprs_10#join_rhs#antijoin_rhs#1/1@73bbbbea
[2025-06-03 18:07:10] (0s)  >>> Created relation _exprs_10#join_rhs#antijoin_rhs#1/1@73bbbbea with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate _exprs_10#join_rhs#antijoin_rhs/1@9fb58758
[2025-06-03 18:07:10] (0s)  >>> Created relation _exprs_10#join_rhs#antijoin_rhs/1@9fb58758 with 3092 rows and digest ac6fe6sr7rv6rqmricj4bicfld6.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate @function/1@5f634chc
[2025-06-03 18:07:10] (0s)  >>> Created relation @function/1@5f634chc with 3885 rows and digest 9488cfb25io1p9ntifpcje8i0lc.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Variables::Parameterized#305a8e1f/1@a59a1c43
[2025-06-03 18:07:10] (0s)  >>> Created relation Variables::Parameterized#305a8e1f/1@a59a1c43 with 3885 rows and digest 9488cfb25io1p9ntifpcje8i0lc.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Variables::BindingPattern#efe8ec12/1@97547da4
[2025-06-03 18:07:10] (0s)  >>> Created relation Variables::BindingPattern#efe8ec12/1@97547da4 with 16010 rows and digest 37adc70da3r7277bjh1bkf8cjfb.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Variables::Parameter#a0c1c8e9/1@c1fb94e7
[2025-06-03 18:07:10] (0s)  >>> Created relation Variables::Parameter#a0c1c8e9/1@c1fb94e7 with 4920 rows and digest aac4c1469ihph9srak5hh2k8vl7.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getParent/0#dispred#21e1cceb/2@dbe57dni
[2025-06-03 18:07:10] (0s)  >>> Created relation AST::AstNode.getParent/0#dispred#21e1cceb/2@dbe57dni with 74761 rows and digest c2e183a2mmordhpg19tn948lta1.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::ExprOrType#305a3d05/1@80afab07
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::ExprOrType#305a3d05/1@80afab07 with 60594 rows and digest 2d725f5l2rpomip8151ocjhd04c.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate TypeScript::TypeAssertion#2fbd1ca1/1@c49b68tf
[2025-06-03 18:07:10] (0s)  >>> Created relation TypeScript::TypeAssertion#2fbd1ca1/1@c49b68tf with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate _exprs_10#join_rhs#antijoin_rhs#3/1@00044fe0
[2025-06-03 18:07:10] (0s)  >>> Created relation _exprs_10#join_rhs#antijoin_rhs#3/1@00044fe0 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Inferred that Expr::SeqExpr.getOperand/1#dispred#f959c4fa/3@03d48bc4 is empty, due to _exprs_10#join_rhs#antijoin_rhs#3/1@00044fe0.
[2025-06-03 18:07:10] (0s) Inferred that Expr::SeqExpr.getNumOperands/0#dispred#86a3b8c5/2@cc5a6dto is empty, due to _exprs_10#join_rhs#antijoin_rhs#3/1@00044fe0.
[2025-06-03 18:07:10] (0s) Inferred that project#Expr::SeqExpr.getOperand/1#dispred#f959c4fa/1@1d11ba32 is empty, due to Expr::SeqExpr.getOperand/1#dispred#f959c4fa/3@03d48bc4.
[2025-06-03 18:07:10] (0s) Inferred that Expr::SeqExpr.getOperand/1#dispred#f959c4fa_02#count_range/2@d58fd35m is empty, due to Expr::SeqExpr.getOperand/1#dispred#f959c4fa/3@03d48bc4.
[2025-06-03 18:07:10] (0s) Inferred that Expr::SeqExpr.getLastOperand/0#dispred#0179038c/2@fa2f171p is empty, due to Expr::SeqExpr.getNumOperands/0#dispred#86a3b8c5/2@cc5a6dto.
[2025-06-03 18:07:10] (0s) Inferred that _Expr::SeqExpr.getOperand/1#dispred#f959c4fa_02#count_range#join_rhs/2@7c9eb88l is empty, due to Expr::SeqExpr.getOperand/1#dispred#f959c4fa_02#count_range/2@d58fd35m.
[2025-06-03 18:07:10] (0s) Inferred that Expr::SeqExpr.getLastOperand/0#dispred#0179038c_10#join_rhs/2@f8db31c5 is empty, due to Expr::SeqExpr.getLastOperand/0#dispred#0179038c/2@fa2f171p.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate _exprs_10#join_rhs#antijoin_rhs#5/1@c9105fg2
[2025-06-03 18:07:10] (0s)  >>> Created relation _exprs_10#join_rhs#antijoin_rhs#5/1@c9105fg2 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate _exprs_10#join_rhs#antijoin_rhs#4/1@fd80deh5
[2025-06-03 18:07:10] (0s)  >>> Created relation _exprs_10#join_rhs#antijoin_rhs#4/1@fd80deh5 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate _exprs_10#join_rhs#antijoin_rhs#2/1@c7a2d58t
[2025-06-03 18:07:10] (0s)  >>> Created relation _exprs_10#join_rhs#antijoin_rhs#2/1@c7a2d58t with 4029 rows and digest c8d856k15gmrr163dr27dehmbqd.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::ExprOrType_not_Expr::AssignExpr_Expr::ParExpr_TypeScript::SatisfiesExpr_Expr::SeqExpr_TypeScript::TypeAssertion#b8144dd1/1@cbaa32ej
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::ExprOrType_not_Expr::AssignExpr_Expr::ParExpr_TypeScript::SatisfiesExpr_Expr::SeqExpr_TypeScript::TypeAssertion#b8144dd1/1@cbaa32ej with 56565 rows and digest 5b7f553u2sdp2534mluat2g02o7.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::Assignment#b84ed089/1@8db6e8ao
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::Assignment#b84ed089/1@8db6e8ao with 4029 rows and digest c8d856k15gmrr163dr27dehmbqd.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::Assignment.getRhs/0#dispred#e24981fc/2@5c1e54jf
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::Assignment.getRhs/0#dispred#e24981fc/2@5c1e54jf with 4029 rows and digest 989aed077gm4ra8aj4s5e0ktipd.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs/2@1d8868bv
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs/2@1d8868bv with 4029 rows and digest 0c3443gpaaebsn06t4mugh5dp33.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate exprs_032#join_rhs/3@30fda5lr
[2025-06-03 18:07:10] (0s)  >>> Created relation exprs_032#join_rhs/3@30fda5lr with 60594 rows and digest c5a4118vmhrva3jhm8ejfsee9fc.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::ParExpr.getExpression/0#dispred#70638730/2@e596b40m
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::ParExpr.getExpression/0#dispred#70638730/2@e596b40m with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Inferred that Expr::ParExpr.getExpression/0#dispred#70638730_10#join_rhs/2@f94b08m3 is empty, due to Expr::ParExpr.getExpression/0#dispred#70638730/2@e596b40m.
[2025-06-03 18:07:10] (0s) Inferred that _Expr::ParExpr.getExpression/0#dispred#70638730_10#join_rhs__Expr::ExprOrType#305a3d05_project#Expr:__#join_rhs/2@619dfbbb is empty, due to Expr::ParExpr.getExpression/0#dispred#70638730_10#join_rhs/2@f94b08m3.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@i1#49fc67r8 (iteration 1)
[2025-06-03 18:07:10] (0s) 			 - Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11_delta has 56565 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@i2#49fc67r8 (iteration 2)
[2025-06-03 18:07:10] (0s) 			 - Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11_delta has 4029 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@i3#49fc67r8 (iteration 3)
[2025-06-03 18:07:10] (0s) Empty delta for Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Accumulating deltas
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@49fc67r8 with 60594 rows and digest 324606dm2hv6463uulg82ctpvo2.
[2025-06-03 18:07:10] (0s)  >>> Created relation stmt_containers/2@11b6ea31 with 14072 rows and digest 651b424j97g1kkqjsbmhrlanj89.
[2025-06-03 18:07:10] (0s)  >>> Created relation expr_containers/2@2e4cfcgs with 60594 rows and digest fa9529kp9ggudpittp26isbpnv1.
[2025-06-03 18:07:10] (0s)  >>> Created relation entry_cfg_node/2@563e818j with 3996 rows and digest c37eee75gbo9audr4av8b1qohi2.
[2025-06-03 18:07:10] (0s)  >>> Created relation exit_cfg_node/2@341fbakl with 3996 rows and digest 713cfbjoovdod8lp4aarlg24ki0.
[2025-06-03 18:07:10] (0s)  >>> Created relation cached_CachedStages::Stages::Ast::backref/0#7ac52a97/0@a80d27a0 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getParent/0#dispred#21e1cceb_10#join_rhs/2@7bd7de1r
[2025-06-03 18:07:10] (0s)  >>> Created relation AST::AstNode.getParent/0#dispred#21e1cceb_10#join_rhs/2@7bd7de1r with 74761 rows and digest 4125b8nj2miitapi07jicsdnb62.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i1#5a43exi2 (iteration 1)
[2025-06-03 18:07:10] (0s) Empty delta for AST::AstNode.getTopLevel/0#5d5948b2_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i1#5a43ewi2 (iteration 1)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 111 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i2#5a43exi2 (iteration 2)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 10164 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i3#5a43ewi2 (iteration 3)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 10164 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i4#5a43exi2 (iteration 4)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 11418 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i5#5a43ewi2 (iteration 5)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 11418 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i6#5a43exi2 (iteration 6)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 18525 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i7#5a43ewi2 (iteration 7)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 18525 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i8#5a43exi2 (iteration 8)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 25700 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i9#5a43ewi2 (iteration 9)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 25700 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i10#5a43exi2 (iteration 10)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 7664 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i11#5a43ewi2 (iteration 11)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 7664 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i12#5a43exi2 (iteration 12)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 1271 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i13#5a43ewi2 (iteration 13)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 1271 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i14#5a43exi2 (iteration 14)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 5 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i15#5a43ewi2 (iteration 15)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 5 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i16#5a43exi2 (iteration 16)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 7 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i17#5a43ewi2 (iteration 17)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 7 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i18#5a43exi2 (iteration 18)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 2 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i19#5a43ewi2 (iteration 19)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 2 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i20#5a43exi2 (iteration 20)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 2 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i21#5a43ewi2 (iteration 21)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 2 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i22#5a43exi2 (iteration 22)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 1 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i23#5a43ewi2 (iteration 23)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 1 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i24#5a43exi2 (iteration 24)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#5d5948b2_delta has 2 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#dispred#10343479/2@i25#5a43ewi2 (iteration 25)
[2025-06-03 18:07:10] (0s) 			 - AST::AstNode.getTopLevel/0#dispred#10343479_delta has 2 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getTopLevel/0#5d5948b2/2@i26#5a43exi2 (iteration 26)
[2025-06-03 18:07:10] (0s) Empty delta for AST::AstNode.getTopLevel/0#5d5948b2_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Accumulating deltas
[2025-06-03 18:07:10] (0s)  >>> Created relation AST::AstNode.getTopLevel/0#dispred#10343479/2@5a43ewi2 with 74872 rows and digest acc973djgsc39ht9pt4r99n9s02.
[2025-06-03 18:07:10] (0s)  >>> Created relation AST::AstNode.getTopLevel/0#5d5948b2/2@5a43exi2 with 74761 rows and digest 0c8826opq7bpnipqc4ek8r2htna.
[2025-06-03 18:07:10] (0s) No need to promote strings for predicate AST::AstNode.getTopLevel/0#dispred#10343479  as it does not contain computed strings.
[2025-06-03 18:07:10] (0s)  >>> Created relation cached_AST::AstNode.getTopLevel/0#dispred#10343479/2@0f3bbai4 with 74872 rows and digest acc973djgsc39ht9pt4r99n9s02.
[2025-06-03 18:07:10] (0s) No need to promote strings for predicate AST::AstNode.getParent/0#dispred#21e1cceb  as it does not contain computed strings.
[2025-06-03 18:07:10] (0s)  >>> Created relation cached_AST::AstNode.getParent/0#dispred#21e1cceb/2@790d003s with 74761 rows and digest c2e183a2mmordhpg19tn948lta1.
[2025-06-03 18:07:10] (0s) No need to promote strings for predicate Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11  as it does not contain computed strings.
[2025-06-03 18:07:10] (0s)  >>> Created relation cached_Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11/2@8b4514me with 60594 rows and digest 324606dm2hv6463uulg82ctpvo2.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate exprs_2#antijoin_rhs/1@6182e1q8
[2025-06-03 18:07:10] (0s)  >>> Created relation exprs_2#antijoin_rhs/1@6182e1q8 with 35279 rows and digest 3874899d5n9hbsgsotn1am095pc.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate exprs_20#count_range/2@b5deeecs
[2025-06-03 18:07:10] (0s)  >>> Created relation exprs_20#count_range/2@b5deeecs with 60594 rows and digest 219dfdfqq2s90ou12dih3drcej1.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate _exprs_20#count_range#join_rhs/2@51ef89vg
[2025-06-03 18:07:10] (0s)  >>> Created relation _exprs_20#count_range#join_rhs/2@51ef89vg with 35279 rows and digest ecdec7nouk2vn03mv3a56sf5225.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getNumChildExpr/0#dispred#a4cbe1e8#bf/2@0fb19cd8
[2025-06-03 18:07:10] (0s)  >>> Created relation AST::AstNode.getNumChildExpr/0#dispred#a4cbe1e8#bf/2@0fb19cd8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Inferred that AST::AstNode.getNumChildExpr/0#dispred#a4cbe1e8#bf_10#join_rhs/2@904963u5 is empty, due to AST::AstNode.getNumChildExpr/0#dispred#a4cbe1e8#bf/2@0fb19cd8.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Templates::TemplateLiteral.getElement/1#dispred#9fd3da10#fbf#cpe#13/2@410b62m9
[2025-06-03 18:07:10] (0s)  >>> Created relation Templates::TemplateLiteral.getElement/1#dispred#9fd3da10#fbf#cpe#13/2@410b62m9 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Inferred that Templates::TemplateLiteral.getElement/1#dispred#9fd3da10#fbf#cpe#13_10#join_rhs/2@fc64f5ud is empty, due to Templates::TemplateLiteral.getElement/1#dispred#9fd3da10#fbf#cpe#13/2@410b62m9.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11_10#join_rhs/2@7ebaf2da
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::ExprOrType.getUnderlyingValue/0#dispred#f57e3a11_10#join_rhs/2@7ebaf2da with 60594 rows and digest 0558ebvt6uf28nsrgovm9p3c05b.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate literals_201#join_rhs/3@d5f6d8jq
[2025-06-03 18:07:10] (0s)  >>> Created relation literals_201#join_rhs/3@d5f6d8jq with 34713 rows and digest 562db4gm963o39g79borbb0f8m0.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::getConstantString/1#359f8cc1/2@i1#0a154d13 (iteration 1)
[2025-06-03 18:07:10] (0s) 			 - Expr::getConstantString/1#359f8cc1_delta has 45 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::getConstantString/1#359f8cc1/2@i2#0a154d13 (iteration 2)
[2025-06-03 18:07:10] (0s) Empty delta for Expr::getConstantString/1#359f8cc1_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Accumulating deltas
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::getConstantString/1#359f8cc1/2@0a154d13 with 45 rows and digest bc8bc6v6rb1u48er2cvq6lg7go7.
[2025-06-03 18:07:10] (0s)  >>> Created relation folders/2@bad7d9u0 with 21 rows and digest 31263bbtm8h08it3ibmtpnp74m1.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Files::FsInput::ContainerBase.getAbsolutePath/0#dispred#e3a6eb29/2@966d67vo
[2025-06-03 18:07:10] (0s)  >>> Created relation Files::FsInput::ContainerBase.getAbsolutePath/0#dispred#e3a6eb29/2@966d67vo with 133 rows and digest 3cdebf9lq3mpr971i4sehko7kf4.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b/3@e59cbcdu
[2025-06-03 18:07:10] (0s)  >>> Created relation Files::Container.splitAbsolutePath/2#dispred#43b82b7b/3@e59cbcdu with 378 rows and digest 183773014v33arkjnqpegkt7g69.
[2025-06-03 18:07:10] (0s) Promoting strings for predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b
[2025-06-03 18:07:10] (0s) Promoted strings in predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b in memory, took 8ms
[2025-06-03 18:07:10] (0s) Saving stringpool to save strings from predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b
[2025-06-03 18:07:10] (0s) Saved stringpool to save strings from predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b, took 0ms
[2025-06-03 18:07:10] (0s)  >>> Created relation cached_Files::Container.splitAbsolutePath/2#dispred#43b82b7b/3@c992f399 with 378 rows and digest 183773014v33arkjnqpegkt7g69.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Files::Container.splitAbsolutePath/2#dispred#43b82b7b_021#join_rhs/3@9a865792
[2025-06-03 18:07:10] (0s)  >>> Created relation Files::Container.splitAbsolutePath/2#dispred#43b82b7b_021#join_rhs/3@9a865792 with 378 rows and digest 58daabtmdhn4p77hk1g4bm7v536.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::isAmbientTopLevel/1#ca754f36/1@09f0d91a
[2025-06-03 18:07:10] (0s)  >>> Created relation AST::isAmbientTopLevel/1#ca754f36/1@09f0d91a with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Inferred that _AST::AstNode.getTopLevel/0#dispred#10343479_AST::isAmbientTopLevel/1#ca754f36__@function_AST::AstNo__#antijoin_rhs/1@40b827k3 is empty, due to AST::isAmbientTopLevel/1#ca754f36/1@09f0d91a.
[2025-06-03 18:07:10] (0s) Inferred that cached_AST::isAmbientTopLevel/1#ca754f36/1@4d886ci9 is empty, due to AST::isAmbientTopLevel/1#ca754f36/1@09f0d91a.
[2025-06-03 18:07:10] (0s)  >>> Created relation has_declare_keyword/1@676f31pn with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s)  >>> Created relation has_type_keyword/1@d8c0925e with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.getChild/1#dispred#615b8db2_120#join_rhs/3@94bbd000
[2025-06-03 18:07:10] (0s)  >>> Created relation AST::AstNode.getChild/1#dispred#615b8db2_120#join_rhs/3@94bbd000 with 74761 rows and digest 6af4b14udobke2b7f4ppc7jh7g2.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::ExprOrStmt#142dc628/1@1429d934
[2025-06-03 18:07:10] (0s)  >>> Created relation AST::ExprOrStmt#142dc628/1@1429d934 with 74666 rows and digest 39640av7o6mdij1hrvaaetrg6r7.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate _@function_AST::AstNode.getChild/1#dispred#615b8db2_AST::ExprOrStmt#142dc628#antijoin_rhs/1@ec8df2qf
[2025-06-03 18:07:10] (0s)  >>> Created relation _@function_AST::AstNode.getChild/1#dispred#615b8db2_AST::ExprOrStmt#142dc628#antijoin_rhs/1@ec8df2qf with 3885 rows and digest 9488cfb25io1p9ntifpcje8i0lc.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate _@function_AST::AstNode.getChild/1#dispred#615b8db2_120#join_rhs__@function_AST::AstNode.getChild/1#__#shared/1@52a890m0
[2025-06-03 18:07:10] (0s)  >>> Created relation _@function_AST::AstNode.getChild/1#dispred#615b8db2_120#join_rhs__@function_AST::AstNode.getChild/1#__#shared/1@52a890m0 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate AST::AstNode.isAmbientInternal/0#ebd19382/1@i1#ed1d7ekc (iteration 1)
[2025-06-03 18:07:10] (0s) Empty delta for AST::AstNode.isAmbientInternal/0#ebd19382_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:10] (0s) Accumulating deltas
[2025-06-03 18:07:10] (0s)  >>> Created relation AST::AstNode.isAmbientInternal/0#ebd19382/1@ed1d7ekc with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Inferred that cached_AST::AstNode.isAmbientInternal/0#ebd19382/1@43d67air is empty, due to AST::AstNode.isAmbientInternal/0#ebd19382/1@ed1d7ekc.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::BinaryExpr#18c21d7d/1@49e1f1kg
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::BinaryExpr#18c21d7d/1@49e1f1kg with 9 rows and digest d813e6nv2m63d2frln5v61pi8n2.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Constants::SyntacticConstants::PrimitiveLiteralConstant#303549d7/1@e4c7adrv
[2025-06-03 18:07:10] (0s)  >>> Created relation Constants::SyntacticConstants::PrimitiveLiteralConstant#303549d7/1@e4c7adrv with 191 rows and digest 41fcd9kbihp7h20bor0kjstr69e.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Constants::SyntacticConstants::NullConstant#5b41c79d/1@63281f6a
[2025-06-03 18:07:10] (0s)  >>> Created relation Constants::SyntacticConstants::NullConstant#5b41c79d/1@63281f6a with 4 rows and digest 76eb2fd0rpt771lpggu9i34qj55.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::UnaryExpr#56508658/1@a07fdb1d
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::UnaryExpr#56508658/1@a07fdb1d with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Inferred that Constants::SyntacticConstants::UnaryConstant#7f985313/1@874180wp is empty, due to Expr::UnaryExpr#56508658/1@a07fdb1d.
[2025-06-03 18:07:10] (0s) Inferred that cached_Constants::SyntacticConstants::UnaryConstant#7f985313/1@6b76df2t is empty, due to Constants::SyntacticConstants::UnaryConstant#7f985313/1@874180wp.
[2025-06-03 18:07:10] (0s) No need to promote strings for predicate Constants::SyntacticConstants::NullConstant#5b41c79d  as it does not contain computed strings.
[2025-06-03 18:07:10] (0s)  >>> Created relation cached_Constants::SyntacticConstants::NullConstant#5b41c79d/1@b9abb1u7 with 4 rows and digest 76eb2fd0rpt771lpggu9i34qj55.
[2025-06-03 18:07:10] (0s) No need to promote strings for predicate Constants::SyntacticConstants::PrimitiveLiteralConstant#303549d7  as it does not contain computed strings.
[2025-06-03 18:07:10] (0s)  >>> Created relation cached_Constants::SyntacticConstants::PrimitiveLiteralConstant#303549d7/1@5e66f2go with 191 rows and digest 41fcd9kbihp7h20bor0kjstr69e.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::Literal#62045a1b/1@81201fg9
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::Literal#62045a1b/1@81201fg9 with 195 rows and digest 71dc241hn9k235g896hc7bucl49.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::Literal.getValue/0#dispred#c75e715e/2@806438ih
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::Literal.getValue/0#dispred#c75e715e/2@806438ih with 195 rows and digest 9125bag943n2mhfl7lt8p0b30i8.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Expr::Assignment#b84ed089/1@16fa64dv
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::Assignment#b84ed089/1@16fa64dv with 4029 rows and digest c8d856k15gmrr163dr27dehmbqd.
[2025-06-03 18:07:10] (0s)  >>> Created relation Expr::Assignment.getRhs/0#dispred#e24981fc/2@5545891e with 4029 rows and digest 989aed077gm4ra8aj4s5e0ktipd.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate m#Variables::BindingPattern.getName/0#dispred#38c8fb21#bf/1@28f864ju
[2025-06-03 18:07:10] (0s)  >>> Created relation m#Variables::BindingPattern.getName/0#dispred#38c8fb21#bf/1@28f864ju with 4029 rows and digest 9ce963u3tkdo5bsqf89pocvqtdd.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate m#Variables::VarRef#38254975#b/1@861b933h
[2025-06-03 18:07:10] (0s)  >>> Created relation m#Variables::VarRef#38254975#b/1@861b933h with 4029 rows and digest 9ce963u3tkdo5bsqf89pocvqtdd.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Variables::VarRef#38254975#b/1@639fdfd1
[2025-06-03 18:07:10] (0s)  >>> Created relation Variables::VarRef#38254975#b/1@639fdfd1 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:10] (0s) Inferred that Variables::BindingPattern.getName/0#dispred#38c8fb21#bf/2@1051e2mq is empty, due to Variables::VarRef#38254975#b/1@639fdfd1.
[2025-06-03 18:07:10] (0s) Inferred that #select/2@ef4acao4 is empty, due to Variables::VarRef#38254975#b/1@639fdfd1.
[2025-06-03 18:07:10] (0s) Inferred that _#select_exprs_exprs_0#antijoin_rhs#shared/3@6d8a82fi is empty, due to #select/2@ef4acao4.
[2025-06-03 18:07:10] (0s) Inferred that _Locations::getLocatableLocation/1#b180d129__#select_exprs_exprs_0#antijoin_rhs#shared#shared/4@09c299g5 is empty, due to _#select_exprs_exprs_0#antijoin_rhs#shared/3@6d8a82fi.
[2025-06-03 18:07:10] (0s) Inferred that #select#query/8@8c46c8ea is empty, due to _#select_exprs_exprs_0#antijoin_rhs#shared/3@6d8a82fi.
[2025-06-03 18:07:10] (0s) Inferred that _Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a__Locations::getLocatableLocation/1#b180d12__#antijoin_rhs/3@96a198h1 is empty, due to _Locations::getLocatableLocation/1#b180d129__#select_exprs_exprs_0#antijoin_rhs#shared#shared/4@09c299g5.
[2025-06-03 18:07:10] (0s)  >>> Created relation jsdoc/3@a172dbu7 with 9173 rows and digest b883cbio1fq2m7bcud8mcf5iebe.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate jsdoc_20#join_rhs/2@f78685i3
[2025-06-03 18:07:10] (0s)  >>> Created relation jsdoc_20#join_rhs/2@f78685i3 with 9173 rows and digest 64ad15dio3jmn2r83tambe6eha2.
[2025-06-03 18:07:10] (0s)  >>> Created relation next_token/2@e2a2b9na with 10591 rows and digest 860b9e3e0ipejdm545n36q5ean0.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate m#AST::AstNode.getFirstToken/0#dispred#b897a56e#fb/1@d534dfb1
[2025-06-03 18:07:10] (0s)  >>> Created relation m#AST::AstNode.getFirstToken/0#dispred#b897a56e#fb/1@d534dfb1 with 9084 rows and digest 9aa4743nue7phrr08qi1fktvrdb.
[2025-06-03 18:07:10] (0s)  >>> Created relation tokeninfo/5@cf3f533v with 89093 rows and digest 6c9180t2mk4pjmp5p1qnjd35ef7.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate tokeninfo_230#join_rhs/3@45c6b3mi
[2025-06-03 18:07:10] (0s)  >>> Created relation tokeninfo_230#join_rhs/3@45c6b3mi with 89093 rows and digest 183f705m6kakh6qiqlgkamcft21.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Tokens::Token.getPreviousToken/0#dispred#924795eb#fb/2@7a247df5
[2025-06-03 18:07:10] (0s)  >>> Created relation Tokens::Token.getPreviousToken/0#dispred#924795eb#fb/2@7a247df5 with 9084 rows and digest f5e9ffa4qft655faf004loau6l8.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate m#AST::AstNode.getFirstToken/0#5411fa9d#fb/1@3b24a9ir
[2025-06-03 18:07:10] (0s)  >>> Created relation m#AST::AstNode.getFirstToken/0#5411fa9d#fb/1@3b24a9ir with 18168 rows and digest f2a793rbvq12gvvanh68mv7ssa1.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a/6@1a6e1eqp
[2025-06-03 18:07:10] (0s)  >>> Created relation Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a/6@1a6e1eqp with 190466 rows and digest 7086b6rsfus8jh6qib48dpt6bhe.
[2025-06-03 18:07:10] (0s) Starting to evaluate predicate Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a_1230#join_rhs/4@ec80ba11
[2025-06-03 18:07:11] (0s)  >>> Created relation Locations::DbLocation.hasLocationInfo/5#dispred#85ba015a_1230#join_rhs/4@ec80ba11 with 190466 rows and digest 335cf2a1nfu4dgsd7ac58uscab6.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate Locations::getLocatableLocation/1#b180d129_10#join_rhs/2@54c47d1u
[2025-06-03 18:07:11] (0s)  >>> Created relation Locations::getLocatableLocation/1#b180d129_10#join_rhs/2@54c47d1u with 234711 rows and digest a3ecc9ruphaugrfvtpoqd2j0gec.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate AST::AstNode#17d81be7/1@e3bd09n9
[2025-06-03 18:07:11] (0s)  >>> Created relation AST::AstNode#17d81be7/1@e3bd09n9 with 74872 rows and digest dab0d9tcn1r02slsn3igq7ppro9.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate AST::AstNode.getFirstToken/0#5411fa9d#fb/2@03f9bfa8
[2025-06-03 18:07:11] (0s)  >>> Created relation AST::AstNode.getFirstToken/0#5411fa9d#fb/2@03f9bfa8 with 37066 rows and digest 8591feget89gtpi99sopvaaujef.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate xUnit::IndexExprIndexIsBracketedListOfExpressions#3ad78363/1@016ab9t0
[2025-06-03 18:07:11] (0s)  >>> Created relation xUnit::IndexExprIndexIsBracketedListOfExpressions#3ad78363/1@016ab9t0 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate AST::AstNode.getFirstToken/0#dispred#b897a56e#fb/2@c31219k7
[2025-06-03 18:07:11] (0s)  >>> Created relation AST::AstNode.getFirstToken/0#dispred#b897a56e#fb/2@c31219k7 with 35845 rows and digest a33b81q0lou6rc3akd4iq2vqvhc.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate AST::AstNode.getFirstToken/0#dispred#b897a56e#fb_10#join_rhs/2@a969a52l
[2025-06-03 18:07:11] (0s)  >>> Created relation AST::AstNode.getFirstToken/0#dispred#b897a56e#fb_10#join_rhs/2@a969a52l with 35845 rows and digest 96e5ae8v4d6h9h20838r7ujnfs9.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate Expr::ExprOrType.getOwnDocumentation/0#dispred#3c812d8f/2@e735f4an
[2025-06-03 18:07:11] (0s)  >>> Created relation Expr::ExprOrType.getOwnDocumentation/0#dispred#3c812d8f/2@e735f4an with 29977 rows and digest 0a53a22ahqumg96qjf91edgmdec.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate project#Expr::ExprOrType.getOwnDocumentation/0#dispred#3c812d8f/1@fee808e7
[2025-06-03 18:07:11] (0s)  >>> Created relation project#Expr::ExprOrType.getOwnDocumentation/0#dispred#3c812d8f/1@fee808e7 with 29858 rows and digest 6e9939tgtff71cge0c1qkq503n3.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate _Expr::ExprOrType#305a3d05_project#Expr::ExprOrType.getOwnDocumentation/0#dispred#3c812d8f#shared/1@3eb148b0
[2025-06-03 18:07:11] (0s)  >>> Created relation _Expr::ExprOrType#305a3d05_project#Expr::ExprOrType.getOwnDocumentation/0#dispred#3c812d8f#shared/1@3eb148b0 with 30736 rows and digest 0fd0ectskjsaccfqqdeefjmndd7.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate _AST::AstNode.getParent/0#dispred#21e1cceb__Expr::ExprOrType#305a3d05_project#Expr::ExprOrType.getOw__#join_rhs/2@07b731kl
[2025-06-03 18:07:11] (0s)  >>> Created relation _AST::AstNode.getParent/0#dispred#21e1cceb__Expr::ExprOrType#305a3d05_project#Expr::ExprOrType.getOw__#join_rhs/2@07b731kl with 375 rows and digest 6d0093n703isgbqp6ormbgr5hk9.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate Expr::Property#e5a4273a/1@cd6f62vd
[2025-06-03 18:07:11] (0s)  >>> Created relation Expr::Property#e5a4273a/1@cd6f62vd with 95 rows and digest 3c6a10se6lrq689fmobnhfqlvib.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate _AST::AstNode.getParent/0#dispred#21e1cceb_Expr::Property#e5a4273a__Expr::ExprOrType#305a3d05_projec__#join_rhs/2@619f36t1
[2025-06-03 18:07:11] (0s)  >>> Created relation _AST::AstNode.getParent/0#dispred#21e1cceb_Expr::Property#e5a4273a__Expr::ExprOrType#305a3d05_projec__#join_rhs/2@619f36t1 with 190 rows and digest f1f9e9ik4k4etj2n0n0si61diec.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate _Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs__Expr::ExprOrType#305a3d05_project#Expr::Exp__#join_rhs/2@7f6c28s0
[2025-06-03 18:07:11] (0s)  >>> Created relation _Expr::Assignment.getRhs/0#dispred#e24981fc_10#join_rhs__Expr::ExprOrType#305a3d05_project#Expr::Exp__#join_rhs/2@7f6c28s0 with 932 rows and digest a345d336oghhn4pd834gfv5ks40.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate Expr::DotExpr.getProperty/0#dispred#8bb313c1/2@c39c5f7j
[2025-06-03 18:07:11] (0s)  >>> Created relation Expr::DotExpr.getProperty/0#dispred#8bb313c1/2@c39c5f7j with 18413 rows and digest 48b82dh5r4hntmtp6p2j1ghr0bb.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate Expr::DotExpr.getProperty/0#dispred#8bb313c1_10#join_rhs/2@37c998ai
[2025-06-03 18:07:11] (0s)  >>> Created relation Expr::DotExpr.getProperty/0#dispred#8bb313c1_10#join_rhs/2@37c998ai with 18413 rows and digest b67a16gpk26fmr0fbdl8l9ni501.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate _Expr::DotExpr.getProperty/0#dispred#8bb313c1_10#join_rhs__Expr::ExprOrType#305a3d05_project#Expr::E__#join_rhs/2@8dcd69vq
[2025-06-03 18:07:11] (0s)  >>> Created relation _Expr::DotExpr.getProperty/0#dispred#8bb313c1_10#join_rhs__Expr::ExprOrType#305a3d05_project#Expr::E__#join_rhs/2@8dcd69vq with 18413 rows and digest 48b82dh5r4hntmtp6p2j1ghr0bb.
[2025-06-03 18:07:11] (0s)  >>> Created relation is_method/1@227214e8 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:11] (0s) Inferred that _AST::AstNode.getParent/0#dispred#21e1cceb_Classes::MemberDeclaration#5cd163da_Expr::ExprOrType#305a__#join_rhs/2@9fab4bq7 is empty, due to is_method/1@227214e8.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate Stmt::DeclStmt#28398b2c/1@fb8d64fs
[2025-06-03 18:07:11] (0s)  >>> Created relation Stmt::DeclStmt#28398b2c/1@fb8d64fs with 250 rows and digest a3af943usk1j6gri14gredubob8.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate exprs_12034#join_rhs/5@d7abdd0t
[2025-06-03 18:07:11] (0s)  >>> Created relation exprs_12034#join_rhs/5@d7abdd0t with 60594 rows and digest 0a26f7prrqme20gapjfbrvut085.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate Stmt::DeclStmt.getDecl/1#dispred#a1ae5d80/3@e7aedd0n
[2025-06-03 18:07:11] (0s)  >>> Created relation Stmt::DeclStmt.getDecl/1#dispred#a1ae5d80/3@e7aedd0n with 250 rows and digest 3a943f1lgasbi7ri9r5ctfprfn7.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate Stmt::DeclStmt.getDecl/1#dispred#a1ae5d80_120#join_rhs/3@1a34d0fj
[2025-06-03 18:07:11] (0s)  >>> Created relation Stmt::DeclStmt.getDecl/1#dispred#a1ae5d80_120#join_rhs/3@1a34d0fj with 250 rows and digest 313dc35pjaus9039sa6g82hl28c.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate _Stmt::DeclStmt.getDecl/1#dispred#a1ae5d80_120#join_rhs__Expr::ExprOrType#305a3d05_project#Expr::Exp__#join_rhs/2@973438il
[2025-06-03 18:07:11] (0s)  >>> Created relation _Stmt::DeclStmt.getDecl/1#dispred#a1ae5d80_120#join_rhs__Expr::ExprOrType#305a3d05_project#Expr::Exp__#join_rhs/2@973438il with 250 rows and digest 818510mb8gtjp9b11mvgeboprc7.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate Classes::ClassOrInterface#9b4808c4/1@81ce12ng
[2025-06-03 18:07:11] (0s)  >>> Created relation Classes::ClassOrInterface#9b4808c4/1@81ce12ng with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:11] (0s) Inferred that Classes::MemberDeclaration#5cd163da/1@d51f56rt is empty, due to Classes::ClassOrInterface#9b4808c4/1@81ce12ng.
[2025-06-03 18:07:11] (0s) Starting to evaluate predicate JSDoc::Documentable#614b2543/1@c3dea9sv
[2025-06-03 18:07:11] (1s)  >>> Created relation JSDoc::Documentable#614b2543/1@c3dea9sv with 17259 rows and digest 9b4481t9a6uvdsbfeueidp5psv1.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate next_token_10#join_rhs/2@cc93939p
[2025-06-03 18:07:11] (1s)  >>> Created relation next_token_10#join_rhs/2@cc93939p with 10591 rows and digest 0c2ba3qfrbmracrdhdjp443jvif.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::Documentable.getDocumentation/0#5aa175df/2@170516el
[2025-06-03 18:07:11] (1s)  >>> Created relation JSDoc::Documentable.getDocumentation/0#5aa175df/2@170516el with 9173 rows and digest 9f59170gbb3tkf282cs2dvkelsc.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@i1#a8a9e47r (iteration 1)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69_delta has 39150 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@i2#a8a9e47r (iteration 2)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69_delta has 15880 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@i3#a8a9e47r (iteration 3)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69_delta has 338 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@i4#a8a9e47r (iteration 4)
[2025-06-03 18:07:11] (1s) Empty delta for JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Accumulating deltas
[2025-06-03 18:07:11] (1s)  >>> Created relation JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@a8a9e47r with 55368 rows and digest 054ce4tc341epdca7bdu5angk7a.
[2025-06-03 18:07:11] (1s) No need to promote strings for predicate JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69  as it does not contain computed strings.
[2025-06-03 18:07:11] (1s)  >>> Created relation cached_JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69/2@3216d56j with 55368 rows and digest 054ce4tc341epdca7bdu5angk7a.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69_10#join_rhs/2@32eb53o2
[2025-06-03 18:07:11] (1s)  >>> Created relation JSDoc::Documentable.getDocumentation/0#dispred#70bb8a69_10#join_rhs/2@32eb53o2 with 55368 rows and digest 39bf23v5f7n2l5aoutbbeij9ib0.
[2025-06-03 18:07:11] (1s)  >>> Created relation jsdoc_tags/5@1637af13 with 18122 rows and digest a40d3frdjcanr2e8ks2nstfe07a.
[2025-06-03 18:07:11] (1s)  >>> Created relation jsdoc_type_exprs/5@e8bf5dvc with 24686 rows and digest 6a1d0edanecr26h0ktvigg5ks66.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate jsdoc_type_exprs_2#join_rhs/1@d69010dm
[2025-06-03 18:07:11] (1s)  >>> Created relation jsdoc_type_exprs_2#join_rhs/1@d69010dm with 20662 rows and digest 5227cav4ni6ba4611vnasvlm9u7.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate jsdoc_type_exprs_20#join_rhs/2@137d08st
[2025-06-03 18:07:11] (1s)  >>> Created relation jsdoc_type_exprs_20#join_rhs/2@137d08st with 24686 rows and digest 714233n5cvlpq2k47et1hf7fk90.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f/2@i1#a56b7xt8 (iteration 1)
[2025-06-03 18:07:11] (1s) Empty delta for JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf/2@i1#a56b7wt8 (iteration 1)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf_delta has 13365 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f/2@i2#a56b7xt8 (iteration 2)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f_delta has 13365 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf/2@i3#a56b7wt8 (iteration 3)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf_delta has 4209 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f/2@i4#a56b7xt8 (iteration 4)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f_delta has 5908 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf/2@i5#a56b7wt8 (iteration 5)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf_delta has 1775 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f/2@i6#a56b7xt8 (iteration 6)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f_delta has 3185 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf/2@i7#a56b7wt8 (iteration 7)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf_delta has 893 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f/2@i8#a56b7xt8 (iteration 8)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f_delta has 1515 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf/2@i9#a56b7wt8 (iteration 9)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf_delta has 309 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f/2@i10#a56b7xt8 (iteration 10)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f_delta has 515 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf/2@i11#a56b7wt8 (iteration 11)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf_delta has 101 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f/2@i12#a56b7xt8 (iteration 12)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f_delta has 180 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf/2@i13#a56b7wt8 (iteration 13)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf_delta has 7 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f/2@i14#a56b7xt8 (iteration 14)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f_delta has 12 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf/2@i15#a56b7wt8 (iteration 15)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf_delta has 3 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f/2@i16#a56b7xt8 (iteration 16)
[2025-06-03 18:07:11] (1s) 			 - JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f_delta has 6 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf/2@i17#a56b7wt8 (iteration 17)
[2025-06-03 18:07:11] (1s) Empty delta for JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Accumulating deltas
[2025-06-03 18:07:11] (1s)  >>> Created relation JSDoc::JSDocTypeExprParent.getJSDocComment/0#dispred#c87c66f5#bf/2@a56b7wt8 with 20662 rows and digest ba3f5e41revscod2ncvqemvf021.
[2025-06-03 18:07:11] (1s)  >>> Created relation JSDoc::JSDocTypeExpr.getJSDocComment/0#dispred#1834753f/2@a56b7xt8 with 24686 rows and digest feef529bi1q52cr5jgv5gk74tn2.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Expr::Property.getObjectExpr/0#dispred#fcf8662c/2@6070dd0b
[2025-06-03 18:07:11] (1s)  >>> Created relation Expr::Property.getObjectExpr/0#dispred#fcf8662c/2@6070dd0b with 95 rows and digest 0e992978bqabl7ikamos539ojj1.
[2025-06-03 18:07:11] (1s)  >>> Created relation enclosing_stmt/2@31d26ca0 with 54879 rows and digest a134fd7plgig3aebg8m2rlf3eh0.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Expr::ExprOrType_not_Expr::ArrowFunctionExpr_Expr::FunctionExpr_TypeScript::TypeExpr#111bb8f5/1@6e5ba90g
[2025-06-03 18:07:11] (1s)  >>> Created relation Expr::ExprOrType_not_Expr::ArrowFunctionExpr_Expr::FunctionExpr_TypeScript::TypeExpr#111bb8f5/1@6e5ba90g with 57502 rows and digest 35a55bb4n735lgq1m8v2cec0dg2.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Expr::ExprOrType.getEnclosingStmt/0#dispred#29832c05/2@49331e08
[2025-06-03 18:07:11] (1s)  >>> Created relation Expr::ExprOrType.getEnclosingStmt/0#dispred#29832c05/2@49331e08 with 54879 rows and digest a134fd7plgig3aebg8m2rlf3eh0.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate JSDoc::JSDocTypeExpr.getEnclosingStmt/0#dispred#1ddce613/2@c958f18v
[2025-06-03 18:07:11] (1s)  >>> Created relation JSDoc::JSDocTypeExpr.getEnclosingStmt/0#dispred#1ddce613/2@c958f18v with 24686 rows and digest a1ae34frllol0rpqlmcvft0iobc.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate StmtContainers::getStmtContainer/1#845eb172/2@8b6734vr
[2025-06-03 18:07:11] (1s)  >>> Created relation StmtContainers::getStmtContainer/1#845eb172/2@8b6734vr with 107455 rows and digest da72a06p626odktnpj02l1asep7.
[2025-06-03 18:07:11] (1s) No need to promote strings for predicate StmtContainers::getStmtContainer/1#845eb172  as it does not contain computed strings.
[2025-06-03 18:07:11] (1s)  >>> Created relation cached_StmtContainers::getStmtContainer/1#845eb172/2@7374fbre with 107455 rows and digest da72a06p626odktnpj02l1asep7.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate AST::StmtContainer.getEnclosingContainer/0#dispred#77c848c6/2@aa2751ia
[2025-06-03 18:07:11] (1s)  >>> Created relation AST::StmtContainer.getEnclosingContainer/0#dispred#77c848c6/2@aa2751ia with 3885 rows and digest d3ad4fltl117bbgssupdjdoa08e.
[2025-06-03 18:07:11] (1s) No need to promote strings for predicate AST::StmtContainer.getEnclosingContainer/0#dispred#77c848c6  as it does not contain computed strings.
[2025-06-03 18:07:11] (1s)  >>> Created relation cached_AST::StmtContainer.getEnclosingContainer/0#dispred#77c848c6/2@36e74d65 with 3885 rows and digest d3ad4fltl117bbgssupdjdoa08e.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Expr::getAnAddOperand/1#c448e049/2@935276c6
[2025-06-03 18:07:11] (1s)  >>> Created relation Expr::getAnAddOperand/1#c448e049/2@935276c6 with 4 rows and digest 7387b8gddt17c9q26mcmgjdi1ma.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Expr::getAnAddOperand/1#c448e049_10#higher_order_body/2@5797a6k7
[2025-06-03 18:07:11] (1s)  >>> Created relation Expr::getAnAddOperand/1#c448e049_10#higher_order_body/2@5797a6k7 with 4 rows and digest 6547cdjbl5tvvhme3oua1k4n1p6.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate project#Expr::getConstantString/1#359f8cc1/1@69d17fpn
[2025-06-03 18:07:11] (1s)  >>> Created relation project#Expr::getConstantString/1#359f8cc1/1@69d17fpn with 45 rows and digest f53709443684qhe13cqgosgdh22.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate project#Expr::getAnAddOperand/1#c448e049/1@a4abe262
[2025-06-03 18:07:11] (1s)  >>> Created relation project#Expr::getAnAddOperand/1#c448e049/1@a4abe262 with 2 rows and digest 399ba6helq3jinni1qiahe2186f.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate _project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_body/1@4548cdas
[2025-06-03 18:07:11] (1s)  >>> Created relation _project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_body/1@4548cdas with 45 rows and digest f53709443684qhe13cqgosgdh22.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate boundedFastTC:Expr::getAnAddOperand/1#c448e049_10#higher_order_body:_project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_body/2@dc94704c = HOP boundedFastTC(4,45)
[2025-06-03 18:07:11] (1s)  >>> Relation boundedFastTC:Expr::getAnAddOperand/1#c448e049_10#higher_order_body:_project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_body: 2 rows using 0 MB
[2025-06-03 18:07:11] (1s)  >>> Created relation boundedFastTC:Expr::getAnAddOperand/1#c448e049_10#higher_order_body:_project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_body/2@dc94704c with 2 rows and digest e93bc8pn7e16v8k7og9no7rtti8.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate __project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_b__#shared/1@d3c2d0lm
[2025-06-03 18:07:11] (1s)  >>> Created relation __project#Expr::getAnAddOperand/1#c448e049_project#Expr::getConstantString/1#359f8cc1#higher_order_b__#shared/1@d3c2d0lm with 2 rows and digest 399ba6helq3jinni1qiahe2186f.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate #Expr::getAnAddOperand/1#c448e049Plus/2@bb2097j3 = HOP fastTC(4)
[2025-06-03 18:07:11] (1s)  >>> Relation #Expr::getAnAddOperand/1#c448e049Plus: 4 rows using 0 MB
[2025-06-03 18:07:11] (1s)  >>> Created relation #Expr::getAnAddOperand/1#c448e049Plus/2@bb2097j3 with 4 rows and digest 7387b8gddt17c9q26mcmgjdi1ma.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate _#Expr::getAnAddOperand/1#c448e049Plus___project#Expr::getAnAddOperand/1#c448e049_project#Expr::getC__#antijoin_rhs/1@dccbf28s
[2025-06-03 18:07:11] (1s)  >>> Created relation _#Expr::getAnAddOperand/1#c448e049Plus___project#Expr::getAnAddOperand/1#c448e049_project#Expr::getC__#antijoin_rhs/1@dccbf28s with 2 rows and digest 399ba6helq3jinni1qiahe2186f.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Expr::hasAllConstantLeafs/1#729c3ae8/1@eb5b3b4h
[2025-06-03 18:07:11] (1s)  >>> Created relation Expr::hasAllConstantLeafs/1#729c3ae8/1@eb5b3b4h with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:11] (1s) Inferred that _Expr::getAnAddOperand/1#c448e049_10#higher_order_body_Expr::hasAllConstantLeafs/1#729c3ae8#antijoin_rhs/2@d14219me is empty, due to Expr::hasAllConstantLeafs/1#729c3ae8/1@eb5b3b4h.
[2025-06-03 18:07:11] (1s) Inferred that _Expr::hasAllConstantLeafs/1#729c3ae8__Expr::getAnAddOperand/1#c448e049_10#higher_order_body_Expr::h__#shared/1@d273e1rj is empty, due to Expr::hasAllConstantLeafs/1#729c3ae8/1@eb5b3b4h.
[2025-06-03 18:07:11] (1s) Inferred that Expr::SmallConcatRoot#3606cd2c/1@bcb9de59 is empty, due to Expr::hasAllConstantLeafs/1#729c3ae8/1@eb5b3b4h.
[2025-06-03 18:07:11] (1s) Inferred that _#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeafs/1#729c3ae8__Expr::getAnAddOperand/__#shared/2@593e0af2 is empty, due to _Expr::hasAllConstantLeafs/1#729c3ae8__Expr::getAnAddOperand/1#c448e049_10#higher_order_body_Expr::h__#shared/1@d273e1rj.
[2025-06-03 18:07:11] (1s) Inferred that _#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#3606cd2c_Locations::dbLocationInfo/6#a0__#shared/4@a12ed952 is empty, due to Expr::SmallConcatRoot#3606cd2c/1@bcb9de59.
[2025-06-03 18:07:11] (1s) Inferred that _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeaf__#sum_range/3@04579836 is empty, due to _#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeafs/1#729c3ae8__Expr::getAnAddOperand/__#shared/2@593e0af2.
[2025-06-03 18:07:11] (1s) Inferred that _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeaf__#sum_term/4@778679b5 is empty, due to _#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeafs/1#729c3ae8__Expr::getAnAddOperand/__#shared/2@593e0af2.
[2025-06-03 18:07:11] (1s) Inferred that _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeaf__#antijoin_rhs/2@62b41d4j is empty, due to _#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeafs/1#729c3ae8__Expr::getAnAddOperand/__#shared/2@593e0af2.
[2025-06-03 18:07:11] (1s) Inferred that _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#360__#concat_range/6@4ebea2lc is empty, due to _#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#3606cd2c_Locations::dbLocationInfo/6#a0__#shared/4@a12ed952.
[2025-06-03 18:07:11] (1s) Inferred that _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#360__#concat_term/10@e5df30gj is empty, due to _#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#3606cd2c_Locations::dbLocationInfo/6#a0__#shared/4@a12ed952.
[2025-06-03 18:07:11] (1s) Inferred that __Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLea__#join_rhs/2@5bc876ld is empty, due to _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus__Expr::hasAllConstantLeaf__#sum_range/3@04579836.
[2025-06-03 18:07:11] (1s) Inferred that Expr::getConcatenatedString/1#d0ffb08b/2@6792082s is empty, due to _Expr::getConstantString/1#359f8cc1__#Expr::getAnAddOperand/1#c448e049Plus_Expr::SmallConcatRoot#360__#concat_range/6@4ebea2lc.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Expr::Expr.getStringValue/0#dispred#d7c72429/2@b6c11e34
[2025-06-03 18:07:11] (1s)  >>> Created relation Expr::Expr.getStringValue/0#dispred#d7c72429/2@b6c11e34 with 45 rows and digest bc8bc6v6rb1u48er2cvq6lg7go7.
[2025-06-03 18:07:11] (1s) Promoting strings for predicate Expr::Expr.getStringValue/0#dispred#d7c72429
[2025-06-03 18:07:11] (1s) Promoted strings in predicate Expr::Expr.getStringValue/0#dispred#d7c72429 in memory, took 0ms
[2025-06-03 18:07:11] (1s) Saving stringpool to save strings from predicate Expr::Expr.getStringValue/0#dispred#d7c72429
[2025-06-03 18:07:11] (1s) Saved stringpool to save strings from predicate Expr::Expr.getStringValue/0#dispred#d7c72429, took 0ms
[2025-06-03 18:07:11] (1s)  >>> Created relation cached_Expr::Expr.getStringValue/0#dispred#d7c72429/2@a455d204 with 45 rows and digest bc8bc6v6rb1u48er2cvq6lg7go7.
[2025-06-03 18:07:11] (1s)  >>> Created relation Constants::ConstantString#e55e40cc/1@6b166a7j with 45 rows and digest f53709443684qhe13cqgosgdh22.
[2025-06-03 18:07:11] (1s) No need to promote strings for predicate Constants::ConstantString#e55e40cc  as it does not contain computed strings.
[2025-06-03 18:07:11] (1s)  >>> Created relation cached_Constants::ConstantString#e55e40cc/1@43d594jb with 45 rows and digest f53709443684qhe13cqgosgdh22.
[2025-06-03 18:07:11] (1s)  >>> Created relation scopes/2@52faa9si with 3926 rows and digest 7aab5bghuhtn95tj4969914sk3f.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate scopes_10#join_rhs/2@a5da03n1
[2025-06-03 18:07:11] (1s)  >>> Created relation scopes_10#join_rhs/2@a5da03n1 with 3926 rows and digest eb7259ihruhfo1bnjsji6ovoak8.
[2025-06-03 18:07:11] (1s)  >>> Created relation variables/3@fcf622l5 with 10018 rows and digest 4d87ecv4t9m0cqs3rv6fe80hth5.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate variables_20#join_rhs/2@85c1f447
[2025-06-03 18:07:11] (1s)  >>> Created relation variables_20#join_rhs/2@85c1f447 with 10018 rows and digest c6b9cc7fo7qjdnvnplbvekecf45.
[2025-06-03 18:07:11] (1s)  >>> Created relation bind/2@d1aa8dkm with 10045 rows and digest 8c015eueokan4gs4akehq5v6oh9.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate bind_10#join_rhs/2@82b421is
[2025-06-03 18:07:11] (1s)  >>> Created relation bind_10#join_rhs/2@82b421is with 10045 rows and digest 58ed62eroei2u5if28honnn3lu2.
[2025-06-03 18:07:11] (1s)  >>> Created relation is_arguments_object/1@6b5f787i with 3885 rows and digest ff63412d41oef9e6cgb5s8ctq35.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Variables::GlobalVarAccess#d9e3aaf5/1@7820c9rd
[2025-06-03 18:07:11] (1s)  >>> Created relation Variables::GlobalVarAccess#d9e3aaf5/1@7820c9rd with 6330 rows and digest 4bcb415ggfug1arsua5fbcmdm7c.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::UndefinedConstant#782564e3/1@614ed22h
[2025-06-03 18:07:11] (1s)  >>> Created relation Constants::SyntacticConstants::UndefinedConstant#782564e3/1@614ed22h with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:11] (1s) Inferred that cached_Constants::SyntacticConstants::UndefinedConstant#782564e3/1@d6f2510t is empty, due to Constants::SyntacticConstants::UndefinedConstant#782564e3/1@614ed22h.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::InfinityConstant#613f811d/1@074935lm
[2025-06-03 18:07:11] (1s)  >>> Created relation Constants::SyntacticConstants::InfinityConstant#613f811d/1@074935lm with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:11] (1s) Inferred that cached_Constants::SyntacticConstants::InfinityConstant#613f811d/1@8a3941qn is empty, due to Constants::SyntacticConstants::InfinityConstant#613f811d/1@074935lm.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::NaNConstant#91a69921/1@0cbe5418
[2025-06-03 18:07:11] (1s)  >>> Created relation Constants::SyntacticConstants::NaNConstant#91a69921/1@0cbe5418 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:11] (1s) Inferred that cached_Constants::SyntacticConstants::NaNConstant#91a69921/1@ffe46apv is empty, due to Constants::SyntacticConstants::NaNConstant#91a69921/1@0cbe5418.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::UnaryConstant#7f985313/1@i1#874180wp (iteration 1)
[2025-06-03 18:07:11] (1s) Empty delta for Constants::SyntacticConstants::UnaryConstant#7f985313_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@i1#87418znp (iteration 1)
[2025-06-03 18:07:11] (1s) Empty delta for Constants::SyntacticConstants::WrappedConstant#38aa55b5_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::SyntacticConstant#a9c29f7a/1@i1#87418ynp (iteration 1)
[2025-06-03 18:07:11] (1s) 			 - Constants::SyntacticConstants::SyntacticConstant#a9c29f7a_delta has 195 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@i1#87418xnp (iteration 1)
[2025-06-03 18:07:11] (1s) Empty delta for Constants::SyntacticConstants::BinaryConstant#8ea6dda6_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@i1#87418wnp (iteration 1)
[2025-06-03 18:07:11] (1s) Empty delta for Constants::SyntacticConstants::ConditionalConstant#822d95c8_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate _Constants::SyntacticConstants::SyntacticConstant#a9c29f7a#prev_delta_exprs_032#join_rhs#shared/1@i2#L0#87418wnp (iteration 2)
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::UnaryConstant#7f985313/1@i2#874180wp (iteration 2)
[2025-06-03 18:07:11] (1s) Empty delta for Constants::SyntacticConstants::UnaryConstant#7f985313_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@i2#87418znp (iteration 2)
[2025-06-03 18:07:11] (1s) 			 - Constants::SyntacticConstants::WrappedConstant#38aa55b5_delta has 335 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@i2#87418xnp (iteration 2)
[2025-06-03 18:07:11] (1s) Empty delta for Constants::SyntacticConstants::BinaryConstant#8ea6dda6_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@i2#87418wnp (iteration 2)
[2025-06-03 18:07:11] (1s) Empty delta for Constants::SyntacticConstants::ConditionalConstant#822d95c8_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::SyntacticConstant#a9c29f7a/1@i3#87418ynp (iteration 3)
[2025-06-03 18:07:11] (1s) 			 - Constants::SyntacticConstants::SyntacticConstant#a9c29f7a_delta has 140 rows (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate _Constants::SyntacticConstants::SyntacticConstant#a9c29f7a#prev_delta_exprs_032#join_rhs#shared/1@i4#L0#87418wnp (iteration 4)
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::UnaryConstant#7f985313/1@i4#874180wp (iteration 4)
[2025-06-03 18:07:11] (1s) Empty delta for Constants::SyntacticConstants::UnaryConstant#7f985313_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@i4#87418znp (iteration 4)
[2025-06-03 18:07:11] (1s) Empty delta for Constants::SyntacticConstants::WrappedConstant#38aa55b5_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@i4#87418xnp (iteration 4)
[2025-06-03 18:07:11] (1s) Empty delta for Constants::SyntacticConstants::BinaryConstant#8ea6dda6_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@i4#87418wnp (iteration 4)
[2025-06-03 18:07:11] (1s) Empty delta for Constants::SyntacticConstants::ConditionalConstant#822d95c8_delta (order for disjuncts: delta=<standard>).
[2025-06-03 18:07:11] (1s) Accumulating deltas
[2025-06-03 18:07:11] (1s)  >>> Created relation Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@87418wnp with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:11] (1s)  >>> Created relation Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@87418xnp with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 18:07:11] (1s)  >>> Created relation Constants::SyntacticConstants::SyntacticConstant#a9c29f7a/1@87418ynp with 335 rows and digest 8e09809oiios61nph1l4jnmkv76.
[2025-06-03 18:07:11] (1s)  >>> Created relation Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@87418znp with 335 rows and digest 8e09809oiios61nph1l4jnmkv76.
[2025-06-03 18:07:11] (1s)  >>> Discarded freshly computed Constants::SyntacticConstants::UnaryConstant#7f985313/1@874180wp, preempted by a cache hit with digest THIS-RELATION-HAS-NO-TUPLES
[2025-06-03 18:07:11] (1s) Inferred that cached_Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@214e74k7 is empty, due to Constants::SyntacticConstants::ConditionalConstant#822d95c8/1@87418wnp.
[2025-06-03 18:07:11] (1s) Inferred that cached_Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@6a405eg0 is empty, due to Constants::SyntacticConstants::BinaryConstant#8ea6dda6/1@87418xnp.
[2025-06-03 18:07:11] (1s) Starting to evaluate predicate Constants::ConstantExpr#16c7d9a2/1@ae9906rq
[2025-06-03 18:07:11] (1s)  >>> Created relation Constants::ConstantExpr#16c7d9a2/1@ae9906rq with 335 rows and digest 8e09809oiios61nph1l4jnmkv76.
[2025-06-03 18:07:11] (1s) No need to promote strings for predicate Constants::ConstantExpr#16c7d9a2  as it does not contain computed strings.
[2025-06-03 18:07:11] (1s)  >>> Created relation cached_Constants::ConstantExpr#16c7d9a2/1@ed3d38s0 with 335 rows and digest 8e09809oiios61nph1l4jnmkv76.
[2025-06-03 18:07:11] (1s)  >>> Created relation cached_Constants::SyntacticConstants::SyntacticConstant#a9c29f7a/1@2ba6ad9n with 335 rows and digest 8e09809oiios61nph1l4jnmkv76.
[2025-06-03 18:07:11] (1s)  >>> Created relation cached_Constants::SyntacticConstants::WrappedConstant#38aa55b5/1@41f1f0eb with 335 rows and digest 8e09809oiios61nph1l4jnmkv76.
[2025-06-03 18:07:11] (1s) Query done
[2025-06-03 18:07:11] (1s) Sequence stamp origin is -6042184797358189537
[2025-06-03 18:07:11] (1s) Pausing evaluation to sync to disk at sequence stamp o+0
[2025-06-03 18:07:11] (1s) Will trim disk cache to check for obsolete items before writing 11.62MiB (currently using 0.00B).
[2025-06-03 18:07:11] (1s) After trimming, disk cache uses 0.00B.
[2025-06-03 18:07:11] (1s) Unpausing evaluation
[2025-06-03 18:07:11] Evaluation of E:\advance_javascript\codeQL\7\multi_purpose_project\queries\hardcoded-credentials.ql produced BQRS results.
[2025-06-03 18:07:11] [PROGRESS] execute queries> [1/1 eval 1.3s] Evaluation done; writing results to nodejs-security-queries\hardcoded-credentials.bqrs.
[2025-06-03 18:07:11] [PROGRESS] execute queries> Shutting down query evaluator.
[2025-06-03 18:07:11] Pausing evaluation to close the cache at sequence stamp o+1
[2025-06-03 18:07:11] Doing closing disk-cache trim now.
[2025-06-03 18:07:11] After trimming, disk cache uses 2.02MiB.
[2025-06-03 18:07:11] Unpausing evaluation
[2025-06-03 18:07:11] Exiting with code 0
