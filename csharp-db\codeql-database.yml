---
sourceLocationPrefix: E:\advance_javascript\codeQL\7
baselineLinesOfCode: 75
unicodeNewlines: false
columnKind: utf16
primaryLanguage: csharp
inProgress:
  primaryLanguage: csharp
  installedExtractors:
    actions:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/actions
    cpp:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/cpp
    csharp:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/csharp
    csv:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/csv
    go:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/go
    html:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/html
    java:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/java
    javascript:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/javascript
    properties:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/properties
    python:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/python
    ruby:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/ruby
    swift:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/swift
    xml:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/xml
    yaml:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/yaml
creationMetadata:
  cliVersion: 2.21.3
  creationTime: 2025-06-03T06:08:57.019265800Z
finalised: false
