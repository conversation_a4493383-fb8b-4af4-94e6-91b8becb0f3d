/**
 * @name SQL injection in Node.js
 * @description Detects potential SQL injection vulnerabilities in JavaScript/Node.js code
 * @kind problem
 * @problem.severity error
 * @id js/sql-injection
 */

import javascript

from StringLiteral sql
where
  (
    sql.getValue().toLowerCase().matches("%select%") or
    sql.getValue().toLowerCase().matches("%insert%") or
    sql.getValue().toLowerCase().matches("%update%") or
    sql.getValue().toLowerCase().matches("%delete%") or
    sql.getValue().toLowerCase().matches("%where%")
  ) and
  sql.getParent() instanceof AddExpr
select sql, "Potential SQL injection vulnerability: SQL query '" + sql.getValue() + "' uses string concatenation."
