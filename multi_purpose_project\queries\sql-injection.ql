/**
 * @name SQL injection in Node.js
 * @description Detects potential SQL injection vulnerabilities in JavaScript/Node.js code
 * @kind problem
 * @problem.severity error
 * @id js/sql-injection
 */

import javascript

from AddExpr concat, StringLiteral sqlPart
where
  concat.getAnOperand() = sqlPart and
  (
    sqlPart.getValue().toLowerCase().matches("%select%") or
    sqlPart.getValue().toLowerCase().matches("%insert%") or
    sqlPart.getValue().toLowerCase().matches("%update%") or
    sqlPart.getValue().toLowerCase().matches("%delete%") or
    sqlPart.getValue().toLowerCase().matches("%where%")
  )
select concat, "Potential SQL injection vulnerability: SQL query '" + sqlPart.getValue() + "' uses string concatenation."
