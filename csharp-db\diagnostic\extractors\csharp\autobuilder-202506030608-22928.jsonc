{"timestamp":"2025-06-03T06:09:03.0933754Z","source":{"id":"csharp/autobuilder/dotnet-incompatible-projects","name":"Some projects are incompatible with .NET Core","extractorName":"csharp"},"markdownMessage":"CodeQL found some projects which cannot be built with .NET Core:\n\n- `SecurityVulnerabilities.csproj`","helpLinks":[],"severity":"warning","internal":false,"visibility":{"statusPage":true,"cliSummaryTable":true,"telemetry":true},"attributes":{}}{"timestamp":"2025-06-03T06:09:03.4242886Z","source":{"id":"csharp/autobuilder/msbuild-build-failure","name":"Some projects or solutions failed to build using MSBuild","extractorName":"csharp"},"markdownMessage":"CodeQL was unable to build the following projects using MSBuild:\n\n- `SecurityVulnerabilities.csproj`\n\nSet up a [manual build command](https://docs.github.com/en/code-security/code-scanning/automatically-scanning-your-code-for-vulnerabilities-and-errors/configuring-the-codeql-workflow-for-compiled-languages).","helpLinks":[],"severity":"error","internal":false,"visibility":{"statusPage":true,"cliSummaryTable":true,"telemetry":true},"attributes":{}}