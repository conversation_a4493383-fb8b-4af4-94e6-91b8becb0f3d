[2025-06-03 11:07:05] This is codeql execute queries -J-Xmx1374M --verbosity=progress --logdir=E:\advance_javascript\codeQL\7\python-db-updated\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\7\python-db-updated\results -- E:\advance_javascript\codeQL\7\python-db-updated\db-python queries/python-password-simple.ql
[2025-06-03 11:07:05] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --format=json -- queries/python-password-simple.ql
[2025-06-03 11:07:05] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 11:07:05] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\queries\\python-password-simple.ql"
                      ]
[2025-06-03 11:07:05] Refusing fancy output: The terminal is not an xterm: 
[2025-06-03 11:07:05] Creating executor with 1 threads.
[2025-06-03 11:07:05] Calling plumbing command: codeql resolve extensions --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations queries/python-password-simple.ql
[2025-06-03 11:07:05] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --allow-library-packs --format startingpacks -- queries/python-password-simple.ql
[2025-06-03 11:07:05] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 11:07:05] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7"
                      ]
[2025-06-03 11:07:05] Calling plumbing command: codeql resolve extensions-by-pack --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\7
[2025-06-03 11:07:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}
[2025-06-03 11:07:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] python-security-queries: 1.0.0 {python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 1] python-security-queries: 1.0.0
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/python-all: * [*] {python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/python-all: 4.0.7
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 11:07:07] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 11:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\ext\default-threat-models-fixup.model.yml.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 11:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Asyncpg.model.yml.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 5 tuples.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 6 tuples.
[2025-06-03 11:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Stdlib.model.yml.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 12 tuples.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 1 tuples.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 66 tuples.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 11:07:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\empty.model.yml.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 0 tuples.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 0 tuples.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 0 tuples.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 11:07:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 11:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\subclass-capture\ALL.model.yml.
[2025-06-03 11:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 58275 tuples.
[2025-06-03 11:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-03 11:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 11:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-03 11:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-03 11:07:08] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-03 11:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-03 11:07:08] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-03 11:07:08] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=7+1+2+1+2",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=20+4+1*2+2+1",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4+2+1+2+1*2+4+2",
                                "columnNumbers" : "A=9*12"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=29",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=37+1+2+4+2*2+4+2*3+1+2+1+2+1+2+4+2+4+2*2+3+2*2+3+1+2*4+4+1+4+1+4+1*5+2*4+4+1+2*11+3+2+3+4+1+2*2+1+2",
                                "columnNumbers" : "A=9*66"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+3*58274",
                                "columnNumbers" : "A=5*58275"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 11:07:08] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=7+1+2+1+2",
                                "columnNumbers" : "A=9*5"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=20+4+1*2+2+1",
                                "columnNumbers" : "A=9*6"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=6+1*4+2+1+2+1*2+4+2",
                                "columnNumbers" : "A=9*12"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=29",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A=37+1+2+4+2*2+4+2*3+1+2+1+2+1+2+4+2+4+2*2+3+2*2+3+1+2*4+4+1+4+1+4+1*5+2*4+4+1+2*11+3+2+3+4+1+2*2+1+2",
                                "columnNumbers" : "A=9*66"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=7+3*58274",
                                "columnNumbers" : "A=5*58275"
                              }
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=6",
                                "columnNumbers" : "A=9"
                              }
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A=8+3+1+3+1*5+3+1+5+1*3",
                                "columnNumbers" : "A=9*15"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false,
                              "locations" : {
                                "lineNumbers" : "A",
                                "columnNumbers" : "A"
                              }
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 11:07:09] Calling plumbing command: codeql resolve library-path --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --query=E:\advance_javascript\codeQL\7\queries\python-password-simple.ql --format=json
[2025-06-03 11:07:09] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\7\queries\python-password-simple.ql.
[2025-06-03 11:07:09] [DETAILS] resolve library-path> Found enclosing pack 'python-security-queries' at E:\advance_javascript\codeQL\7.
[2025-06-03 11:07:09] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-03 11:07:09] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\7\qlpack.yml.
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] python-security-queries: 1.0.0 {python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 1] python-security-queries: 1.0.0
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] codeql/python-all: * [*] {python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 2] codeql/python-all: 4.0.7
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] codeql/yaml: * [*] {codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] codeql/xml: * [*] {codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] codeql/util: * [*] {codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] codeql/tutorial: * [*] {codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] codeql/threat-models: * [*] {codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] codeql/regex: * [*] {codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] codeql/mad: * [*] {codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] codeql/dataflow: * [*] {codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] codeql/typetracking: * [*] {codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DERIVATION] codeql/ssa: * [*] {codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 11:07:09] [SPAMMY] resolve library-path> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 11:07:09] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\7 resolved OK.
[2025-06-03 11:07:09] [DETAILS] resolve library-path> Found dbscheme through QL packs: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmlecode.python.dbscheme.
[2025-06-03 11:07:09] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\dataflow\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\mad\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\regex\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\ssa\\1.1.2",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\tutorial\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\typetracking\\2.0.7",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\xml\\1.0.23",
                          "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\yaml\\1.0.23"
                        ],
                        "dbscheme" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmlecode.python.dbscheme",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "python-security-queries\\queries\\python-password-simple.ql",
                        "qlPackName" : "python-security-queries"
                      }
[2025-06-03 11:07:09] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\7\queries\python-password-simple.ql.
[2025-06-03 11:07:09] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\7\queries\python-password-simple.ql.
[2025-06-03 11:07:11] Resolved file set for E:\advance_javascript\codeQL\7\queries\python-password-simple.ql hashes to 37d7668d92cc4d982600701da3a74629.
[2025-06-03 11:07:11] [DETAILS] execute queries> Checking QL for E:\advance_javascript\codeQL\7\queries\python-password-simple.ql.
[2025-06-03 11:07:12] Stale frontend caches are invalidated based on import graph reachability.
[2025-06-03 11:07:12] ExternalModuleBindingPass ...
[2025-06-03 11:07:13] ExternalModuleBindingPass time: 00:00.931
[2025-06-03 11:07:13] CollectInstantiationsPass ...
[2025-06-03 11:07:13] CollectInstantiationsPass time: 00:00.290
[2025-06-03 11:07:13] Ql checks ...
[2025-06-03 11:07:17] Ql checks time: 00:03.912
[2025-06-03 11:07:23] Compilation pipeline
[2025-06-03 11:07:23] Type Inference ...
[2025-06-03 11:07:23] CSV_TYPE_HIERARCHY: Best number of iterations,Maximum number of runs,Number of BDD variables,Size of BDD for typefacts
[2025-06-03 11:07:23] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:23] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:07:23] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:07:23] CSV_TYPE_HIERARCHY: 0,0,96,97
[2025-06-03 11:07:23] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:23] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,326,384
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,0,0
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,24,24
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,48,72
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,5,5
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,7,7
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,561,15752
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,12,13
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,8,10
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,7,7
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,6,6
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,5,5
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,10,11
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,10,10
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,13,17
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,8,10
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,52,62
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,18,18
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,8,8
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,4,4
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,9,9
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,6,7
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,41,52
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,2,2
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,1,1
[2025-06-03 11:07:24] CSV_TYPE_HIERARCHY: 0,0,3,3
[2025-06-03 11:07:25] Type Inference time: 00:01.478
[2025-06-03 11:07:25] [DETAILS] execute queries> Optimizing E:\advance_javascript\codeQL\7\queries\python-password-simple.ql.
[2025-06-03 11:07:25] Processing compilation pipeline
[2025-06-03 11:07:25] Started compiling a query
[2025-06-03 11:07:25] Initialising compiler...
[2025-06-03 11:07:25] 	 ... compiler initialised
[2025-06-03 11:07:25] About to start query optimisation
[2025-06-03 11:07:25] Compilation cache hit - skipping compilation.
[2025-06-03 11:07:26] Compilation cache hit - skipping compilation.
[2025-06-03 11:07:26] Compilation cache miss for d44d16935bfd9a354256d23555b2246c.
[2025-06-03 11:07:27] Stored compiled program for d44d16935bfd9a354256d23555b2246c.
[2025-06-03 11:07:27] CSV_COMPILATION: NONE,MISC,RA_TRANSLATION,OPTIMISATIONS
[2025-06-03 11:07:27] CSV_COMPILATION: 172,311,255,1058
[2025-06-03 11:07:27] [SPAMMY] execute queries> No database upgrade/downgrade needed for E:\advance_javascript\codeQL\7\queries\python-password-simple.ql
[2025-06-03 11:07:27] [PROGRESS] execute queries> [1/1 comp 18.2s] Compiled E:\advance_javascript\codeQL\7\queries\python-password-simple.ql.
[2025-06-03 11:07:27] [PROGRESS] execute queries> Starting evaluation of python-security-queries\queries\python-password-simple.ql.
[2025-06-03 11:07:27] Starting evaluation of E:\advance_javascript\codeQL\7\queries\python-password-simple.ql
[2025-06-03 11:07:27] (0s) Start query execution
[2025-06-03 11:07:27] (0s) Beginning execution of E:\advance_javascript\codeQL\7\queries\python-password-simple.ql
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_AstExtended::AstNode.getLocation/0#dispred#6b4dcb62/2@389a43u0 with 129 rows and digest 230d52r3fgbfc6mbinshdjrdvee.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_Files::Impl::Container.splitAbsolutePath/2#dispred#324a3985/3@c992f399 with 35 rows and digest 1cb23dnt3nmevu3157k9j89fo26.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_Essa::TEssaDefinition#4c062f56/1@918595pb with 41 rows and digest 9a96c1ct2q0kg6j9r9c4su7r5if.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_Essa::TEssaEdgeDefinition#be4738bc/4@2da913f5 with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaCompute::EssaDefinitions::piNode/3#b1917f93/3@3204beti with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaCompute::AdjacentUses::adjacentUseUse/2#c094915e/2@1fd2fe9p with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaCompute::AdjacentUses::adjacentUseUseSameVar/2#2eafd7db/2@0dcc16hn with empty relation.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::adjacentVarRefs/5#de1f7cd5/5@4359219o with 30 rows and digest 6855a6siea2560of9nrlnj7jh35.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::defSourceUseRank/4#ebad7bcb/4@99c70bvp with 66 rows and digest e018a42trdrh9roipdhbhi53cec.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::variableDefine/4#79261f91/4@1e9ed9n1 with 55 rows and digest f0af40jc4tshc9ft2i0lclbnc9e.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_Flow::BasicBlock.strictlyDominates/1#dispred#65ae2ca3/2@40c1c68m with empty relation.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_CachedStages::Stages::AST::ref/0#e52d92b5/0@c876115v with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_CachedStages::Stages::AST::backref/0#dddb3000/0@a80d27a0 with 1 rows and digest ac90f0ei322hhreshlvdl215c38.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaDefinitions::SsaSource::method_call_refinement/3#3634d2a9/3@ec7d91se with 2 rows and digest ad8cbfggshaf5jrsdmflrimdjmc.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaDefinitions::SsaSource::argument_refinement/3#615fb3a9/3@2d844778 with 1 rows and digest d22634250nvm4lnndccm7b5ni39.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaDefinitions::SsaSource::assignment_definition/3#a5ba4d99/3@5c11868o with 23 rows and digest f0d941po4rfm4pghgansasjk06a.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_Flow::DefinitionNode#e8809c3b/1@a95b912i with 23 rows and digest 5f6dcbtbrphb5cuovedd0066220.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_AstExtended::AstNode.getAFlowNode/0#dispred#fcebb9ee/2@89ae53vo with 109 rows and digest 260af6fcpll0em4qao052h8j0q4.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::parameter_definition/2#40b2fede/2@9409b5ba with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::attribute_assignment_refinement/3#5f777592/3@f4c9688s with empty relation.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_Flow::ControlFlowNode.getScope/0#dispred#b061daac/2@afe6a2k8 with 109 rows and digest 24903dlh7adrl0pf81ntttrn0rd.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_AstExtended::AstNode.getAChildNode/0#dispred#a130356d/2@4a4f6brg with 127 rows and digest fced70t0gh1cundc5s9va72gab4.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_Flow::BasicBlock.getASuccessor/0#dispred#7249dd96/2@debdc7ao with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::attribute_deletion_refinement/3#7de1bd7a/3@bc7edf3e with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::deletion_definition/2#e74a17e3/2@a934fde8 with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::exception_capture/2#fd0779df/2@793700dj with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::exception_group_capture/2#d53f492d/2@790fdeja with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::import_star_refinement/3#1675f8e2/3@39fa47h0 with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::init_module_submodule_defn/2#64b1af7d/2@2761bd9c with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_Module::moduleNameFromFile/1#a01d5f51/2@010e4f4r with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::multi_assignment_definition/4#1d17889e/4@89e653d3 with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_Flow::SequenceNode.getElement/1#dispred#4cc4068f/3@cc5e78ka with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::pattern_alias_definition/2#e55586af/2@f91af3ha with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::pattern_capture_definition/2#77b3ae42/2@6e282c28 with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::test_refinement/3#2cbdf557/3@c646bdum with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaDefinitions::SsaSource::with_definition/2#e1888ceb/2@e0bd2902 with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaCompute::SsaDefinitions::reachesEndOfBlock/4#214bd902/4@8d3536mv with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaCompute::Liveness::liveAtExit/2#b6aa63f4/2@00f3ba04 with empty relation.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::Liveness::liveAtEntry/2#bab3ea7c/2@6601ffvo with 5 rows and digest fd3201pqqeb2dloh2qrkt53s6b4.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::defUseRank/4#782a2f48/4@a2d696uu with 130 rows and digest 25d73cg3gqaj39ogc9acq623hl8.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::variableDef/4#38b3ef7e/4@8a1c0f76 with 58 rows and digest 73b2be1t55j4ie8mesec8bbrqg7.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::variableRefine/4#b20a3c26/4@209e7767 with 3 rows and digest 6e2619ol47gc8sosqs0cvrtjhhb.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::variableUse/4#da62dc30/4@ce02f549 with 72 rows and digest e061381rd58uh7kttr3uorvp9nd.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::defRank/4#f608ea69/4@7f1611ue with 58 rows and digest 0850c7pms3v3ogckuivs8tu7hl6.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::lastRank/2#6cfcb19d/3@0891d874 with 37 rows and digest 52fe47of0m88acm1o64g7m4mre0.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_SsaCompute::EssaDefinitions::phiNode/2#4864b30d/2@ce8e8e2f with empty relation.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::ssaDef/2#1ca5a11b/2@ae9182bm with 32 rows and digest 061eb88coj3debb88unqmcia5k8.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::EssaDefinitions::variableUpdate/5#c42159b9/5@2ba91fut with 41 rows and digest 315cea3j4uq0oo059sc3cda5tca.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::EssaDefinitions::variableDefinition/5#32b5d84f/5@5f1279gb with 39 rows and digest b6eec00j5rmih9an5e7kei04o70.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::EssaDefinitions::variableRefinement/5#4e891107/5@95e977ab with 2 rows and digest ab4479eommd6ggr8umnpqk7ohj8.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::ssaDefReachesRank/4#f19c6fee/4@404efe5b with 106 rows and digest 36fdc7ftg1jgc1a4jo993gdbjs8.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_Flow::BasicBlock.getImmediateDominator/0#dispred#0f15e8ec/2@408ca88v with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_Essa::TPhiFunction#dfb6fe3b/3@2bce46ul with empty relation.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_Essa::PhiFunction.getInput/1#dispred#f797e1b9/3@8f4ad7c4 with empty relation.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_Essa::TEssaNodeDefinition#96e9ebfe/4@a811d0mp with 39 rows and digest 1b88abkdhpuc45rjnkltkh00oo7.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_Essa::TEssaNodeRefinement#8de2de42/4@3787696p with 2 rows and digest bade7a0ldoksn7dfcusa4udfc49.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_Exprs::Expr.toString/0#dispred#7435dfcd/2@a8c03872 with 86 rows and digest c43300spki6g1kqtglo00fbtgk2.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_Import::ImportStar.getModuleExpr/0#dispred#8ab9ce73/2@9212cahk with empty relation.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_AstExtended::AstNode.getParentNode/0#dispred#c159e3ee/2@699751d5 with 127 rows and digest 76d33di2qp08hg947gac2givg43.
[2025-06-03 11:07:27] (0s)  >>> First-level cache hit cached_#Flow::BasicBlock.getASuccessor/0#dispred#7249dd96Plus/2@4ae642b8 with empty relation.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_Flow::ControlFlowNode.toString/0#dispred#e1af144b/2@54b666h0 with 109 rows and digest 84c277p3ud6cqs7iqgcdhk4fo6d.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::variableSourceUse/4#4525ef19/4@271bba4s with 11 rows and digest 1d9d42qqmmmesn7cdld5hcnqfke.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::definesAt/4#68130204/4@388909lj with 39 rows and digest 1ed504sgaq4bc3nair6iq9qj0vd.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::firstUse/2#ca053ce0/2@b3437a3s with 7 rows and digest 50dba43m42lq9hbatojlhdsg7q8.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::AdjacentUses::useOfDef/2#16a7ee50/2@c0f71efh with 7 rows and digest 50dba43m42lq9hbatojlhdsg7q8.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaDefinitions::reachesExit/3#a534505a/3@cbea635k with 31 rows and digest cf8c04ifauet6eenn28k6tv0ka3.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaDefinitions::reachesUse/4#216dc4f6/4@2dbd699l with 64 rows and digest 386a7546i76tul016qdjaf4objc.
[2025-06-03 11:07:27] (0s)  >>> Full cache hit cached_SsaCompute::SsaComputeImpl::ssaDefReachesUseWithinBlock/4#87a91e15/4@f3e4d889 with 64 rows and digest 386a7546i76tul016qdjaf4objc.
[2025-06-03 11:07:27] (0s)  >>> Created relation py_exprs/4@62fb455g with 86 rows and digest 476bbb9bpd54bfhhl3b277dvi93.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate py_exprs_10#join_rhs/2@073ac12u
[2025-06-03 11:07:28] (0s)  >>> Created relation py_exprs_10#join_rhs/2@073ac12u with 86 rows and digest 5cb25bh65v882cbcgs7ktbjlra0.
[2025-06-03 11:07:28] (0s)  >>> Created relation py_strs/3@35cd6b34 with 83 rows and digest 473c56049jkfnie29f6s5t9remb.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate py_strs_120#join_rhs/3@bec877pd
[2025-06-03 11:07:28] (0s)  >>> Created relation py_strs_120#join_rhs/3@bec877pd with 83 rows and digest 5f8da0i5n99qiq8i5of4fpnl5t3.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Exprs::StringLiteral.getText/0#dispred#8c21c5a0/2@7c4c2dkg
[2025-06-03 11:07:28] (0s)  >>> Created relation Exprs::StringLiteral.getText/0#dispred#8c21c5a0/2@7c4c2dkg with 33 rows and digest c274ccm8l4b39fd39aumogujh41.
[2025-06-03 11:07:28] (0s)  >>> Created relation py_stmts/4@93720efi with 27 rows and digest ce9b86tatnb2dmhmci78er5qqf8.
[2025-06-03 11:07:28] (0s)  >>> Created relation py_expr_lists/3@1b5e391t with 27 rows and digest c28eb8cpfv32pdrjrv9jjkt6o1c.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate py_expr_lists_120#join_rhs/3@3304a2u1
[2025-06-03 11:07:28] (0s)  >>> Created relation py_expr_lists_120#join_rhs/3@3304a2u1 with 27 rows and digest 7d37e3skhhddakh4trmkoosn29a.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate py_exprs_203#join_rhs/3@0e8aa5mo
[2025-06-03 11:07:28] (0s)  >>> Created relation py_exprs_203#join_rhs/3@0e8aa5mo with 86 rows and digest add76dsd6fkuqjdv7esrobrejh8.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7#ffb/3@a4dc08h1
[2025-06-03 11:07:28] (0s)  >>> Created relation AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7#ffb/3@a4dc08h1 with 5 rows and digest 8c20941p5ee97qhgismerp23g96.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate _py_exprs_10#join_rhs#antijoin_rhs#10/1@8754f207
[2025-06-03 11:07:28] (0s)  >>> Created relation _py_exprs_10#join_rhs#antijoin_rhs#10/1@8754f207 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:07:28] (0s) Inferred that Flow::StarredNode.getValue/0#dispred#77106d16/2@ecb8631a is empty, due to _py_exprs_10#join_rhs#antijoin_rhs#10/1@8754f207.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate _AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7#ffb_py_exprs#antijoin_rhs/3@45e6d618
[2025-06-03 11:07:28] (0s)  >>> Created relation _AstGenerated::Call_.getPositionalArg/1#dispred#16c46df7#ffb_py_exprs#antijoin_rhs/3@45e6d618 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Exprs::Call.getArg/1#dispred#50c53eea#ffb/3@1505fcts
[2025-06-03 11:07:28] (0s)  >>> Created relation Exprs::Call.getArg/1#dispred#50c53eea#ffb/3@1505fcts with 5 rows and digest 8c20941p5ee97qhgismerp23g96.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Exprs::Call.getArg/1#dispred#50c53eea#ffb_120#join_rhs/3@3628ddph
[2025-06-03 11:07:28] (0s)  >>> Created relation Exprs::Call.getArg/1#dispred#50c53eea#ffb_120#join_rhs/3@3628ddph with 5 rows and digest ef71fcea1d792bq1k3aila2b18a.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Function::FunctionExpr.getADecoratorCall/0#062a6e2a/2@i1#0345bbbi (iteration 1)
[2025-06-03 11:07:28] (0s) Empty delta for Function::FunctionExpr.getADecoratorCall/0#062a6e2a_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:07:28] (0s) Accumulating deltas
[2025-06-03 11:07:28] (0s)  >>> Created relation Function::FunctionExpr.getADecoratorCall/0#062a6e2a/2@0345bbbi with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:07:28] (0s) Inferred that project#Function::FunctionExpr.getADecoratorCall/0#062a6e2a/1@d0a3dcds is empty, due to Function::FunctionExpr.getADecoratorCall/0#062a6e2a/2@0345bbbi.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Class::ClassExpr.getADecoratorCall/0#54df5696/2@i1#a74da0oo (iteration 1)
[2025-06-03 11:07:28] (0s) Empty delta for Class::ClassExpr.getADecoratorCall/0#54df5696_delta (order for disjuncts: delta=<standard>).
[2025-06-03 11:07:28] (0s) Accumulating deltas
[2025-06-03 11:07:28] (0s)  >>> Created relation Class::ClassExpr.getADecoratorCall/0#54df5696/2@a74da0oo with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:07:28] (0s) Inferred that project#Class::ClassExpr.getADecoratorCall/0#54df5696/1@a676207g is empty, due to Class::ClassExpr.getADecoratorCall/0#54df5696/2@a74da0oo.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate m#AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb/1@401b0cbv
[2025-06-03 11:07:28] (0s)  >>> Created relation m#AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb/1@401b0cbv with 36 rows and digest f419fcln5ls5pmh02lmdqsglhv3.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate py_exprs_032#join_rhs/3@dc21c4iv
[2025-06-03 11:07:28] (0s)  >>> Created relation py_exprs_032#join_rhs/3@dc21c4iv with 86 rows and digest be84dc6fojrfanlpc0s7kg5t146.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb/2@07bf1fqf
[2025-06-03 11:07:28] (0s)  >>> Created relation AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb/2@07bf1fqf with 15 rows and digest 2c34b4ds38arvh8rtlaktlm1ifc.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb_10#join_rhs/2@507d7798
[2025-06-03 11:07:28] (0s)  >>> Created relation AstGenerated::Assign_.getValue/0#dispred#53d00b56#fb_10#join_rhs/2@507d7798 with 15 rows and digest f1086cgntibv1e5hsri52799ird.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate py_stmts_10#join_rhs/2@97ec97cm
[2025-06-03 11:07:28] (0s)  >>> Created relation py_stmts_10#join_rhs/2@97ec97cm with 27 rows and digest acaec0mjjbssf7gvsdkslhopcv1.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate AstGenerated::Assign_.getTarget/1#dispred#6ad44d47/3@b7f147lv
[2025-06-03 11:07:28] (0s)  >>> Created relation AstGenerated::Assign_.getTarget/1#dispred#6ad44d47/3@b7f147lv with 21 rows and digest 3afd64ju6tlmhqf0ncantatbv3a.
[2025-06-03 11:07:28] (0s)  >>> Created relation variable/3@5e1d40kq with 33 rows and digest 6a3b4b14bgnblis0klnmukgbrr8.
[2025-06-03 11:07:28] (0s)  >>> Created relation py_variables/2@62cd450b with 34 rows and digest 05c4e5cs4vkc2t3m8v34ndch22c.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Exprs::Name.getId/0#dispred#4fa5460e/2@1c667ebq
[2025-06-03 11:07:28] (0s)  >>> Created relation Exprs::Name.getId/0#dispred#4fa5460e/2@1c667ebq with 34 rows and digest 52e800lsal1tbvs2vop4h4jq09f.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#4/1@4df2e509
[2025-06-03 11:07:28] (0s)  >>> Created relation _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#4/1@4df2e509 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#3/1@1bdcb426
[2025-06-03 11:07:28] (0s)  >>> Created relation _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#3/1@1bdcb426 with 4 rows and digest 5e46559b3d087us5u4m77uihro3.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#2/1@f8881bq2
[2025-06-03 11:07:28] (0s)  >>> Created relation _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#2/1@f8881bq2 with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#1/1@e774e2qs
[2025-06-03 11:07:28] (0s)  >>> Created relation _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs#1/1@e774e2qs with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs/1@e0deb0u7
[2025-06-03 11:07:28] (0s)  >>> Created relation _Exprs::StringLiteral.getText/0#dispred#8c21c5a0#antijoin_rhs/1@e0deb0u7 with 2 rows and digest 59952f356q0jv5fodshnar55a9e.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate #select/2@0558cd7m
[2025-06-03 11:07:28] (0s)  >>> Created relation #select/2@0558cd7m with 10 rows and digest 6768e28ddp0vmocnjd5tpilele3.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Function::FunctionDef#ffa22159/2@fc4e36sm
[2025-06-03 11:07:28] (0s)  >>> Created relation Function::FunctionDef#ffa22159/2@fc4e36sm with 3 rows and digest ee4888dp68puidtt9rgbbvl534d.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate project#Function::FunctionDef#ffa22159/1@0c0f5b8n
[2025-06-03 11:07:28] (0s)  >>> Created relation project#Function::FunctionDef#ffa22159/1@0c0f5b8n with 3 rows and digest d50492310js1lnqpg0ef31aqqta.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Class::ClassDef#83097204/1@4d2446ee
[2025-06-03 11:07:28] (0s)  >>> Created relation Class::ClassDef#83097204/1@4d2446ee with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Stmts::AssignStmt#45f46a75/1@8cc50es5
[2025-06-03 11:07:28] (0s)  >>> Created relation Stmts::AssignStmt#45f46a75/1@8cc50es5 with 18 rows and digest 78df83bjfr2c5p1t45jbfg4he37.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate AstGenerated::Assign__not_Stmts::AssignStmt_Class::ClassDef_Function::FunctionDef#9571075b/1@670cb7di
[2025-06-03 11:07:28] (0s)  >>> Created relation AstGenerated::Assign__not_Stmts::AssignStmt_Class::ClassDef_Function::FunctionDef#9571075b/1@670cb7di with 0 rows and digest THIS-RELATION-HAS-NO-TUPLES.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate AstGenerated::Assign_.toString/0#dispred#782906e5/2@926fbbjs
[2025-06-03 11:07:28] (0s)  >>> Created relation AstGenerated::Assign_.toString/0#dispred#782906e5/2@926fbbjs with 21 rows and digest b2057fqp4st16cdai9ii06hkgif.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate project#AstGenerated::Assign_.toString/0#dispred#782906e5/1@98168d1j
[2025-06-03 11:07:28] (0s)  >>> Created relation project#AstGenerated::Assign_.toString/0#dispred#782906e5/1@98168d1j with 21 rows and digest febace78imdtv5m9a7plcm5lh29.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate project##select/1@27eb9ear
[2025-06-03 11:07:28] (0s)  >>> Created relation project##select/1@27eb9ear with 10 rows and digest d9704df7vc2968k5ps1udna0qtb.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/2@c18d8553
[2025-06-03 11:07:28] (0s)  >>> Created relation AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/2@c18d8553 with 10 rows and digest f1f33d2g5b0195fqg3dtgcu4rc9.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate project#AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/1@590b79m2
[2025-06-03 11:07:28] (0s)  >>> Created relation project#AstGenerated::AstNode_.toString/0#dispred#192f27b5#bf/1@590b79m2 with 10 rows and digest d9704df7vc2968k5ps1udna0qtb.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate _#select_project#AstGenerated::Assign_.toString/0#dispred#782906e5_project#AstGenerated::AstNode_.to__#antijoin_rhs/2@6c41a5uo
[2025-06-03 11:07:28] (0s)  >>> Created relation _#select_project#AstGenerated::Assign_.toString/0#dispred#782906e5_project#AstGenerated::AstNode_.to__#antijoin_rhs/2@6c41a5uo with 10 rows and digest 6768e28ddp0vmocnjd5tpilele3.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate _#select_AstGenerated::Assign_.toString/0#dispred#782906e5_AstGenerated::AstNode_.toString/0#dispred__#shared/3@7d6debeu
[2025-06-03 11:07:28] (0s)  >>> Created relation _#select_AstGenerated::Assign_.toString/0#dispred#782906e5_AstGenerated::AstNode_.toString/0#dispred__#shared/3@7d6debeu with 10 rows and digest f5136cvq0v3hldldtd5icsr4fm3.
[2025-06-03 11:07:28] (0s)  >>> Created relation folders/2@bad7d9u0 with 7 rows and digest 504939bjsummg163iumr35cn6fc.
[2025-06-03 11:07:28] (0s)  >>> Created relation files/2@ec93749g with 7 rows and digest 25e58crktdgaufn1jel1vuc5rq9.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Files::Input::ContainerBase.getAbsolutePath/0#dispred#bb5aca3b/2@966d67vo
[2025-06-03 11:07:28] (0s)  >>> Created relation Files::Input::ContainerBase.getAbsolutePath/0#dispred#bb5aca3b/2@966d67vo with 14 rows and digest da9ba69sf3j2f354p2stvjdrj72.
[2025-06-03 11:07:28] (0s)  >>> Created relation py_module_path/2@7a0f7599 with 2 rows and digest 76820d8bcba222g98abm3vg7e07.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Module::Module.getFile/0#dispred#53eb9b1b/2@648ff7sb
[2025-06-03 11:07:28] (0s)  >>> Created relation Module::Module.getFile/0#dispred#53eb9b1b/2@648ff7sb with 2 rows and digest 76820d8bcba222g98abm3vg7e07.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Module::Module.getFile/0#dispred#53eb9b1b_0#antijoin_rhs/1@b54f01qv
[2025-06-03 11:07:28] (0s)  >>> Created relation Module::Module.getFile/0#dispred#53eb9b1b_0#antijoin_rhs/1@b54f01qv with 2 rows and digest 1758e8s2mdeq4m767kljah7j4r6.
[2025-06-03 11:07:28] (0s)  >>> Created relation locations_default/6@2b5cc3pr with 574 rows and digest e548d7crqpjq6b0dj0bibvtc344.
[2025-06-03 11:07:28] (0s)  >>> Created relation locations_ast/6@aba204da with 140 rows and digest acf902vjm0jpiucb5cthhe02kqa.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate locations_ast_102345#join_rhs/6@ba5e2d5g
[2025-06-03 11:07:28] (0s)  >>> Created relation locations_ast_102345#join_rhs/6@ba5e2d5g with 140 rows and digest 117827sltp60vgg3jp4m5mfbh1b.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Files::Location.hasLocationInfo/5#dispred#143bb906/6@5c85b9n0
[2025-06-03 11:07:28] (0s)  >>> Created relation Files::Location.hasLocationInfo/5#dispred#143bb906/6@5c85b9n0 with 714 rows and digest 563123iq49t3qqslv960gl3vkbc.
[2025-06-03 11:07:28] (0s)  >>> Created relation py_locations/2@d222f4l2 with 124 rows and digest 1708daoeqjn73e3dd8krpu4got4.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate py_locations_10#join_rhs/2@cad24bnc
[2025-06-03 11:07:28] (0s)  >>> Created relation py_locations_10#join_rhs/2@cad24bnc with 124 rows and digest 56b5b7iks3ev6iouv9k9saassfb.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate Stmts::Stmt.getLocation/0#dispred#18f9d034/2@d3cb24lu
[2025-06-03 11:07:28] (0s)  >>> Created relation Stmts::Stmt.getLocation/0#dispred#18f9d034/2@d3cb24lu with 27 rows and digest 4866fbvmp483jnqe0l669693ql4.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62__#select_AstGenerated::Assign_.toString/0#dispr__#shared/4@eeb01fo5
[2025-06-03 11:07:28] (0s)  >>> Created relation _AstExtended::AstNode.getLocation/0#dispred#6b4dcb62__#select_AstGenerated::Assign_.toString/0#dispr__#shared/4@eeb01fo5 with 10 rows and digest 237415pvuk5gad9g230moctt4md.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate _AstGenerated::Stmt_.getLocation/0#dispred#de1f1018__#select_AstGenerated::Assign_.toString/0#dispre__#shared/4@2c1785ev
[2025-06-03 11:07:28] (0s)  >>> Created relation _AstGenerated::Stmt_.getLocation/0#dispred#de1f1018__#select_AstGenerated::Assign_.toString/0#dispre__#shared/4@2c1785ev with 10 rows and digest 237415pvuk5gad9g230moctt4md.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate project#Files::Location.hasLocationInfo/5#dispred#143bb906/1@2da765ju
[2025-06-03 11:07:28] (0s)  >>> Created relation project#Files::Location.hasLocationInfo/5#dispred#143bb906/1@2da765ju with 714 rows and digest c8832acv82q79lafn3sp7afjul4.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate __AstExtended::AstNode.getLocation/0#dispred#6b4dcb62__#select_AstGenerated::Assign_.toString/0#disp__#antijoin_rhs/3@5c710d0h
[2025-06-03 11:07:28] (0s)  >>> Created relation __AstExtended::AstNode.getLocation/0#dispred#6b4dcb62__#select_AstGenerated::Assign_.toString/0#disp__#antijoin_rhs/3@5c710d0h with 10 rows and digest f5136cvq0v3hldldtd5icsr4fm3.
[2025-06-03 11:07:28] (0s) Starting to evaluate predicate #select#query/8@7037827i
[2025-06-03 11:07:28] (0s)  >>> Created relation #select#query/8@7037827i with 10 rows and digest 1e49adnc8u24fbb6ab4hff4bcfb.
[2025-06-03 11:07:28] (0s) Query done
[2025-06-03 11:07:28] (0s) Sequence stamp origin is -6042293025182709956
[2025-06-03 11:07:28] (0s) Pausing evaluation to sync to disk at sequence stamp o+0
[2025-06-03 11:07:28] (0s) Unpausing evaluation
[2025-06-03 11:07:28] Evaluation of E:\advance_javascript\codeQL\7\queries\python-password-simple.ql produced BQRS results.
[2025-06-03 11:07:28] [PROGRESS] execute queries> [1/1 eval 240ms] Evaluation done; writing results to python-security-queries\queries\python-password-simple.bqrs.
[2025-06-03 11:07:28] [PROGRESS] execute queries> Shutting down query evaluator.
[2025-06-03 11:07:28] Pausing evaluation to close the cache at sequence stamp o+1
[2025-06-03 11:07:28] The disk cache is freshly trimmed; leave it be.
[2025-06-03 11:07:28] Unpausing evaluation
[2025-06-03 11:07:28] Exiting with code 0
