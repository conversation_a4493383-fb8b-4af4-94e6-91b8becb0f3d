---
sourceLocationPrefix: E:\advance_javascript\codeQL\7\multi_purpose_project
baselineLinesOfCode: 0
unicodeNewlines: true
columnKind: utf16
primaryLanguage: javascript
inProgress:
  primaryLanguage: javascript
  installedExtractors:
    actions:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/actions
    cpp:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/cpp
    csharp:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/csharp
    csv:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/csv
    go:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/go
    html:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/html
    java:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/java
    javascript:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/javascript
    properties:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/properties
    python:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/python
    ruby:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/ruby
    swift:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/swift
    xml:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/xml
    yaml:
     - C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/yaml
creationMetadata:
  cliVersion: 2.21.3
  creationTime: 2025-06-03T10:40:37.287374700Z
finalised: false
