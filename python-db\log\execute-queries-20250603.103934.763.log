[2025-06-03 10:39:34] This is codeql execute queries -J-Xmx1374M --verbosity=progress --logdir=E:\advance_javascript\codeQL\7\python-db\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\7\python-db\results -- E:\advance_javascript\codeQL\7\python-db\db-python queries/python-problm-1.ql
[2025-06-03 10:39:34] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --format=json -- queries/python-problm-1.ql
[2025-06-03 10:39:35] [PROGRESS] resolve queries> Recording pack reference (anonymous QL pack at E:\advance_javascript\codeQL\7\queries\python-problm-1.ql) at E:\advance_javascript\codeQL\7\queries.
[2025-06-03 10:39:35] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\queries\\python-problm-1.ql"
                      ]
[2025-06-03 10:39:35] Refusing fancy output: The terminal is not an xterm: 
[2025-06-03 10:39:35] Creating executor with 1 threads.
[2025-06-03 10:39:35] Calling plumbing command: codeql resolve extensions --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations queries/python-problm-1.ql
[2025-06-03 10:39:35] Calling plumbing command: codeql resolve queries --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --allow-library-packs --format startingpacks -- queries/python-problm-1.ql
[2025-06-03 10:39:35] [PROGRESS] resolve queries> Recording pack reference (anonymous QL pack at E:\advance_javascript\codeQL\7\queries\python-problm-1.ql) at E:\advance_javascript\codeQL\7\queries.
[2025-06-03 10:39:35] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\queries"
                      ]
[2025-06-03 10:39:35] Calling plumbing command: codeql resolve extensions-by-pack --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --include-extension-row-locations -- E:\advance_javascript\codeQL\7\queries
[2025-06-03 10:39:35] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] <anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}
[2025-06-03 10:39:35] [SPAMMY] resolve extensions-by-pack> [DERIVATION] <anonymous pack>: 0.0.0 {<anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}}
[2025-06-03 10:39:35] [SPAMMY] resolve extensions-by-pack> [DECISION 1] <anonymous pack>: 0.0.0
[2025-06-03 10:39:35] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : { },
                        "threatModels" : { },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 10:39:35] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : { },
                        "threatModels" : { },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 10:39:35] Calling plumbing command: codeql resolve library-path --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --query=E:\advance_javascript\codeQL\7\queries\python-problm-1.ql --format=json
[2025-06-03 10:39:35] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:39:35] [DETAILS] resolve library-path> Found no pack; trying after symlink resolution with E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:39:35] [DETAILS] resolve library-path> Found enclosing pack 'queries' at E:\advance_javascript\codeQL\7\queries.
[2025-06-03 10:39:35] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-03 10:39:35] [DETAILS] resolve library-path> Resolving library dependencies from E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:39:35] [SPAMMY] resolve library-path> [INCOMPATIBILITY] <anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}
[2025-06-03 10:39:35] [SPAMMY] resolve library-path> [DERIVATION] <anonymous pack>: 0.0.0 {<anonymous pack>: not 0.0.0 {root: <anonymous pack>@0.0.0}}
[2025-06-03 10:39:35] [SPAMMY] resolve library-path> [DECISION 1] <anonymous pack>: 0.0.0
[2025-06-03 10:39:35] [DETAILS] resolve library-path> QL pack dependencies for E:\advance_javascript\codeQL\7\queries resolved OK.
[2025-06-03 10:39:35] [DETAILS] resolve library-path> Found no dbscheme through dependencies.
[2025-06-03 10:39:35] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\7\\queries"
                        ],
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "queries\\python-problm-1.ql",
                        "possibleAdvice" : "There should probably be a qlpack.yml file declaring dependencies in E:\\advance_javascript\\codeQL\\7\\queries or an enclosing directory.",
                        "qlPackName" : "queries"
                      }
[2025-06-03 10:39:35] [PROGRESS] execute queries> Compiling query plan for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:39:35] [DETAILS] execute queries> Resolving imports for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:39:35] Resolved file set for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql hashes to 6b2223a219f0b20b3d1d16cd66d7dd35.
[2025-06-03 10:39:35] [DETAILS] execute queries> Checking QL for E:\advance_javascript\codeQL\7\queries\python-problm-1.ql.
[2025-06-03 10:39:36] Stale frontend caches are invalidated based on import graph reachability.
[2025-06-03 10:39:36] ExternalModuleBindingPass ...
[2025-06-03 10:39:36] ExternalModuleBindingPass time: 00:00.001
[2025-06-03 10:39:36] CollectInstantiationsPass ...
[2025-06-03 10:39:36] CollectInstantiationsPass time: 00:00.007
[2025-06-03 10:39:36] Ql checks ...
[2025-06-03 10:39:36] Ql checks time: 00:00.122
[2025-06-03 10:39:36] [ERROR] execute queries> ERROR: could not resolve module python (E:\advance_javascript\codeQL\7\queries\python-problm-1.ql:9,8-14)
[2025-06-03 10:39:36] [ERROR] execute queries> ERROR: could not resolve type Call (E:\advance_javascript\codeQL\7\queries\python-problm-1.ql:11,22-26)
[2025-06-03 10:39:36] [ERROR] execute queries> ERROR: could not resolve type Name (E:\advance_javascript\codeQL\7\queries\python-problm-1.ql:12,10-14)
[2025-06-03 10:39:36] [ERROR] execute queries> ERROR: could not resolve type Call (E:\advance_javascript\codeQL\7\queries\python-problm-1.ql:18,6-10)
[2025-06-03 10:39:36] Sequence stamp origin is -6042300113727153890
[2025-06-03 10:39:36] Pausing evaluation to close the cache at sequence stamp o+0
[2025-06-03 10:39:36] The disk cache is freshly trimmed; leave it be.
[2025-06-03 10:39:36] Unpausing evaluation
[2025-06-03 10:39:36] Exiting with code 2
