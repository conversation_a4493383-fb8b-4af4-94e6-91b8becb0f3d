[2025-06-03 11:04:36] This is codeql database analyze python-db-updated queries/python-password-vulnerability.ql --format=csv --output=password-results.csv --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks
[2025-06-03 11:04:36] Log file was started late.
[2025-06-03 11:04:36] [PROGRESS] database analyze> Running queries.
[2025-06-03 11:04:36] Running plumbing command: codeql database run-queries --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --no-rerun -- E:\advance_javascript\codeQL\7\python-db-updated queries/python-password-vulnerability.ql
[2025-06-03 11:04:36] Calling plumbing command: codeql resolve ram --dataset=E:\advance_javascript\codeQL\7\python-db-updated\db-python --format=json
[2025-06-03 11:04:36] [PROGRESS] resolve ram> Stringpool size measured as 2179482
[2025-06-03 11:04:36] Plumbing command codeql resolve ram completed:
                      [
                        "-J-Xmx1374M"
                      ]
[2025-06-03 11:04:36] Spawning plumbing command: execute queries -J-Xmx1374M --verbosity=progress --logdir=E:\advance_javascript\codeQL\7\python-db-updated\log --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --no-rerun --output=E:\advance_javascript\codeQL\7\python-db-updated\results -- E:\advance_javascript\codeQL\7\python-db-updated\db-python queries/python-password-vulnerability.ql
[2025-06-03 11:05:02] Plumbing command codeql execute queries terminated with status 0.
[2025-06-03 11:05:02] Plumbing command codeql database run-queries completed with status 0.
[2025-06-03 11:05:02] [PROGRESS] database analyze> Interpreting results.
[2025-06-03 11:05:02] Running plumbing command: codeql database interpret-results --format=csv -o=E:\advance_javascript\codeQL\7\password-results.csv --max-paths=4 --csv-location-format=line-column --print-diagnostics-summary --print-metrics-summary --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml -- E:\advance_javascript\codeQL\7\python-db-updated queries/python-password-vulnerability.ql
[2025-06-03 11:05:02] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --format=json -- queries/python-password-vulnerability.ql
[2025-06-03 11:05:03] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 11:05:03] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\queries\\python-password-vulnerability.ql"
                      ]
[2025-06-03 11:05:03] [PROGRESS] database interpret-results> Resolving extensions
[2025-06-03 11:05:03] Calling plumbing command: codeql resolve extensions --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml queries/python-password-vulnerability.ql
[2025-06-03 11:05:03] Calling plumbing command: codeql resolve queries --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml --allow-library-packs --format startingpacks -- queries/python-password-vulnerability.ql
[2025-06-03 11:05:03] [PROGRESS] resolve queries> Recording pack reference python-security-queries at E:\advance_javascript\codeQL\7.
[2025-06-03 11:05:03] Plumbing command codeql resolve queries completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7"
                      ]
[2025-06-03 11:05:03] Calling plumbing command: codeql resolve extensions-by-pack --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\qlconfig.yml -- E:\advance_javascript\codeQL\7
[2025-06-03 11:05:03] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}
[2025-06-03 11:05:03] [SPAMMY] resolve extensions-by-pack> [DERIVATION] python-security-queries: 1.0.0 {python-security-queries: not 1.0.0 {root: python-security-queries@1.0.0}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 1] python-security-queries: 1.0.0
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/python-all: * [*] {python-security-queries: * [*], codeql/python-all: not * [*] {dependency: python-security-queries@* [*] requires codeql/python-all@*}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 2] codeql/python-all: 4.0.7
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/yaml: * [*] {codeql/python-all: * [*], codeql/yaml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/yaml@1.0.23}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/xml: * [*] {codeql/python-all: * [*], codeql/xml: not * [*] {dependency: codeql/python-all@* [*] requires codeql/xml@1.0.23}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/util: * [*] {codeql/python-all: * [*], codeql/util: not * [*] {dependency: codeql/python-all@* [*] requires codeql/util@2.0.10}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/tutorial: * [*] {codeql/python-all: * [*], codeql/tutorial: not * [*] {dependency: codeql/python-all@* [*] requires codeql/tutorial@1.0.23}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/threat-models: * [*] {codeql/python-all: * [*], codeql/threat-models: not * [*] {dependency: codeql/python-all@* [*] requires codeql/threat-models@1.0.23}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/regex: * [*] {codeql/python-all: * [*], codeql/regex: not * [*] {dependency: codeql/python-all@* [*] requires codeql/regex@1.0.23}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/mad: * [*] {codeql/python-all: * [*], codeql/mad: not * [*] {dependency: codeql/python-all@* [*] requires codeql/mad@1.0.23}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/dataflow: * [*] {codeql/python-all: * [*], codeql/dataflow: not * [*] {dependency: codeql/python-all@* [*] requires codeql/dataflow@2.0.7}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/dataflow: * [*], codeql/util: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 3] codeql/dataflow: 2.0.7
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/typetracking: * [*] {codeql/dataflow: * [*], codeql/typetracking: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/typetracking@2.0.7}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DERIVATION] codeql/ssa: * [*] {codeql/dataflow: * [*], codeql/ssa: not * [*] {dependency: codeql/dataflow@* [*] requires codeql/ssa@1.1.2}}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/dataflow: not * [*] {dependency: codeql/mad@* [*] requires codeql/dataflow@2.0.7}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/mad: * [*], codeql/util: not * [*] {dependency: codeql/mad@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 4] codeql/mad: 1.0.23
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/regex: * [*], codeql/util: not * [*] {dependency: codeql/regex@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 5] codeql/regex: 1.0.23
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/ssa: * [*], codeql/util: not * [*] {dependency: codeql/ssa@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 6] codeql/ssa: 1.1.2
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 7] codeql/threat-models: 1.0.23
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 8] codeql/tutorial: 1.0.23
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/typetracking: * [*], codeql/util: not * [*] {dependency: codeql/typetracking@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 9] codeql/typetracking: 2.0.7
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 10] codeql/util: 2.0.10
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [INCOMPATIBILITY] codeql/xml: * [*], codeql/util: not * [*] {dependency: codeql/xml@* [*] requires codeql/util@2.0.10}
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 11] codeql/xml: 1.0.23
[2025-06-03 11:05:05] [SPAMMY] resolve extensions-by-pack> [DECISION 12] codeql/yaml: 1.0.23
[2025-06-03 11:05:05] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\ext\default-threat-models-fixup.model.yml.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 11:05:05] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Asyncpg.model.yml.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 5 tuples.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 6 tuples.
[2025-06-03 11:05:05] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\Stdlib.model.yml.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 12 tuples.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 1 tuples.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 66 tuples.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 11:05:05] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\empty.model.yml.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sourceModel: 0 tuples.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:sinkModel: 0 tuples.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:summaryModel: 0 tuples.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:neutralModel: 0 tuples.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 0 tuples.
[2025-06-03 11:05:05] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeVariableModel: 0 tuples.
[2025-06-03 11:05:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\python-all\4.0.7\semmle\python\frameworks\data\internal\subclass-capture\ALL.model.yml.
[2025-06-03 11:05:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/python-all:typeModel: 58275 tuples.
[2025-06-03 11:05:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\supported-threat-models.model.yml.
[2025-06-03 11:05:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelConfiguration: 1 tuples.
[2025-06-03 11:05:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\threat-models\1.0.23\ext\threat-model-grouping.model.yml.
[2025-06-03 11:05:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/threat-models:threatModelGrouping: 15 tuples.
[2025-06-03 11:05:07] [PROGRESS] resolve extensions-by-pack> Loading data extensions in C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks\codeql\util\2.0.10\ext\default-alert-filter.yml.
[2025-06-03 11:05:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsTo: 0 tuples.
[2025-06-03 11:05:07] [DETAILS] resolve extensions-by-pack>   Found extension targeting codeql/util:restrictAlertsToExactLocation: 0 tuples.
[2025-06-03 11:05:07] Plumbing command codeql resolve extensions-by-pack completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 11:05:07] Plumbing command codeql resolve extensions completed:
                      {
                        "models" : [ ],
                        "data" : {
                          "E:\\advance_javascript\\codeQL\\7" : [
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\ext\\default-threat-models-fixup.model.yml",
                              "index" : 0,
                              "firstRowId" : 0,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 0,
                              "firstRowId" : 1,
                              "rowCount" : 5,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Asyncpg.model.yml",
                              "index" : 1,
                              "firstRowId" : 6,
                              "rowCount" : 6,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 0,
                              "firstRowId" : 12,
                              "rowCount" : 12,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 1,
                              "firstRowId" : 24,
                              "rowCount" : 1,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 2,
                              "firstRowId" : 25,
                              "rowCount" : 66,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\Stdlib.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "sourceModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "sinkModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 1,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "summaryModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 2,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : true
                            },
                            {
                              "predicate" : "neutralModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 3,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 4,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "typeVariableModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\empty.model.yml",
                              "index" : 5,
                              "firstRowId" : 91,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "typeModel",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\python-all\\4.0.7\\semmle\\python\\frameworks\\data\\internal\\subclass-capture\\ALL.model.yml",
                              "index" : 0,
                              "firstRowId" : 91,
                              "rowCount" : 58275,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "threatModelConfiguration",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\supported-threat-models.model.yml",
                              "index" : 0,
                              "firstRowId" : 58366,
                              "rowCount" : 1,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "threatModelGrouping",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\threat-models\\1.0.23\\ext\\threat-model-grouping.model.yml",
                              "index" : 0,
                              "firstRowId" : 58367,
                              "rowCount" : 15,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "restrictAlertsTo",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 0,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            },
                            {
                              "predicate" : "restrictAlertsToExactLocation",
                              "file" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\qlpacks\\codeql\\util\\2.0.10\\ext\\default-alert-filter.yml",
                              "index" : 1,
                              "firstRowId" : 58382,
                              "rowCount" : 0,
                              "predicateHasOrigin" : false
                            }
                          ]
                        },
                        "threatModels" : {
                          "E:\\advance_javascript\\codeQL\\7" : {
                            "extensions" : [
                              {
                                "data" : [ ],
                                "addsTo" : {
                                  "extensible" : "threatModelConfiguration",
                                  "checkPresence" : true,
                                  "packName" : "codeql/threat-models"
                                }
                              }
                            ]
                          }
                        },
                        "extensionPacks" : [ ]
                      }
[2025-06-03 11:05:07] [PROGRESS] database interpret-results> Interpreting E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql...
[2025-06-03 11:05:07] Calling plumbing command: codeql resolve library-path --query=E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql --full-library-path=none --dbscheme=none --format=json
[2025-06-03 11:05:07] [DETAILS] resolve library-path> Resolving query at normalized path E:\advance_javascript\codeQL\7\queries\python-password-vulnerability.ql.
[2025-06-03 11:05:07] [DETAILS] resolve library-path> Found enclosing pack 'python-security-queries' at E:\advance_javascript\codeQL\7.
[2025-06-03 11:05:07] [DETAILS] resolve library-path> Adding compilation cache at C:\Users\<USER>\.codeql\compile-cache.
[2025-06-03 11:05:07] [DETAILS] resolve library-path> Dbscheme was explicitly overridden as E:\advance_javascript\codeQL\7\none
[2025-06-03 11:05:07] [DETAILS] resolve library-path> Library path was overridden on command line.
[2025-06-03 11:05:07] Plumbing command codeql resolve library-path completed:
                      {
                        "libraryPath" : [
                          "E:\\advance_javascript\\codeQL\\7\\none"
                        ],
                        "dbscheme" : "E:\\advance_javascript\\codeQL\\7\\none",
                        "compilationCache" : [
                          "C:\\Users\\<USER>\\.codeql\\compile-cache"
                        ],
                        "relativeName" : "python-security-queries\\queries\\python-password-vulnerability.ql",
                        "qlPackName" : "python-security-queries"
                      }
[2025-06-03 11:05:07] [DETAILS] database interpret-results>  ... found results file at E:\advance_javascript\codeQL\7\python-db-updated\results\python-security-queries\queries\python-password-vulnerability.bqrs.
[2025-06-03 11:05:07] [DETAILS] database interpret-results> Interpreted problem query "Hardcoded passwords and sensitive credentials" (py/hardcoded-credentials) at path E:\advance_javascript\codeQL\7\python-db-updated\results\python-security-queries\queries\python-password-vulnerability.bqrs.
[2025-06-03 11:05:07] [PROGRESS] database interpret-results> Reading source archive E:\advance_javascript\codeQL\7\python-db-updated\src.zip
[2025-06-03 11:05:07] [DETAILS] database interpret-results> Interpreting file coverage baseline information
[2025-06-03 11:05:07] [DETAILS] database interpret-results> Finished interpreting file coverage baseline information.
[2025-06-03 11:05:07] [DETAILS] database interpret-results> Interpreting diagnostic messages...
[2025-06-03 11:05:07] [SPAMMY] database interpret-results> Looking for diagnostics in E:\advance_javascript\codeQL\7\python-db-updated\diagnostic...
[2025-06-03 11:05:07] [SPAMMIER] database interpret-results> Found diagnostics file E:\advance_javascript\codeQL\7\python-db-updated\diagnostic\cli-diagnostics-add-20250603T053341.018Z.json.
[2025-06-03 11:05:07] [SPAMMIER] database interpret-results> Found diagnostics file E:\advance_javascript\codeQL\7\python-db-updated\diagnostic\cli-diagnostics-add-20250603T053342.509Z.json.
[2025-06-03 11:05:07] [SPAMMIER] database interpret-results> Found diagnostics file E:\advance_javascript\codeQL\7\python-db-updated\diagnostic\cli-diagnostics-add-20250603T053409.192Z.json.
[2025-06-03 11:05:08] [SPAMMY] database interpret-results> Looking for diagnostics in E:\advance_javascript\codeQL\7\python-db-updated\diagnostic\extractors...
[2025-06-03 11:05:08] [SPAMMY] database interpret-results> Looking for diagnostics in E:\advance_javascript\codeQL\7\python-db-updated\diagnostic\extractors\python...
[2025-06-03 11:05:08] [SPAMMY] database interpret-results> Looking for diagnostics in E:\advance_javascript\codeQL\7\python-db-updated\diagnostic\extractors\yaml...
[2025-06-03 11:05:08] [SPAMMY] database interpret-results> Looking for diagnostics in E:\advance_javascript\codeQL\7\python-db-updated\diagnostic\tracer...
[2025-06-03 11:05:08] [DETAILS] database interpret-results> Found 0 raw diagnostic messages.
[2025-06-03 11:05:08] [DETAILS] database interpret-results> Processed diagnostic messages (removed 0 due to limits, created 0 summary diagnostics for status page).
[2025-06-03 11:05:08] [DETAILS] database interpret-results> Interpreted diagnostic messages (62ms).
[2025-06-03 11:05:08] Calling plumbing command: codeql resolve languages --format=json
[2025-06-03 11:05:08] [DETAILS] resolve languages> Scanning for [codeql-extractor.yml] from C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\.codeqlmanifest.json
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\actions\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\cpp\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csv\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\go\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\html\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\java\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\javascript\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\properties\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\python\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\ruby\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\swift\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\xml\codeql-extractor.yml.
[2025-06-03 11:05:08] [DETAILS] resolve languages> Parsing C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\codeql-extractor.yml.
[2025-06-03 11:05:08] Plumbing command codeql resolve languages completed:
                      {
                        "aliases" : {
                          "c" : "cpp",
                          "c++" : "cpp",
                          "c-c++" : "cpp",
                          "c-cpp" : "cpp",
                          "c#" : "csharp",
                          "java-kotlin" : "java",
                          "kotlin" : "java",
                          "javascript-typescript" : "javascript",
                          "typescript" : "javascript"
                        },
                        "extractors" : {
                          "actions" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\actions",
                              "extractor_options" : { }
                            }
                          ],
                          "cpp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\cpp",
                              "extractor_options" : {
                                "scale_timeouts" : {
                                  "title" : "Value to scale compiler introspection timeouts with",
                                  "description" : "The extractor attempts to determine what compiler the source code being extracted is compiled with. To this end the extractor makes additional calls to the compiler, some of which are expected to return within a certain fixed time (either 10s or 15s). On some systems that are under high load this time might be too short, and can be scaled up using this option.\n",
                                  "type" : "string",
                                  "pattern" : "[0-9]+"
                                },
                                "log_verbosity" : {
                                  "title" : "Verbosity of the extractor logging",
                                  "description" : "Set the verbosity of the extractor logging to 'quiet' (0), 'normal' (1), 'chatty' (2), or 'noisy' (3). The default is 'normal'.\n",
                                  "type" : "string",
                                  "pattern" : "[0-3]"
                                }
                              }
                            }
                          ],
                          "csharp" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csharp",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'brotli' (the default, to write brotli-compressed TRAP), 'gzip', and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip|brotli)$"
                                    }
                                  }
                                },
                                "buildless" : {
                                  "title" : "DEPRECATED - Whether to use buildless (standalone) extraction.",
                                  "description" : "DEPRECATED: Use `--build-mode none` instead.\nA value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the extractor. The supported levels are (in order of increasing verbosity):\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "binlog" : {
                                  "title" : "Binlog",
                                  "description" : "[EXPERIMENTAL] The value is a path to the MsBuild binary log file that should be extracted. This option only works when `--build-mode none` is also specified.\n",
                                  "type" : "array"
                                }
                              }
                            }
                          ],
                          "csv" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\csv"
                            }
                          ],
                          "go" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\go",
                              "extractor_options" : {
                                "extract_tests" : {
                                  "title" : "Whether to include Go test files in the CodeQL database.",
                                  "description" : "A value indicating whether Go test files should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "extract_vendor_dirs" : {
                                  "title" : "Whether to include Go vendor directories in the CodeQL database.",
                                  "description" : "A value indicating whether Go vendor directories should be included in the CodeQL database. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "html" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\html"
                            }
                          ],
                          "java" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\java",
                              "extractor_options" : {
                                "exclude" : {
                                  "title" : "A glob excluding files from analysis.",
                                  "description" : "A glob indicating what files to exclude from the analysis. This accepts glob patterns that are supported by Java's 'getPathMatcher' implementation.\n",
                                  "type" : "string"
                                },
                                "add_prefer_source" : {
                                  "title" : "Whether to always prefer source files over class files.",
                                  "description" : "A value indicating whether source files should be preferred over class files. If set to 'true', the extraction adds '-Xprefer:source' to the javac command line. If set to 'false', the extraction uses the default javac behavior ('-Xprefer:newer'). The default is 'true'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless" : {
                                  "title" : "Whether to use buildless (standalone) extraction (experimental).",
                                  "description" : "A value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "buildless_dependency_dir" : {
                                  "title" : "The path where buildless (standalone) extraction should keep dependencies.",
                                  "description" : "If set, the buildless (standalone) extractor will store dependencies in this directory.\n",
                                  "type" : "string"
                                },
                                "minimize_dependency_jars" : {
                                  "title" : "Whether to rewrite and minimize downloaded JAR dependencies (experimental).",
                                  "description" : "If 'true', JAR dependencies downloaded during extraction will be rewritten to remove unneeded data, such as method bodies. The default is 'false'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "javascript" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\javascript",
                              "extractor_options" : {
                                "skip_types" : {
                                  "title" : "Skip type extraction for TypeScript",
                                  "description" : "Whether to skip the extraction of types in a TypeScript application",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "properties" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\properties"
                            }
                          ],
                          "python" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\python",
                              "extractor_options" : {
                                "logging" : {
                                  "title" : "Options pertaining to logging.",
                                  "description" : "Options pertaining to logging.",
                                  "type" : "object",
                                  "properties" : {
                                    "verbosity" : {
                                      "title" : "Python extractor logging verbosity level.",
                                      "description" : "Controls the level of verbosity of the CodeQL Python extractor.\nThe supported levels are (in order of increasing verbosity):\n\n  - off\n  - errors\n  - warnings\n  - info or progress\n  - debug or progress+\n  - trace or progress++\n  - progress+++\n",
                                      "type" : "string",
                                      "pattern" : "^(off|errors|warnings|(info|progress)|(debug|progress\\+)|(trace|progress\\+\\+)|progress\\+\\+\\+)$"
                                    }
                                  }
                                },
                                "python_executable_name" : {
                                  "title" : "Controls the name of the Python executable used by the Python extractor.",
                                  "description" : "The Python extractor uses platform-dependent heuristics to determine the name of the Python executable to use. Specifying a value for this option overrides the name of the Python executable used by the extractor. Accepted values are py, python and python3. Use this setting with caution, the Python extractor requires Python 3 to run.\n",
                                  "type" : "string",
                                  "pattern" : "^(py|python|python3)$"
                                }
                              }
                            }
                          ],
                          "ruby" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\ruby",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'gzip' (the default, to write gzip-compressed TRAP) and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip)$"
                                    }
                                  }
                                }
                              }
                            }
                          ],
                          "swift" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\swift"
                            }
                          ],
                          "xml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\xml"
                            }
                          ],
                          "yaml" : [
                            {
                              "extractor_root" : "C:\\Users\\<USER>\\Downloads\\codeql-bundle-win64\\codeql\\yaml"
                            }
                          ]
                        }
                      }
[2025-06-03 11:05:08] [SPAMMIER] database interpret-results> Loaded the following extractors: GitHub Actions, C/C++, C#, CSV, Go, HTML, Java/Kotlin, JavaScript/TypeScript, Java Properties Files, Python, Ruby, Swift, XML, YAML
[2025-06-03 11:05:08] [PROGRESS] database interpret-results> Exporting results to CSV...
[2025-06-03 11:05:08] [SPAMMY] database interpret-results> Skipping non-rule analysis py/baseline/expected-extracted-files
[2025-06-03 11:05:08] [PROGRESS] database interpret-results> Exported results to CSV (49ms).
[2025-06-03 11:05:08] Plumbing command codeql database interpret-results completed.
[2025-06-03 11:05:08] Exiting with code 0
