[2025-06-03 12:45:51] This is codeql database analyze ..\csharp-db csharp-sql-injection.ql --format=csv --output=csharp-sql-results.csv --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks
[2025-06-03 12:45:51] Log file was started late.
[2025-06-03 12:45:51] [PROGRESS] database analyze> Running queries.
[2025-06-03 12:45:51] Running plumbing command: codeql database run-queries --evaluator-log-level=5 --warnings=show --dynamic-join-order-mode=none --search-path=C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\qlpacks --qlconfig-file=E:\advance_javascript\codeQL\7\csharp-queries\qlconfig.yml --no-rerun -- E:\advance_javascript\codeQL\7\csharp-db csharp-sql-injection.ql
[2025-06-03 12:45:51] Exception caught at top level: Database at E:\advance_javascript\codeQL\7\csharp-db needs to be finalized before running queries; please run codeql database finalize
                      com.semmle.cli2.database.RunQueriesCommand.executeSubcommand(RunQueriesCommand.java:233)
                      com.semmle.cli2.picocli.SubcommandCommon.lambda$executeSubcommandWithMessages$5(SubcommandCommon.java:892)
                      com.semmle.cli2.picocli.SubcommandCommon.withCompilationMessages(SubcommandCommon.java:444)
                      com.semmle.cli2.picocli.SubcommandCommon.executeSubcommandWithMessages(SubcommandCommon.java:890)
                      com.semmle.cli2.picocli.PlumbingRunner.run(PlumbingRunner.java:119)
                      com.semmle.cli2.picocli.SubcommandCommon.runPlumbingInProcess(SubcommandCommon.java:201)
                      com.semmle.cli2.database.AnalyzeCommand.executeSubcommand(AnalyzeCommand.java:169)
                      com.semmle.cli2.picocli.SubcommandCommon.lambda$executeSubcommandWithMessages$5(SubcommandCommon.java:892)
                      com.semmle.cli2.picocli.SubcommandCommon.withCompilationMessages(SubcommandCommon.java:444)
                      com.semmle.cli2.picocli.SubcommandCommon.executeSubcommandWithMessages(SubcommandCommon.java:890)
                      com.semmle.cli2.picocli.SubcommandCommon.toplevelMain(SubcommandCommon.java:777)
                      com.semmle.cli2.picocli.SubcommandCommon.call(SubcommandCommon.java:757)
                      com.semmle.cli2.picocli.SubcommandMaker.runMain(SubcommandMaker.java:238)
                      com.semmle.cli2.picocli.SubcommandMaker.runMain(SubcommandMaker.java:259)
                      com.semmle.cli2.CodeQL.main(CodeQL.java:115)
