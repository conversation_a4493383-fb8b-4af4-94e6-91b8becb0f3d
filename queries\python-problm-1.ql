/**
 * @name Insecure use of eval()
 * @description Finds calls to the built-in eval() function, which can lead to code execution vulnerabilities.
 * @kind problem
 * @problem.severity error
 * @id py/insecure-eval
 */

import python

predicate isEvalCall(Call call) {
  exists(Name func |
    call.getFunc() = func and
    func.getId() = "eval"
  )
}

from Call call
where isEvalCall(call)
select call, "Insecure use of eval() function - this can lead to arbitrary code execution vulnerabilities."