/**
 * @name Insecure use of eval()
 * @description Finds calls to the built-in eval() function, which can lead to code execution vulnerabilities.
 * @kind problem
 * @problem.severity error
 * @id py/insecure-eval
 */

import python

from Call call
where
  call.getFunc().(Name).getId() = "eval"
select call, "Insecure use of eval() function - this can lead to arbitrary code execution vulnerabilities."
