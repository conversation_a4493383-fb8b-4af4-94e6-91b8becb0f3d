---
queries:
 -
  pack: python-security-queries#3
  relativeQueryPath: queries/python-password-simple.ql
  relativeBqrsPath: python-security-queries/queries/python-password-simple.bqrs
  metadata:
    name: Hardcoded credentials detection
    description: "Detects hardcoded passwords, API keys, and secrets in variable assignments."
    kind: problem
    problem.severity: error
    id: py/hardcoded-credentials-simple
extensionPacks: []
packs:
  codeql/util#2:
    name: codeql/util
    version: 2.0.10
    isLibrary: true
    isExtensionPack: false
    localPath: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/util/2.0.10/
    localPackDefinitionFile: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/util/2.0.10/qlpack.yml
    headSha: 4bb829ebec2082e8c06f02a7d9c07e181d27c784
    runDataExtensions: []
  codeql/python-all#0:
    name: codeql/python-all
    version: 4.0.7
    isLibrary: true
    isExtensionPack: false
    localPath: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/python-all/4.0.7/
    localPackDefinitionFile: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/python-all/4.0.7/qlpack.yml
    headSha: 4bb829ebec2082e8c06f02a7d9c07e181d27c784
    runDataExtensions: []
  codeql/threat-models#1:
    name: codeql/threat-models
    version: 1.0.23
    isLibrary: true
    isExtensionPack: false
    localPath: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/threat-models/1.0.23/
    localPackDefinitionFile: file:///C:/Users/<USER>/Downloads/codeql-bundle-win64/codeql/qlpacks/codeql/threat-models/1.0.23/qlpack.yml
    headSha: 4bb829ebec2082e8c06f02a7d9c07e181d27c784
    runDataExtensions: []
  python-security-queries#3:
    name: python-security-queries
    version: 1.0.0
    isLibrary: false
    isExtensionPack: false
    localPath: file:///E:/advance_javascript/codeQL/7/
    localPackDefinitionFile: file:///E:/advance_javascript/codeQL/7/qlpack.yml
    runDataExtensions:
     -
      pack: codeql/python-all#0
      relativePath: ext/default-threat-models-fixup.model.yml
      index: 0
      firstRowId: 0
      rowCount: 1
      locations:
        lineNumbers: A=8
        columnNumbers: A=9
     -
      pack: codeql/python-all#0
      relativePath: semmle/python/frameworks/Asyncpg.model.yml
      index: 0
      firstRowId: 1
      rowCount: 5
      locations:
        lineNumbers: A=7+1+2+1+2
        columnNumbers: A=9*5
     -
      pack: codeql/python-all#0
      relativePath: semmle/python/frameworks/Asyncpg.model.yml
      index: 1
      firstRowId: 6
      rowCount: 6
      locations:
        lineNumbers: A=20+4+1*2+2+1
        columnNumbers: A=9*6
     -
      pack: codeql/python-all#0
      relativePath: semmle/python/frameworks/Stdlib.model.yml
      index: 0
      firstRowId: 12
      rowCount: 12
      locations:
        lineNumbers: A=6+1*4+2+1+2+1*2+4+2
        columnNumbers: A=9*12
     -
      pack: codeql/python-all#0
      relativePath: semmle/python/frameworks/Stdlib.model.yml
      index: 1
      firstRowId: 24
      rowCount: 1
      locations:
        lineNumbers: A=29
        columnNumbers: A=9
     -
      pack: codeql/python-all#0
      relativePath: semmle/python/frameworks/Stdlib.model.yml
      index: 2
      firstRowId: 25
      rowCount: 66
      locations:
        lineNumbers: A=37+1+2+4+2*2+4+2*3+1+2+1+2+1+2+4+2+4+2*2+3+2*2+3+1+2*4+4+1+4+1+4+1*5+2*4+4+1+2*11+3+2+3+4+1+2*2+1+2
        columnNumbers: A=9*66
     -
      pack: codeql/python-all#0
      relativePath: semmle/python/frameworks/data/internal/subclass-capture/ALL.model.yml
      index: 0
      firstRowId: 91
      rowCount: 58275
      locations:
        lineNumbers: A=7+3*58274
        columnNumbers: A=5*58275
     -
      pack: codeql/threat-models#1
      relativePath: ext/supported-threat-models.model.yml
      index: 0
      firstRowId: 58366
      rowCount: 1
      locations:
        lineNumbers: A=6
        columnNumbers: A=9
     -
      pack: codeql/threat-models#1
      relativePath: ext/threat-model-grouping.model.yml
      index: 0
      firstRowId: 58367
      rowCount: 15
      locations:
        lineNumbers: A=8+3+1+3+1*5+3+1+5+1*3
        columnNumbers: A=9*15
