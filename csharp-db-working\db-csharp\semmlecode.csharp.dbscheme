/* This is a dummy line to alter the dbscheme, so we can make a database upgrade
 * without actually changing any of the dbscheme predicates. It contains a date
 * to allow for such updates in the future as well.
 *
 * 2021-07-14
 *
 * DO NOT remove this comment carelessly, since it can revert the dbscheme back to a
 * previously seen state (matching a previously seen SHA), which would make the upgrade
 * mechanism not work properly.
 */

/**
 * An invocation of the compiler. Note that more than one file may be
 * compiled per invocation. For example, this command compiles three
 * source files:
 *
 *   csc f1.cs f2.cs f3.cs
 *
 * The `id` simply identifies the invocation, while `cwd` is the working
 * directory from which the compiler was invoked.
 */
compilations(
    unique int id : @compilation,
    string cwd : string ref
);

compilation_info(
  int id : @compilation ref,
  string info_key: string ref,
  string info_value: string ref
)

/**
 * The arguments that were passed to the extractor for a compiler
 * invocation. If `id` is for the compiler invocation
 *
 *   csc f1.cs f2.cs f3.cs
 *
 * then typically there will be rows for
 *
 * num | arg
 * --- | ---
 * 0   | --compiler
 * 1   | *path to compiler*
 * 2   | f1.cs
 * 3   | f2.cs
 * 4   | f3.cs
 */
#keyset[id, num]
compilation_args(
    int id : @compilation ref,
    int num : int ref,
    string arg : string ref
);

/**
 * The expanded arguments that were passed to the extractor for a
 * compiler invocation. This is similar to `compilation_args`, but
 * for a `@someFile.rsp` argument, it includes the arguments from that
 * file, rather than just taking the argument literally.
 */
#keyset[id, num]
compilation_expanded_args(
    int id : @compilation ref,
    int num : int ref,
    string arg : string ref
);

/**
 * The source files that are compiled by a compiler invocation.
 * If `id` is for the compiler invocation
 *
 *   csc f1.cs f2.cs f3.cs
 *
 * then there will be rows for
 *
 * num | arg
 * --- | ---
 * 0   | f1.cs
 * 1   | f2.cs
 * 2   | f3.cs
 */
#keyset[id, num]
compilation_compiling_files(
    int id : @compilation ref,
    int num : int ref,
    int file : @file ref
);

/**
 * The references used by a compiler invocation.
 * If `id` is for the compiler invocation
 *
 *   csc f1.cs f2.cs f3.cs /r:ref1.dll /r:ref2.dll /r:ref3.dll
 *
 * then there will be rows for
 *
 * num | arg
 * --- | ---
 * 0   | ref1.dll
 * 1   | ref2.dll
 * 2   | ref3.dll
 */
#keyset[id, num]
compilation_referencing_files(
    int id : @compilation ref,
    int num : int ref,
    int file : @file ref
);

/**
 * The time taken by the extractor for a compiler invocation.
 *
 * For each file `num`, there will be rows for
 *
 * kind | seconds
 * ---- | ---
 * 1    | CPU seconds used by the extractor frontend
 * 2    | Elapsed seconds during the extractor frontend
 * 3    | CPU seconds used by the extractor backend
 * 4    | Elapsed seconds during the extractor backend
 */
#keyset[id, num, kind]
compilation_time(
    int id : @compilation ref,
    int num : int ref,
    /* kind:
       1 = frontend_cpu_seconds
       2 = frontend_elapsed_seconds
       3 = extractor_cpu_seconds
       4 = extractor_elapsed_seconds
    */
    int kind : int ref,
    float seconds : float ref
);

/**
 * An error or warning generated by the extractor.
 * The diagnostic message `diagnostic` was generated during compiler
 * invocation `compilation`, and is the `file_number_diagnostic_number`th
 * message generated while extracting the `file_number`th file of that
 * invocation.
 */
#keyset[compilation, file_number, file_number_diagnostic_number]
diagnostic_for(
    unique int diagnostic : @diagnostic ref,
    int compilation : @compilation ref,
    int file_number : int ref,
    int file_number_diagnostic_number : int ref
);

diagnostics(
    unique int id: @diagnostic,
    int severity: int ref,
    string error_tag: string ref,
    string error_message: string ref,
    string full_error_message: string ref,
    int location: @location ref
);

extractor_messages(
    unique int id: @extractor_message,
    int severity: int ref,
    string origin : string ref,
    string text : string ref,
    string entity : string ref,
    int location: @location ref,
    string stack_trace : string ref
);

/**
 * If extraction was successful, then `cpu_seconds` and
 * `elapsed_seconds` are the CPU time and elapsed time (respectively)
 * that extraction took for compiler invocation `id`.
 */
compilation_finished(
    unique int id : @compilation ref,
    float cpu_seconds : float ref,
    float elapsed_seconds : float ref
);

compilation_assembly(
  unique int id : @compilation ref,
  int assembly: @assembly ref
)

// Populated by the CSV extractor
externalData(
   int id: @externalDataElement,
   string path: string ref,
   int column: int ref,
   string value: string ref);

sourceLocationPrefix(
  string prefix: string ref);

/*
 * C# dbscheme
 */

/** ELEMENTS **/

@element = @declaration | @stmt | @expr | @modifier | @attribute | @namespace_declaration
         | @using_directive | @type_parameter_constraints | @externalDataElement
         | @xmllocatable | @asp_element | @namespace | @preprocessor_directive;

@declaration = @callable | @generic | @assignable | @namespace;

@named_element = @namespace | @declaration;

@declaration_with_accessors = @property | @indexer | @event;

@assignable = @variable | @assignable_with_accessors | @event;

@assignable_with_accessors = @property | @indexer;

@attributable = @assembly | @field | @parameter | @operator | @method | @constructor
              | @destructor | @callable_accessor | @value_or_ref_type | @declaration_with_accessors
              | @local_function | @lambda_expr;

/** LOCATIONS, ASEMMBLIES, MODULES, FILES and FOLDERS **/

@location = @location_default | @assembly;

locations_default(
  unique int id: @location_default,
  int file: @file ref,
  int beginLine: int ref,
  int beginColumn: int ref,
  int endLine: int ref,
  int endColumn: int ref);

locations_mapped(
  unique int id: @location_default ref,
  int mapped_to: @location_default ref);

@sourceline = @file | @callable | @xmllocatable;

numlines(
  int element_id: @sourceline ref,
  int num_lines: int ref,
  int num_code: int ref,
  int num_comment: int ref);

assemblies(
  unique int id: @assembly,
  int file: @file ref,
  string fullname: string ref,
  string name: string ref,
  string version: string ref);

files(
  unique int id: @file,
  string name: string ref);

folders(
  unique int id: @folder,
  string name: string ref);

@container = @folder | @file ;

containerparent(
  int parent: @container ref,
  unique int child: @container ref);

file_extraction_mode(
  unique int file: @file ref,
  int mode: int ref
  /* 0 = normal, 1 = standalone extractor */
  );

/** NAMESPACES **/

@type_container = @namespace | @type;

namespaces(
  unique int id: @namespace,
  string name: string ref);

namespace_declarations(
  unique int id: @namespace_declaration,
  int namespace_id: @namespace ref);

namespace_declaration_location(
  unique int id: @namespace_declaration ref,
  int loc: @location ref);

parent_namespace(
  unique int child_id: @type_container ref,
  int namespace_id: @namespace ref);

@declaration_or_directive = @namespace_declaration | @type | @using_directive;

parent_namespace_declaration(
  int child_id: @declaration_or_directive ref, // cannot be unique because of partial classes
  int namespace_id: @namespace_declaration ref);

@using_directive = @using_namespace_directive | @using_static_directive;

using_global(
  unique int id: @using_directive ref
);

using_namespace_directives(
  unique int id: @using_namespace_directive,
  int namespace_id: @namespace ref);

using_static_directives(
  unique int id: @using_static_directive,
  int type_id: @type_or_ref ref);

using_directive_location(
  unique int id: @using_directive ref,
  int loc: @location ref);

@preprocessor_directive = @pragma_warning | @pragma_checksum | @directive_define | @directive_undefine | @directive_warning
  | @directive_error | @directive_nullable | @directive_line | @directive_region | @directive_endregion | @directive_if
  | @directive_elif | @directive_else | @directive_endif;

@conditional_directive = @directive_if | @directive_elif;
@branch_directive = @directive_if | @directive_elif | @directive_else;

directive_ifs(
  unique int id: @directive_if,
  int branchTaken: int ref,     /* 0: false, 1: true */
  int conditionValue: int ref); /* 0: false, 1: true */

directive_elifs(
  unique int id: @directive_elif,
  int branchTaken: int ref,     /* 0: false, 1: true */
  int conditionValue: int ref,  /* 0: false, 1: true */
  int parent: @directive_if ref,
  int index: int ref);

directive_elses(
  unique int id: @directive_else,
  int branchTaken: int ref,   /* 0: false, 1: true */
  int parent: @directive_if ref,
  int index: int ref);

#keyset[id, start]
directive_endifs(
  unique int id: @directive_endif,
  unique int start: @directive_if ref);

directive_define_symbols(
  unique int id: @define_symbol_expr ref,
  string name: string ref);

directive_regions(
  unique int id: @directive_region,
  string name: string ref);

#keyset[id, start]
directive_endregions(
  unique int id: @directive_endregion,
  unique int start: @directive_region ref);

directive_lines(
  unique int id: @directive_line,
  int kind: int ref); /* 0: default, 1: hidden, 2: numeric, 3: span */

directive_line_value(
  unique int id: @directive_line ref,
  int line: int ref);

directive_line_file(
  unique int id: @directive_line ref,
  int file: @file ref);

directive_line_offset(
  unique int id: @directive_line ref,
  int offset: int ref);

directive_line_span(
  unique int id: @directive_line ref,
  int startLine: int ref,
  int startColumn: int ref,
  int endLine: int ref,
  int endColumn: int ref);

directive_nullables(
  unique int id: @directive_nullable,
  int setting: int ref, /* 0: disable, 1: enable, 2: restore */
  int target: int ref); /* 0: none, 1: annotations, 2: warnings */

directive_warnings(
  unique int id: @directive_warning,
  string message: string ref);

directive_errors(
  unique int id: @directive_error,
  string message: string ref);

directive_undefines(
  unique int id: @directive_undefine,
  string name: string ref);

directive_defines(
  unique int id: @directive_define,
  string name: string ref);

pragma_checksums(
  unique int id: @pragma_checksum,
  int file: @file ref,
  string guid: string ref,
  string bytes: string ref);

pragma_warnings(
  unique int id: @pragma_warning,
  int kind: int ref /* 0 = disable, 1 = restore */);

#keyset[id, index]
pragma_warning_error_codes(
  int id: @pragma_warning ref,
  string errorCode: string ref,
  int index: int ref);

preprocessor_directive_location(
  unique int id: @preprocessor_directive ref,
  int loc: @location ref);

preprocessor_directive_compilation(
  int id: @preprocessor_directive ref,
  int compilation: @compilation ref);

preprocessor_directive_active(
  unique int id: @preprocessor_directive ref,
  int active: int ref);  /* 0: false, 1: true */

/** TYPES **/

types(
  unique int id: @type,
  int kind: int ref,
  string name: string ref);

case @type.kind of
   1 = @bool_type
|  2 = @char_type
|  3 = @decimal_type
|  4 = @sbyte_type
|  5 = @short_type
|  6 = @int_type
|  7 = @long_type
|  8 = @byte_type
|  9 = @ushort_type
| 10 = @uint_type
| 11 = @ulong_type
| 12 = @float_type
| 13 = @double_type
| 14 = @enum_type
| 15 = @struct_type
| 17 = @class_type
| 19 = @interface_type
| 20 = @delegate_type
| 21 = @null_type
| 22 = @type_parameter
| 23 = @pointer_type
| 24 = @nullable_type
| 25 = @array_type
| 26 = @void_type
| 27 = @int_ptr_type
| 28 = @uint_ptr_type
| 29 = @dynamic_type
| 30 = @arglist_type
| 31 = @unknown_type
| 32 = @tuple_type
| 33 = @function_pointer_type
| 34 = @inline_array_type
  ;

@simple_type = @bool_type | @char_type | @integral_type | @floating_point_type | @decimal_type;
@integral_type = @signed_integral_type | @unsigned_integral_type;
@signed_integral_type = @sbyte_type | @short_type | @int_type | @long_type;
@unsigned_integral_type = @byte_type  | @ushort_type | @uint_type | @ulong_type;
@floating_point_type = @float_type | @double_type;
@value_type = @simple_type | @enum_type | @struct_type | @nullable_type | @int_ptr_type
            | @uint_ptr_type | @tuple_type | @void_type | @inline_array_type;
@ref_type = @class_type | @interface_type | @array_type | @delegate_type | @null_type
          | @dynamic_type;
@value_or_ref_type = @value_type | @ref_type;

typerefs(
  unique int id: @typeref,
  string name: string ref);

typeref_type(
  int id: @typeref ref,
  unique int typeId: @type ref);

@type_or_ref = @type | @typeref;

array_element_type(
  unique int array: @array_type ref,
  int dimension: int ref,
  int rank: int ref,
  int element: @type_or_ref ref);

nullable_underlying_type(
  unique int nullable: @nullable_type ref,
  int underlying: @type_or_ref ref);

pointer_referent_type(
  unique int pointer: @pointer_type ref,
  int referent: @type_or_ref ref);

enum_underlying_type(
  unique int enum_id: @enum_type ref,
  int underlying_type_id: @type_or_ref ref);

delegate_return_type(
  unique int delegate_id: @delegate_type ref,
  int return_type_id: @type_or_ref ref);

function_pointer_return_type(
    unique int function_pointer_id: @function_pointer_type ref,
    int return_type_id: @type_or_ref ref);

extend(
  int sub: @type ref,
  int super: @type_or_ref ref);

anonymous_types(
  unique int id: @type ref);

@interface_or_ref = @interface_type | @typeref;

implement(
  int sub: @type ref,
  int super: @type_or_ref ref);

type_location(
  int id: @type ref,
  int loc: @location ref);

tuple_underlying_type(
  unique int tuple: @tuple_type ref,
  int struct: @type_or_ref ref);

#keyset[tuple, index]
tuple_element(
  int tuple: @tuple_type ref,
  int index: int ref,
  unique int field: @field ref);

attributes(
  unique int id: @attribute,
  int kind: int ref,
  int type_id: @type_or_ref ref,
  int target:  @attributable ref);

case @attribute.kind of
  0 = @attribute_default
| 1 = @attribute_return
| 2 = @attribute_assembly
| 3 = @attribute_module
;

attribute_location(
  int id: @attribute ref,
  int loc: @location ref);

@type_mention_parent = @element | @type_mention;

type_mention(
  unique int id: @type_mention,
  int type_id: @type_or_ref ref,
  int parent: @type_mention_parent ref);

type_mention_location(
  unique int id: @type_mention ref,
  int loc: @location ref);

@has_type_annotation = @assignable | @type_parameter | @callable | @expr | @delegate_type | @generic | @function_pointer_type;

/**
 * A direct annotation on an entity, for example `string? x;`.
 *
 * Annotations:
 * 2 = reftype is not annotated "!"
 * 3 = reftype is annotated "?"
 * 4 = readonly ref type / in parameter
 * 5 = ref type parameter, return or local variable
 * 6 = out parameter
 *
 * Note that the annotation depends on the element it annotates.
 * @assignable: The annotation is on the type of the assignable, for example the variable type.
 * @type_parameter: The annotation is on the reftype constraint
 * @callable: The annotation is on the return type
 * @array_type: The annotation is on the element type
 */
type_annotation(int id: @has_type_annotation ref, int annotation: int ref);

nullability(unique int nullability: @nullability, int kind: int ref);

case @nullability.kind of
  0 = @oblivious
| 1 = @not_annotated
| 2 = @annotated
;

#keyset[parent, index]
nullability_parent(int nullability: @nullability ref, int index: int ref, int parent: @nullability ref)

type_nullability(int id: @has_type_annotation ref, int nullability: @nullability ref);

/**
 * The nullable flow state of an expression, as determined by Roslyn.
 *   0 = none (default, not populated)
 *   1 = not null
 *   2 = maybe null
 */
expr_flowstate(unique int id: @expr ref, int state: int ref);

/** GENERICS **/

@generic = @type | @method | @local_function;

type_parameters(
  unique int id: @type_parameter ref,
  int index: int ref,
  int generic_id: @generic ref,
  int variance: int ref /* none = 0, out = 1, in = 2 */);

#keyset[constructed_id, index]
type_arguments(
  int id: @type_or_ref ref,
  int index: int ref,
  int constructed_id: @generic_or_ref ref);

@generic_or_ref = @generic | @typeref;

constructed_generic(
  unique int constructed: @generic ref,
  int generic: @generic_or_ref ref);

type_parameter_constraints(
  unique int id: @type_parameter_constraints,
  int param_id: @type_parameter ref);

type_parameter_constraints_location(
  int id: @type_parameter_constraints ref,
  int loc: @location ref);

general_type_parameter_constraints(
  int id: @type_parameter_constraints ref,
  int kind: int ref /* class = 1, struct = 2, new = 3 */);

specific_type_parameter_constraints(
  int id: @type_parameter_constraints ref,
  int base_id: @type_or_ref ref);

specific_type_parameter_nullability(
  int id: @type_parameter_constraints ref,
  int base_id: @type_or_ref ref,
  int nullability: @nullability ref);

/** FUNCTION POINTERS */

function_pointer_calling_conventions(
  int id: @function_pointer_type ref,
  int kind: int ref);

#keyset[id, index]
has_unmanaged_calling_conventions(
  int id: @function_pointer_type ref,
  int index: int ref,
  int conv_id: @type_or_ref ref);

/** MODIFIERS */

@modifiable = @modifiable_direct | @event_accessor;

@modifiable_direct = @member | @accessor | @local_function | @anonymous_function_expr;

modifiers(
  unique int id: @modifier,
  string name: string ref);

has_modifiers(
  int id: @modifiable_direct ref,
  int mod_id: @modifier ref);

/** MEMBERS **/

@member = @method | @constructor | @destructor | @field | @property | @event | @operator | @indexer | @type;

@named_exprorstmt = @goto_stmt | @labeled_stmt | @expr;

@virtualizable = @method | @property | @indexer | @event | @operator;

exprorstmt_name(
  unique int parent_id: @named_exprorstmt ref,
  string name: string ref);

nested_types(
  unique int id: @type ref,
  int declaring_type_id: @type ref,
  int unbound_id: @type ref);

properties(
  unique int id: @property,
  string name: string ref,
  int declaring_type_id: @type ref,
  int type_id: @type_or_ref ref,
  int unbound_id: @property ref);

property_location(
  int id: @property ref,
  int loc: @location ref);

indexers(
  unique int id: @indexer,
  string name: string ref,
  int declaring_type_id: @type ref,
  int type_id: @type_or_ref ref,
  int unbound_id: @indexer ref);

indexer_location(
  int id: @indexer ref,
  int loc: @location ref);

accessors(
  unique int id: @accessor,
  int kind: int ref,
  string name: string ref,
  int declaring_member_id: @member ref,
  int unbound_id: @accessor ref);

case @accessor.kind of
  1 = @getter
| 2 = @setter
  ;

init_only_accessors(
  unique int id: @accessor ref);

accessor_location(
  int id: @accessor ref,
  int loc: @location ref);

events(
  unique int id: @event,
  string name: string ref,
  int declaring_type_id: @type ref,
  int type_id: @type_or_ref ref,
  int unbound_id: @event ref);

event_location(
  int id: @event ref,
  int loc: @location ref);

event_accessors(
  unique int id: @event_accessor,
  int kind: int ref,
  string name: string ref,
  int declaring_event_id: @event ref,
  int unbound_id: @event_accessor ref);

case @event_accessor.kind of
  1 = @add_event_accessor
| 2 = @remove_event_accessor
  ;

event_accessor_location(
  int id: @event_accessor ref,
  int loc: @location ref);

operators(
  unique int id: @operator,
  string name: string ref,
  string symbol: string ref,
  int declaring_type_id: @type ref,
  int type_id: @type_or_ref ref,
  int unbound_id: @operator ref);

operator_location(
  int id: @operator ref,
  int loc: @location ref);

constant_value(
  int id: @variable ref,
  string value: string ref);

/** CALLABLES **/

@callable = @method | @constructor | @destructor | @operator | @callable_accessor | @anonymous_function_expr | @local_function;

@callable_accessor = @accessor | @event_accessor;

methods(
  unique int id: @method,
  string name: string ref,
  int declaring_type_id: @type ref,
  int type_id: @type_or_ref ref,
  int unbound_id: @method ref);

method_location(
  int id: @method ref,
  int loc: @location ref);

constructors(
  unique int id: @constructor,
  string name: string ref,
  int declaring_type_id: @type ref,
  int unbound_id: @constructor ref);

constructor_location(
  int id: @constructor ref,
  int loc: @location ref);

destructors(
  unique int id: @destructor,
  string name: string ref,
  int declaring_type_id: @type ref,
  int unbound_id: @destructor ref);

destructor_location(
  int id: @destructor ref,
  int loc: @location ref);

overrides(
  int id: @callable ref,
  int base_id: @callable ref);

explicitly_implements(
  int id: @member ref,
  int interface_id: @interface_or_ref ref);

local_functions(
    unique int id: @local_function,
    string name: string ref,
    int return_type: @type ref,
    int unbound_id: @local_function ref);

local_function_stmts(
    unique int fn: @local_function_stmt ref,
    int stmt: @local_function ref);

/** VARIABLES **/

@variable = @local_scope_variable | @field;

@local_scope_variable = @local_variable | @parameter;

fields(
  unique int id: @field,
  int kind: int ref,
  string name: string ref,
  int declaring_type_id: @type ref,
  int type_id: @type_or_ref ref,
  int unbound_id: @field ref);

case @field.kind of
  1 = @addressable_field
| 2 = @constant
  ;

field_location(
  int id: @field ref,
  int loc: @location ref);

localvars(
  unique int id: @local_variable,
  int kind: int ref,
  string name: string ref,
  int implicitly_typed: int ref /* 0 = no, 1 = yes */,
  int type_id: @type_or_ref ref,
  int parent_id: @local_var_decl_expr ref);

case @local_variable.kind of
  1 = @addressable_local_variable
| 2 = @local_constant
| 3 = @local_variable_ref
  ;

localvar_location(
  unique int id: @local_variable ref,
  int loc: @location ref);

@parameterizable = @callable | @delegate_type | @indexer | @function_pointer_type;

#keyset[name, parent_id]
#keyset[index, parent_id]
params(
  unique int id: @parameter,
  string name: string ref,
  int type_id: @type_or_ref ref,
  int index: int ref,
  int mode: int ref, /* value = 0, ref = 1, out = 2, params/array = 3, this = 4, in = 5, ref readonly = 6 */
  int parent_id: @parameterizable ref,
  int unbound_id: @parameter ref);

param_location(
  int id: @parameter ref,
  int loc: @location ref);

@has_scoped_annotation = @local_scope_variable

scoped_annotation(
  int id: @has_scoped_annotation ref,
  int kind: int ref // scoped ref = 1, scoped value = 2
  );

/** STATEMENTS **/

@exprorstmt_parent = @control_flow_element | @top_level_exprorstmt_parent;

statements(
  unique int id: @stmt,
  int kind: int ref);

#keyset[index, parent]
stmt_parent(
  unique int stmt: @stmt ref,
  int index: int ref,
  int parent: @control_flow_element ref);

@top_level_stmt_parent = @callable;

// [index, parent] is not a keyset because the same parent may be compiled multiple times
stmt_parent_top_level(
  unique int stmt: @stmt ref,
  int index: int ref,
  int parent: @top_level_stmt_parent ref);

case @stmt.kind of
   1 = @block_stmt
|  2 = @expr_stmt
|  3 = @if_stmt
|  4 = @switch_stmt
|  5 = @while_stmt
|  6 = @do_stmt
|  7 = @for_stmt
|  8 = @foreach_stmt
|  9 = @break_stmt
| 10 = @continue_stmt
| 11 = @goto_stmt
| 12 = @goto_case_stmt
| 13 = @goto_default_stmt
| 14 = @throw_stmt
| 15 = @return_stmt
| 16 = @yield_stmt
| 17 = @try_stmt
| 18 = @checked_stmt
| 19 = @unchecked_stmt
| 20 = @lock_stmt
| 21 = @using_block_stmt
| 22 = @var_decl_stmt
| 23 = @const_decl_stmt
| 24 = @empty_stmt
| 25 = @unsafe_stmt
| 26 = @fixed_stmt
| 27 = @label_stmt
| 28 = @catch
| 29 = @case_stmt
| 30 = @local_function_stmt
| 31 = @using_decl_stmt
  ;

@using_stmt = @using_block_stmt | @using_decl_stmt;

@labeled_stmt = @label_stmt | @case;

@decl_stmt = @var_decl_stmt | @const_decl_stmt | @using_decl_stmt;

@cond_stmt = @if_stmt | @switch_stmt;

@loop_stmt = @while_stmt | @do_stmt | @for_stmt | @foreach_stmt;

@jump_stmt = @break_stmt | @goto_any_stmt | @continue_stmt | @throw_stmt | @return_stmt
           | @yield_stmt;

@goto_any_stmt = @goto_default_stmt | @goto_case_stmt | @goto_stmt;


stmt_location(
  unique int id: @stmt ref,
  int loc: @location ref);

catch_type(
  unique int catch_id: @catch ref,
  int type_id: @type_or_ref ref,
  int kind: int ref /* explicit = 1, implicit = 2 */);

foreach_stmt_info(
  unique int id: @foreach_stmt ref,
  int kind: int ref /* non-async = 1, async = 2 */);

@foreach_symbol =  @method | @property | @type_or_ref;

#keyset[id, kind]
foreach_stmt_desugar(
  int id: @foreach_stmt ref,
  int symbol: @foreach_symbol ref,
  int kind: int ref /* GetEnumeratorMethod = 1, CurrentProperty = 2, MoveNextMethod = 3, DisposeMethod = 4, ElementType = 5 */);

/** EXPRESSIONS **/

expressions(
  unique int id: @expr,
  int kind: int ref,
  int type_id: @type_or_ref ref);

#keyset[index, parent]
expr_parent(
  unique int expr: @expr ref,
  int index: int ref,
  int parent: @control_flow_element ref);

@top_level_expr_parent = @attribute | @field | @property | @indexer | @parameter | @directive_if | @directive_elif;

@top_level_exprorstmt_parent = @top_level_expr_parent | @top_level_stmt_parent;

// [index, parent] is not a keyset because the same parent may be compiled multiple times
expr_parent_top_level(
  unique int expr: @expr ref,
  int index: int ref,
  int parent: @top_level_exprorstmt_parent ref);

case @expr.kind of
/* literal */
   1 = @bool_literal_expr
|  2 = @char_literal_expr
|  3 = @decimal_literal_expr
|  4 = @int_literal_expr
|  5 = @long_literal_expr
|  6 = @uint_literal_expr
|  7 = @ulong_literal_expr
|  8 = @float_literal_expr
|  9 = @double_literal_expr
| 10 = @utf16_string_literal_expr
| 11 = @null_literal_expr
/* primary & unary */
| 12 = @this_access_expr
| 13 = @base_access_expr
| 14 = @local_variable_access_expr
| 15 = @parameter_access_expr
| 16 = @field_access_expr
| 17 = @property_access_expr
| 18 = @method_access_expr
| 19 = @event_access_expr
| 20 = @indexer_access_expr
| 21 = @array_access_expr
| 22 = @type_access_expr
| 23 = @typeof_expr
| 24 = @method_invocation_expr
| 25 = @delegate_invocation_expr
| 26 = @operator_invocation_expr
| 27 = @cast_expr
| 28 = @object_creation_expr
| 29 = @explicit_delegate_creation_expr
| 30 = @implicit_delegate_creation_expr
| 31 = @array_creation_expr
| 32 = @default_expr
| 33 = @plus_expr
| 34 = @minus_expr
| 35 = @bit_not_expr
| 36 = @log_not_expr
| 37 = @post_incr_expr
| 38 = @post_decr_expr
| 39 = @pre_incr_expr
| 40 = @pre_decr_expr
/* multiplicative */
| 41 = @mul_expr
| 42 = @div_expr
| 43 = @rem_expr
/* additive */
| 44 = @add_expr
| 45 = @sub_expr
/* shift */
| 46 = @lshift_expr
| 47 = @rshift_expr
/* relational */
| 48 = @lt_expr
| 49 = @gt_expr
| 50 = @le_expr
| 51 = @ge_expr
/* equality */
| 52 = @eq_expr
| 53 = @ne_expr
/* logical */
| 54 = @bit_and_expr
| 55 = @bit_xor_expr
| 56 = @bit_or_expr
| 57 = @log_and_expr
| 58 = @log_or_expr
/* type testing */
| 59 = @is_expr
| 60 = @as_expr
/* null coalescing */
| 61 = @null_coalescing_expr
/* conditional */
| 62 = @conditional_expr
/* assignment */
| 63 = @simple_assign_expr
| 64 = @assign_add_expr
| 65 = @assign_sub_expr
| 66 = @assign_mul_expr
| 67 = @assign_div_expr
| 68 = @assign_rem_expr
| 69 = @assign_and_expr
| 70 = @assign_xor_expr
| 71 = @assign_or_expr
| 72 = @assign_lshift_expr
| 73 = @assign_rshift_expr
/* more */
| 74 = @object_init_expr
| 75 = @collection_init_expr
| 76 = @array_init_expr
| 77 = @checked_expr
| 78 = @unchecked_expr
| 79 = @constructor_init_expr
| 80 = @add_event_expr
| 81 = @remove_event_expr
| 82 = @par_expr
| 83 = @local_var_decl_expr
| 84 = @lambda_expr
| 85 = @anonymous_method_expr
| 86 = @namespace_expr
/* dynamic */
| 92 = @dynamic_element_access_expr
| 93 = @dynamic_member_access_expr
/* unsafe */
| 100 = @pointer_indirection_expr
| 101 = @address_of_expr
| 102 = @sizeof_expr
/* async */
| 103 = @await_expr
/* C# 6.0 */
| 104 = @nameof_expr
| 105 = @interpolated_string_expr
| 106 = @unknown_expr
/* C# 7.0 */
| 107 = @throw_expr
| 108 = @tuple_expr
| 109 = @local_function_invocation_expr
| 110 = @ref_expr
| 111 = @discard_expr
/* C# 8.0 */
| 112 = @range_expr
| 113 = @index_expr
| 114 = @switch_expr
| 115 = @recursive_pattern_expr
| 116 = @property_pattern_expr
| 117 = @positional_pattern_expr
| 118 = @switch_case_expr
| 119 = @assign_coalesce_expr
| 120 = @suppress_nullable_warning_expr
| 121 = @namespace_access_expr
/* C# 9.0 */
| 122 = @lt_pattern_expr
| 123 = @gt_pattern_expr
| 124 = @le_pattern_expr
| 125 = @ge_pattern_expr
| 126 = @not_pattern_expr
| 127 = @and_pattern_expr
| 128 = @or_pattern_expr
| 129 = @function_pointer_invocation_expr
| 130 = @with_expr
/* C# 11.0 */
| 131 = @list_pattern_expr
| 132 = @slice_pattern_expr
| 133 = @urshift_expr
| 134 = @assign_urshift_expr
| 135 = @utf8_string_literal_expr
/* C# 12.0 */
| 136 = @collection_expr
| 137 = @spread_element_expr
| 138 = @interpolated_string_insert_expr
/* Preprocessor */
| 999 = @define_symbol_expr
;

@switch = @switch_stmt | @switch_expr;
@case = @case_stmt | @switch_case_expr;
@pattern_match = @case | @is_expr;
@unary_pattern_expr = @not_pattern_expr;
@relational_pattern_expr = @gt_pattern_expr | @lt_pattern_expr | @ge_pattern_expr | @le_pattern_expr;
@binary_pattern_expr = @and_pattern_expr | @or_pattern_expr;

@integer_literal_expr = @int_literal_expr | @long_literal_expr | @uint_literal_expr | @ulong_literal_expr;
@real_literal_expr = @float_literal_expr | @double_literal_expr | @decimal_literal_expr;
@string_literal_expr = @utf16_string_literal_expr | @utf8_string_literal_expr;
@literal_expr = @bool_literal_expr | @char_literal_expr | @integer_literal_expr | @real_literal_expr
              | @string_literal_expr | @null_literal_expr;

@assign_expr = @simple_assign_expr | @assign_op_expr | @local_var_decl_expr;
@assign_op_expr =  @assign_arith_expr | @assign_bitwise_expr | @assign_event_expr | @assign_coalesce_expr;
@assign_event_expr = @add_event_expr | @remove_event_expr;

@assign_arith_expr = @assign_add_expr | @assign_sub_expr | @assign_mul_expr | @assign_div_expr
                   | @assign_rem_expr
@assign_bitwise_expr = @assign_and_expr | @assign_or_expr | @assign_xor_expr
                   | @assign_lshift_expr | @assign_rshift_expr | @assign_urshift_expr;

@member_access_expr = @field_access_expr | @property_access_expr | @indexer_access_expr | @event_access_expr
                    | @method_access_expr | @type_access_expr | @dynamic_member_access_expr;
@access_expr = @member_access_expr | @this_access_expr | @base_access_expr | @assignable_access_expr | @namespace_access_expr;
@element_access_expr = @indexer_access_expr | @array_access_expr | @dynamic_element_access_expr;

@local_variable_access = @local_variable_access_expr | @local_var_decl_expr;
@local_scope_variable_access_expr = @parameter_access_expr | @local_variable_access;
@variable_access_expr = @local_scope_variable_access_expr | @field_access_expr;

@assignable_access_expr = @variable_access_expr | @property_access_expr | @element_access_expr
                        | @event_access_expr | @dynamic_member_access_expr;

@objectorcollection_init_expr = @object_init_expr | @collection_init_expr;

@delegate_creation_expr = @explicit_delegate_creation_expr | @implicit_delegate_creation_expr;

@bin_arith_op_expr = @mul_expr | @div_expr | @rem_expr | @add_expr | @sub_expr;
@incr_op_expr =  @pre_incr_expr | @post_incr_expr;
@decr_op_expr =  @pre_decr_expr | @post_decr_expr;
@mut_op_expr = @incr_op_expr | @decr_op_expr;
@un_arith_op_expr = @plus_expr | @minus_expr | @mut_op_expr;
@arith_op_expr = @bin_arith_op_expr | @un_arith_op_expr;

@ternary_log_op_expr = @conditional_expr;
@bin_log_op_expr = @log_and_expr | @log_or_expr | @null_coalescing_expr;
@un_log_op_expr = @log_not_expr;
@log_expr = @un_log_op_expr | @bin_log_op_expr | @ternary_log_op_expr;

@bin_bit_op_expr =  @bit_and_expr | @bit_or_expr | @bit_xor_expr | @lshift_expr
                 | @rshift_expr | @urshift_expr;
@un_bit_op_expr = @bit_not_expr;
@bit_expr = @un_bit_op_expr | @bin_bit_op_expr;

@equality_op_expr =  @eq_expr | @ne_expr;
@rel_op_expr = @gt_expr | @lt_expr| @ge_expr | @le_expr;
@comp_expr = @equality_op_expr | @rel_op_expr;

@op_expr = @assign_expr | @un_op | @bin_op | @ternary_op;

@ternary_op = @ternary_log_op_expr;
@bin_op = @bin_arith_op_expr | @bin_log_op_expr | @bin_bit_op_expr | @comp_expr;
@un_op = @un_arith_op_expr | @un_log_op_expr | @un_bit_op_expr | @sizeof_expr
       | @pointer_indirection_expr | @address_of_expr;

@anonymous_function_expr = @lambda_expr | @anonymous_method_expr;

@call = @method_invocation_expr | @constructor_init_expr | @operator_invocation_expr
      | @delegate_invocation_expr | @object_creation_expr | @call_access_expr
      | @local_function_invocation_expr | @function_pointer_invocation_expr;

@call_access_expr = @property_access_expr | @event_access_expr | @indexer_access_expr;

@late_bindable_expr = @dynamic_element_access_expr | @dynamic_member_access_expr
                    | @object_creation_expr | @method_invocation_expr | @operator_invocation_expr;

@throw_element = @throw_expr | @throw_stmt;

@implicitly_typeable_object_creation_expr = @object_creation_expr | @explicit_delegate_creation_expr;

implicitly_typed_array_creation(
  unique int id: @array_creation_expr ref);

explicitly_sized_array_creation(
  unique int id: @array_creation_expr ref);

stackalloc_array_creation(
  unique int id: @array_creation_expr ref);

implicitly_typed_object_creation(
  unique int id: @implicitly_typeable_object_creation_expr ref);

mutator_invocation_mode(
  unique int id: @operator_invocation_expr ref,
  int mode: int ref /* prefix = 1, postfix = 2*/);

expr_value(
  unique int id: @expr ref,
  string value: string ref);

expr_call(
  unique int caller_id: @expr ref,
  int target_id: @callable ref);

expr_access(
  unique int accesser_id: @access_expr ref,
  int target_id: @accessible ref);

@accessible = @method | @assignable | @local_function | @namespace;

expr_location(
  unique int id: @expr ref,
  int loc: @location ref);

dynamic_member_name(
  unique int id: @late_bindable_expr ref,
  string name: string ref);

@qualifiable_expr = @member_access_expr
                  | @method_invocation_expr
                  | @element_access_expr;

conditional_access(
  unique int id: @qualifiable_expr ref);

expr_argument(
  unique int id: @expr ref,
  int mode: int ref);
  /* mode is the same as params: value = 0, ref = 1, out = 2 */

expr_argument_name(
  unique int id: @expr ref,
  string name: string ref);

lambda_expr_return_type(
  unique int id: @lambda_expr ref,
  int type_id: @type_or_ref ref);

/* Compiler generated */

compiler_generated(unique int id: @element ref);

/** CONTROL/DATA FLOW **/

@control_flow_element = @stmt | @expr;

/* XML Files */

xmlEncoding  (
  unique int id: @file ref,
  string encoding: string ref);

xmlDTDs(
  unique int id: @xmldtd,
  string root: string ref,
  string publicId: string ref,
  string systemId: string ref,
  int fileid: @file ref);

xmlElements(
  unique int id: @xmlelement,
  string name: string ref,
  int parentid: @xmlparent ref,
  int idx: int ref,
  int fileid: @file ref);

xmlAttrs(
  unique int id: @xmlattribute,
  int elementid: @xmlelement ref,
  string name: string ref,
  string value: string ref,
  int idx: int ref,
  int fileid: @file ref);

xmlNs(
  int id: @xmlnamespace,
  string prefixName: string ref,
  string URI: string ref,
  int fileid: @file ref);

xmlHasNs(
  int elementId: @xmlnamespaceable ref,
  int nsId: @xmlnamespace ref,
  int fileid: @file ref);

xmlComments(
  unique int id: @xmlcomment,
  string text: string ref,
  int parentid: @xmlparent ref,
  int fileid: @file ref);

xmlChars(
  unique int id: @xmlcharacters,
  string text: string ref,
  int parentid: @xmlparent ref,
  int idx: int ref,
  int isCDATA: int ref,
  int fileid: @file ref);

@xmlparent = @file | @xmlelement;
@xmlnamespaceable = @xmlelement | @xmlattribute;

xmllocations(
  int xmlElement: @xmllocatable ref,
  int location: @location_default ref);

@xmllocatable = @xmlcharacters | @xmlelement | @xmlcomment | @xmlattribute | @xmldtd | @file | @xmlnamespace;

/* Comments */

commentline(
  unique int id: @commentline,
  int kind: int ref,
  string text: string ref,
  string rawtext: string ref);

case @commentline.kind of
  0 = @singlelinecomment
| 1 = @xmldoccomment
| 2 = @multilinecomment;

commentline_location(
  unique int id: @commentline ref,
  int loc: @location ref);

commentblock(
  unique int id : @commentblock);

commentblock_location(
  unique int id: @commentblock ref,
  int loc: @location ref);

commentblock_binding(
  int id: @commentblock ref,
  int entity: @element ref,
  int bindtype: int ref);    /* 0: Parent, 1: Best, 2: Before, 3: After */

commentblock_child(
  int id: @commentblock ref,
  int commentline: @commentline ref,
  int index: int ref);

/* ASP.NET */

case @asp_element.kind of
  0=@asp_close_tag
| 1=@asp_code
| 2=@asp_comment
| 3=@asp_data_binding
| 4=@asp_directive
| 5=@asp_open_tag
| 6=@asp_quoted_string
| 7=@asp_text
| 8=@asp_xml_directive;

@asp_attribute = @asp_code | @asp_data_binding | @asp_quoted_string;

asp_elements(
  unique int id: @asp_element,
  int kind: int ref,
  int loc: @location ref);

asp_comment_server(unique int comment: @asp_comment ref);
asp_code_inline(unique int code: @asp_code ref);
asp_directive_attribute(
  int directive: @asp_directive ref,
  int index: int ref,
  string name: string ref,
  int value: @asp_quoted_string ref);
asp_directive_name(
  unique int directive: @asp_directive ref,
  string name: string ref);
asp_element_body(
  unique int element: @asp_element ref,
  string body: string ref);
asp_tag_attribute(
  int tag: @asp_open_tag ref,
  int index: int ref,
  string name: string ref,
  int attribute: @asp_attribute ref);
asp_tag_name(
  unique int tag: @asp_open_tag ref,
  string name: string ref);
asp_tag_isempty(int tag: @asp_open_tag ref);
