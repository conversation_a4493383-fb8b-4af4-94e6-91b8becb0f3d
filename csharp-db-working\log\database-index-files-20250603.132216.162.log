[2025-06-03 13:22:16] This is codeql database index-files --include-extension=.config --include-extension=.csproj --include-extension=.props --include-extension=.xml --size-limit 10m --language xml --working-dir=. -- E:\advance_javascript\codeQL\7\csharp-db-working
[2025-06-03 13:22:16] Log file was started late.
[2025-06-03 13:22:16] Using index-files script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\xml\tools\index-files.cmd.
[2025-06-03 13:22:16] [PROGRESS] database index-files> Scanning for files in E:\advance_javascript\codeQL\7\CSharpTest...
[2025-06-03 13:22:16] Calling plumbing command: codeql resolve files --include-extension=.config --include-extension=.csproj --include-extension=.props --include-extension=.xml --size-limit=10m E:\advance_javascript\codeQL\7\CSharpTest --format=json
[2025-06-03 13:22:16] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\CSharpTest...
[2025-06-03 13:22:16] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\CSharpTest\bin...
[2025-06-03 13:22:16] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\CSharpTest\bin\Debug...
[2025-06-03 13:22:16] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\CSharpTest\bin\Debug\net8.0...
[2025-06-03 13:22:16] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\CSharpTest\obj...
[2025-06-03 13:22:16] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug...
[2025-06-03 13:22:16] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0...
[2025-06-03 13:22:16] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\generated...
[2025-06-03 13:22:16] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\ref...
[2025-06-03 13:22:16] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\CSharpTest\obj\Debug\net8.0\refint...
[2025-06-03 13:22:16] Plumbing command codeql resolve files completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\CSharpTest\\CSharpTest.csproj",
                        "E:\\advance_javascript\\codeQL\\7\\CSharpTest\\obj\\CSharpTest.csproj.nuget.g.props"
                      ]
[2025-06-03 13:22:16] [DETAILS] database index-files> Found 2 files.
[2025-06-03 13:22:16] [PROGRESS] database index-files> E:\advance_javascript\codeQL\7\csharp-db-working: Indexing files in in E:\advance_javascript\codeQL\7\CSharpTest...
[2025-06-03 13:22:16] Using index-files script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\xml\tools\index-files.cmd.
[2025-06-03 13:22:16] [PROGRESS] database index-files> Running command in E:\advance_javascript\codeQL\7\CSharpTest: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\xml\tools\index-files.cmd, E:\advance_javascript\codeQL\7\csharp-db-working\working\files-to-index1204049603866596320.list]
[2025-06-03 13:22:17] Terminating normally.
