import requests
import mysql.connector

# Hardcoded password vulnerabilities
database_password = "mySecretPassword123"
admin_password = "admin123"
api_key = "sk-1234567890abcdef"
secret_token = "ghp_xxxxxxxxxxxxxxxxxxxx"
db_passwd = "root123"

# Configuration with hardcoded credentials
DATABASE_CONFIG = {
    'host': 'localhost',
    'user': 'admin',
    'password': 'hardcoded_password',  # Vulnerable
    'database': 'myapp'
}

# API configuration
API_SECRET_KEY = "very-secret-key-12345"
JWT_SECRET = "jwt-signing-secret"

# Safe examples (should not be flagged)
password_prompt = "Enter your password:"
password_field = ""
user_password = input("Password: ")

def connect_to_database():
    # Using hardcoded password in function
    connection = mysql.connector.connect(
        host='localhost',
        user='root',
        password='mysql_root_password',  # Vulnerable
        database='testdb'
    )
    return connection

def make_api_call():
    headers = {
        'Authorization': f'Bearer {secret_token}',
        'X-API-Key': 'hardcoded-api-key-value'  # Vulnerable
    }
    response = requests.get('https://api.example.com/data', headers=headers)
    return response.json()

# More examples
ssh_password = "ssh123!"
ftp_pwd = "ftppass"
encryption_key = "32-char-encryption-key-here!!"
