/**
 * @name Hardcoded credentials in C#
 * @description Detects hardcoded passwords, API keys, and secrets in C# code.
 * @kind problem
 * @problem.severity error
 * @id csharp/hardcoded-credentials
 */

import csharp

from VariableDeclarator decl, StringLiteral value, string varName
where
  decl.getVariable().getName() = varName and
  decl.getInitializer() = value and
  (
    varName.toLowerCase().matches("%password%") or
    varName.toLowerCase().matches("%passwd%") or
    varName.toLowerCase().matches("%pwd%") or
    varName.toLowerCase().matches("%secret%") or
    varName.toLowerCase().matches("%key%") or
    varName.toLowerCase().matches("%token%") or
    varName.toLowerCase().matches("%connectionstring%") or
    varName.toLowerCase().matches("%apikey%")
  ) and
  value.getValue().length() > 5 and
  // Exclude obvious non-credentials
  not value.getValue().toLowerCase().matches("%enter%") and
  not value.getValue().toLowerCase().matches("%input%") and
  not value.getValue().toLowerCase().matches("%prompt%") and
  not value.getValue() = "" and
  not value.getValue().matches("%:%") and
  not value.getValue().matches("%?%")
select decl, "Hardcoded credential found in variable '" + varName + "': " + value.getValue()
