/**
 * @name Hardcoded credentials in C#
 * @description Detects hardcoded passwords, API keys, and secrets in C# code.
 * @kind problem
 * @problem.severity error
 * @id csharp/hardcoded-credentials
 */

import csharp

from Variable var, StringLiteral value
where
  var.getAnAssignedValue() = value and
  (
    var.getName().toLowerCase().matches("%password%") or
    var.getName().toLowerCase().matches("%passwd%") or
    var.getName().toLowerCase().matches("%pwd%") or
    var.getName().toLowerCase().matches("%secret%") or
    var.getName().toLowerCase().matches("%key%") or
    var.getName().toLowerCase().matches("%token%") or
    var.getName().toLowerCase().matches("%connectionstring%") or
    var.getName().toLowerCase().matches("%apikey%")
  ) and
  value.getValue().length() > 5 and
  // Exclude obvious non-credentials
  not value.getValue().toLowerCase().matches("%enter%") and
  not value.getValue().toLowerCase().matches("%input%") and
  not value.getValue().toLowerCase().matches("%prompt%") and
  not value.getValue() = "" and
  not value.getValue().matches("%:%") and
  not value.getValue().matches("%?%")
select var, "Hardcoded credential found in variable '" + var.getName() + "': " + value.getValue()
