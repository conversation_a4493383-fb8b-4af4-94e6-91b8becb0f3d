"Hardcoded credentials in C#","Detects hardcoded passwords, API keys, and secrets in C# code.","error","Hardcoded credential found in variable 'jwt_secret': my-jwt-secret-key","/Program.cs","20","16","20","25"
"Hardcoded credentials in C#","Detects hardcoded passwords, API keys, and secrets in C# code.","error","Hardcoded credential found in variable 'encryption_key': 32-char-encryption-key-value!!","/Program.cs","21","16","21","29"
"Hardcoded credentials in C#","Detects hardcoded passwords, API keys, and secrets in C# code.","error","Hardcoded credential found in variable 'password': MySecretPassword123","/Program.cs","6","27","6","34"
"Hardcoded credentials in C#","Detects hardcoded passwords, API keys, and secrets in C# code.","error","Hardcoded credential found in variable 'apiKey': sk-1234567890abcdef","/Program.cs","7","27","7","32"
"Hardcoded credentials in C#","Detects hardcoded passwords, API keys, and secrets in C# code.","error","Hardcoded credential found in variable 'secret_token': ghp_xxxxxxxxxxxxxxxxxxxx","/Program.cs","8","27","8","38"
