[2025-06-03 10:36:13] This is codeql database index-files --include-extension=.yaml --include-extension=.yml --size-limit=5m --language yaml -- E:\advance_javascript\codeQL\7\python-db
[2025-06-03 10:36:13] Log file was started late.
[2025-06-03 10:36:13] Using index-files script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\tools\index-files.cmd.
[2025-06-03 10:36:13] [PROGRESS] database index-files> Scanning for files in E:\advance_javascript\codeQL\7...
[2025-06-03 10:36:13] Calling plumbing command: codeql resolve files --include-extension=.yaml --include-extension=.yml --size-limit=5m E:\advance_javascript\codeQL\7 --format=json
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\diagnostic...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\diagnostic\extractors...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\diagnostic\extractors\python...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\diagnostic\tracer...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\log...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\src...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\src\E_...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\src\E_\advance_javascript...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\src\E_\advance_javascript\codeQL...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\src\E_\advance_javascript\codeQL\7...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\trap...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\trap\python...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\working...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\working\trap_cache...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\python-db\working\trap_cache\1...
[2025-06-03 10:36:13] [PROGRESS] resolve files> Scanning E:\advance_javascript\codeQL\7\queries...
[2025-06-03 10:36:13] Plumbing command codeql resolve files completed:
                      [
                        "E:\\advance_javascript\\codeQL\\7\\python-db\\codeql-database.yml"
                      ]
[2025-06-03 10:36:13] [DETAILS] database index-files> Found 1 files.
[2025-06-03 10:36:13] [PROGRESS] database index-files> E:\advance_javascript\codeQL\7\python-db: Indexing files in in E:\advance_javascript\codeQL\7...
[2025-06-03 10:36:13] Using index-files script C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\tools\index-files.cmd.
[2025-06-03 10:36:13] [PROGRESS] database index-files> Running command in E:\advance_javascript\codeQL\7: [C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\yaml\tools\index-files.cmd, E:\advance_javascript\codeQL\7\python-db\working\files-to-index8766245005983289623.list]
[2025-06-03 10:36:14] Terminating normally.
