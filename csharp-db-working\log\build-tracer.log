[T 07:51:15 8628] Initializing tracer.
[T 07:51:15 8628] Initializing tags.
[T 07:51:15 8628] CodeQL CLI version 2.21.3
[T 07:51:15 8628] Initializing tracer.
[T 07:51:15 8628] Initializing tags.
[T 07:51:15 8628] Allocated ID 01BC6FC4000021B4_0000000000000001 (parent )
[T 07:51:15 8628] ==== Candidate to intercept: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\runner.exe (28532) ====
[T 07:51:15 8628] Executing the following tracer actions:
[T 07:51:15 8628] Tracer actions:
[T 07:51:15 8628] pre_invocations(0)
[T 07:51:15 8628] post_invocations(0)
[T 07:51:15 8628] trace_languages(1): [csharp]
[T 07:51:15 28532] Initializing tracer.
[T 07:51:15 28532] Initializing tags.
[T 07:51:15 28532] ID set to 01BC6FC4000021B4_0000000000000001 (parent root)
[T 07:51:15 28532] Allocated ID 8EF3463B00006F74_0000000000000001 (parent 01BC6FC4000021B4_0000000000000001)
[T 07:51:15 28532] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (16688) ====
[T 07:51:15 28532] Executing the following tracer actions:
[T 07:51:15 28532] Tracer actions:
[T 07:51:15 28532] pre_invocations(0)
[T 07:51:15 28532] post_invocations(0)
[T 07:51:15 28532] trace_languages(1): [csharp]
[T 07:51:15 16688] Initializing tracer.
[T 07:51:15 16688] Initializing tags.
[T 07:51:15 16688] ID set to 8EF3463B00006F74_0000000000000001 (parent 01BC6FC4000021B4_0000000000000001)
[T 07:51:15 16688] Allocated ID FEDBF49700004130_0000000000000001 (parent 8EF3463B00006F74_0000000000000001)
[T 07:51:15 16688] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (28928) ====
[T 07:51:15 16688] Lua: Execute a .NET SDK command usage detected
[T 07:51:15 16688] Lua: Dotnet subcommand detected: build
[T 07:51:15 16688] Lua: === Intercepted call to c:\program files\dotnet\dotnet.exe ===
[T 07:51:15 16688] Executing the following tracer actions:
[T 07:51:15 16688] Tracer actions:
[T 07:51:15 16688] pre_invocations(0)
[T 07:51:15 16688] replacing compiler with:
[T 07:51:15 16688] invocation: c:\program files\dotnet\dotnet.exe, args: build CSharpTest -p:UseSharedCompilation=false -p:EmitCompilerGeneratedFiles=true
[T 07:51:15 16688] post_invocations(0)
[T 07:51:15 16688] trace_languages(1): [csharp]
[T 07:51:15 12868] Initializing tracer.
[T 07:51:15 12868] Initializing tags.
[T 07:51:15 12868] ID set to FEDBF49700004130_0000000000000001 (parent 8EF3463B00006F74_0000000000000001)
