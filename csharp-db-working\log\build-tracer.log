[T 07:51:15 8628] Initializing tracer.
[T 07:51:15 8628] Initializing tags.
[T 07:51:15 8628] CodeQL CLI version 2.21.3
[T 07:51:15 8628] Initializing tracer.
[T 07:51:15 8628] Initializing tags.
[T 07:51:15 8628] Allocated ID 01BC6FC4000021B4_0000000000000001 (parent )
[T 07:51:15 8628] ==== Candidate to intercept: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\runner.exe (28532) ====
[T 07:51:15 8628] Executing the following tracer actions:
[T 07:51:15 8628] Tracer actions:
[T 07:51:15 8628] pre_invocations(0)
[T 07:51:15 8628] post_invocations(0)
[T 07:51:15 8628] trace_languages(1): [csharp]
[T 07:51:15 28532] Initializing tracer.
[T 07:51:15 28532] Initializing tags.
[T 07:51:15 28532] ID set to 01BC6FC4000021B4_0000000000000001 (parent root)
[T 07:51:15 28532] Allocated ID 8EF3463B00006F74_0000000000000001 (parent 01BC6FC4000021B4_0000000000000001)
[T 07:51:15 28532] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (16688) ====
[T 07:51:15 28532] Executing the following tracer actions:
[T 07:51:15 28532] Tracer actions:
[T 07:51:15 28532] pre_invocations(0)
[T 07:51:15 28532] post_invocations(0)
[T 07:51:15 28532] trace_languages(1): [csharp]
[T 07:51:15 16688] Initializing tracer.
[T 07:51:15 16688] Initializing tags.
[T 07:51:15 16688] ID set to 8EF3463B00006F74_0000000000000001 (parent 01BC6FC4000021B4_0000000000000001)
[T 07:51:15 16688] Allocated ID FEDBF49700004130_0000000000000001 (parent 8EF3463B00006F74_0000000000000001)
[T 07:51:15 16688] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (28928) ====
[T 07:51:15 16688] Lua: Execute a .NET SDK command usage detected
[T 07:51:15 16688] Lua: Dotnet subcommand detected: build
[T 07:51:15 16688] Lua: === Intercepted call to c:\program files\dotnet\dotnet.exe ===
[T 07:51:15 16688] Executing the following tracer actions:
[T 07:51:15 16688] Tracer actions:
[T 07:51:15 16688] pre_invocations(0)
[T 07:51:15 16688] replacing compiler with:
[T 07:51:15 16688] invocation: c:\program files\dotnet\dotnet.exe, args: build CSharpTest -p:UseSharedCompilation=false -p:EmitCompilerGeneratedFiles=true
[T 07:51:15 16688] post_invocations(0)
[T 07:51:15 16688] trace_languages(1): [csharp]
[T 07:51:15 12868] Initializing tracer.
[T 07:51:15 12868] Initializing tags.
[T 07:51:15 12868] ID set to FEDBF49700004130_0000000000000001 (parent 8EF3463B00006F74_0000000000000001)
[T 07:51:41 4416] Initializing tracer.
[T 07:51:41 4416] Initializing tags.
[T 07:51:41 4416] CodeQL CLI version 2.21.3
[T 07:51:41 4416] Initializing tracer.
[T 07:51:41 4416] Initializing tags.
[T 07:51:41 4416] Allocated ID 2D77AD6600001140_0000000000000001 (parent )
[T 07:51:41 4416] ==== Candidate to intercept: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\runner.exe (19320) ====
[T 07:51:41 4416] Executing the following tracer actions:
[T 07:51:41 4416] Tracer actions:
[T 07:51:41 4416] pre_invocations(0)
[T 07:51:41 4416] post_invocations(0)
[T 07:51:41 4416] trace_languages(1): [csharp]
[T 07:51:41 19320] Initializing tracer.
[T 07:51:41 19320] Initializing tags.
[T 07:51:41 19320] ID set to 2D77AD6600001140_0000000000000001 (parent root)
[T 07:51:41 19320] Allocated ID 3809FB0400004B78_0000000000000001 (parent 2D77AD6600001140_0000000000000001)
[T 07:51:41 19320] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (29292) ====
[T 07:51:41 19320] Executing the following tracer actions:
[T 07:51:41 19320] Tracer actions:
[T 07:51:41 19320] pre_invocations(0)
[T 07:51:41 19320] post_invocations(0)
[T 07:51:41 19320] trace_languages(1): [csharp]
[T 07:51:41 29292] Initializing tracer.
[T 07:51:41 29292] Initializing tags.
[T 07:51:41 29292] ID set to 3809FB0400004B78_0000000000000001 (parent 2D77AD6600001140_0000000000000001)
[T 07:51:41 29292] Allocated ID 640BED4A0000726C_0000000000000001 (parent 3809FB0400004B78_0000000000000001)
[T 07:51:41 29292] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (15080) ====
[T 07:51:41 29292] Lua: Execute a .NET SDK command usage detected
[T 07:51:41 29292] Lua: Dotnet subcommand detected: build
[T 07:51:41 29292] Lua: === Intercepted call to c:\program files\dotnet\dotnet.exe ===
[T 07:51:41 29292] Executing the following tracer actions:
[T 07:51:41 29292] Tracer actions:
[T 07:51:41 29292] pre_invocations(0)
[T 07:51:41 29292] replacing compiler with:
[T 07:51:41 29292] invocation: c:\program files\dotnet\dotnet.exe, args: build -p:UseSharedCompilation=false -p:EmitCompilerGeneratedFiles=true
[T 07:51:41 29292] post_invocations(0)
[T 07:51:41 29292] trace_languages(1): [csharp]
[T 07:51:41 20916] Initializing tracer.
[T 07:51:41 20916] Initializing tags.
[T 07:51:41 20916] ID set to 640BED4A0000726C_0000000000000001 (parent 3809FB0400004B78_0000000000000001)
[T 07:51:53 18356] Initializing tracer.
[T 07:51:53 18356] Initializing tags.
[T 07:51:53 18356] CodeQL CLI version 2.21.3
[T 07:51:53 18356] Initializing tracer.
[T 07:51:53 18356] Initializing tags.
[T 07:51:53 18356] Allocated ID 67DE8488000047B4_0000000000000001 (parent )
[T 07:51:53 18356] ==== Candidate to intercept: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\tools\win64\runner.exe (13652) ====
[T 07:51:53 18356] Executing the following tracer actions:
[T 07:51:53 18356] Tracer actions:
[T 07:51:53 18356] pre_invocations(0)
[T 07:51:53 18356] post_invocations(0)
[T 07:51:53 18356] trace_languages(1): [csharp]
[T 07:51:53 13652] Initializing tracer.
[T 07:51:53 13652] Initializing tags.
[T 07:51:53 13652] ID set to 67DE8488000047B4_0000000000000001 (parent root)
[T 07:51:54 13652] Allocated ID 68CB0F1F00003554_0000000000000001 (parent 67DE8488000047B4_0000000000000001)
[T 07:51:54 13652] ==== Candidate to intercept: C:\Windows\System32\cmd.exe (21108) ====
[T 07:51:54 13652] Executing the following tracer actions:
[T 07:51:54 13652] Tracer actions:
[T 07:51:54 13652] pre_invocations(0)
[T 07:51:54 13652] post_invocations(0)
[T 07:51:54 13652] trace_languages(1): [csharp]
[T 07:51:54 21108] Initializing tracer.
[T 07:51:54 21108] Initializing tags.
[T 07:51:54 21108] ID set to 68CB0F1F00003554_0000000000000001 (parent 67DE8488000047B4_0000000000000001)
[T 07:51:54 21108] Allocated ID 41BBD8EB00005274_0000000000000001 (parent 68CB0F1F00003554_0000000000000001)
[T 07:51:54 21108] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (18968) ====
[T 07:51:54 21108] Lua: Execute a .NET SDK command usage detected
[T 07:51:54 21108] Lua: Dotnet subcommand detected: clean
[T 07:51:54 21108] Executing the following tracer actions:
[T 07:51:54 21108] Tracer actions:
[T 07:51:54 21108] pre_invocations(0)
[T 07:51:54 21108] post_invocations(0)
[T 07:51:54 21108] trace_languages(1): [csharp]
[T 07:51:54 18968] Initializing tracer.
[T 07:51:54 18968] Initializing tags.
[T 07:51:54 18968] ID set to 41BBD8EB00005274_0000000000000001 (parent 68CB0F1F00003554_0000000000000001)
[T 07:51:54 21108] Allocated ID 2E3B909C00005274_0000000000000001 (parent 68CB0F1F00003554_0000000000000001)
[T 07:51:54 21108] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (5820) ====
[T 07:51:54 21108] Lua: Execute a .NET SDK command usage detected
[T 07:51:54 21108] Lua: Dotnet subcommand detected: build
[T 07:51:54 21108] Lua: === Intercepted call to c:\program files\dotnet\dotnet.exe ===
[T 07:51:54 21108] Executing the following tracer actions:
[T 07:51:54 21108] Tracer actions:
[T 07:51:54 21108] pre_invocations(0)
[T 07:51:54 21108] replacing compiler with:
[T 07:51:54 21108] invocation: c:\program files\dotnet\dotnet.exe, args: build -p:UseSharedCompilation=false -p:EmitCompilerGeneratedFiles=true
[T 07:51:54 21108] post_invocations(0)
[T 07:51:54 21108] trace_languages(1): [csharp]
[T 07:51:54 5992] Initializing tracer.
[T 07:51:54 5992] Initializing tags.
[T 07:51:54 5992] ID set to 2E3B909C00005274_0000000000000001 (parent 68CB0F1F00003554_0000000000000001)
[T 07:51:56 5992] Allocated ID 5FED6EF300001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:56 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (19620) ====
[T 07:51:56 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:56 5992] Lua: Dotnet subcommand detected: exec
[T 07:51:56 5992] Lua: Execute a .NET application usage detected
[T 07:51:56 5992] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\Roslyn\bincore\csc.dll
[T 07:51:56 5992] Executing the following tracer actions:
[T 07:51:56 5992] Tracer actions:
[T 07:51:56 5992] pre_invocations(0)
[T 07:51:56 5992] post_invocations(1)
[T 07:51:56 5992] invocation: C:\Users\<USER>\Downloads\codeql-bundle-win64\codeql\csharp\tools\win64\Semmle.Extraction.CSharp.Driver.exe, args: "--compiler" "c:\program files\dotnet\sdk\8.0.410\Roslyn\bincore\csc.dll" "/noconfig" "@C:\Users\<USER>\AppData\Local\Temp\MSBuildTemp\tmp0066f6383b4544a5a5312516bac83c6e.rsp"
[T 07:51:56 5992] trace_languages(1): [csharp]
[T 07:51:57 5992] Allocated ID 59743DE100001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (19204) ====
[T 07:51:57 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:57 5992] Executing the following tracer actions:
[T 07:51:57 5992] Tracer actions:
[T 07:51:57 5992] pre_invocations(0)
[T 07:51:57 5992] post_invocations(0)
[T 07:51:57 5992] trace_languages(1): [csharp]
[T 07:51:57 9048] Initializing tracer.
[T 07:51:57 9048] Initializing tags.
[T 07:51:57 9048] ID set to 5FED6EF300001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 5992] Allocated ID B4FECB9400001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 19204] Initializing tracer.
[T 07:51:57 19204] Initializing tags.
[T 07:51:57 19204] ID set to 59743DE100001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (11120) ====
[T 07:51:57 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:57 5992] Executing the following tracer actions:
[T 07:51:57 5992] Tracer actions:
[T 07:51:57 5992] pre_invocations(0)
[T 07:51:57 5992] post_invocations(0)
[T 07:51:57 5992] trace_languages(1): [csharp]
[T 07:51:57 5992] Allocated ID 09AAA48100001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 19620] Initializing tracer.
[T 07:51:57 19620] Initializing tags.
[T 07:51:57 19620] ID set to 5FED6EF300001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 11120] Initializing tracer.
[T 07:51:57 11120] Initializing tags.
[T 07:51:57 11120] ID set to B4FECB9400001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (13392) ====
[T 07:51:57 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:57 5992] Executing the following tracer actions:
[T 07:51:57 5992] Tracer actions:
[T 07:51:57 5992] pre_invocations(0)
[T 07:51:57 5992] post_invocations(0)
[T 07:51:57 5992] trace_languages(1): [csharp]
[T 07:51:57 5992] Allocated ID D1D9B65400001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 13392] Initializing tracer.
[T 07:51:57 13392] Initializing tags.
[T 07:51:57 13392] ID set to 09AAA48100001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (18320) ====
[T 07:51:57 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:57 5992] Executing the following tracer actions:
[T 07:51:57 5992] Tracer actions:
[T 07:51:57 5992] pre_invocations(0)
[T 07:51:57 5992] post_invocations(0)
[T 07:51:57 5992] trace_languages(1): [csharp]
[T 07:51:57 5992] Allocated ID 6403243500001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (24632) ====
[T 07:51:57 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:57 5992] Executing the following tracer actions:
[T 07:51:57 5992] Tracer actions:
[T 07:51:57 5992] pre_invocations(0)
[T 07:51:57 5992] post_invocations(0)
[T 07:51:57 5992] trace_languages(1): [csharp]
[T 07:51:57 18320] Initializing tracer.
[T 07:51:57 18320] Initializing tags.
[T 07:51:57 18320] ID set to D1D9B65400001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 24632] Initializing tracer.
[T 07:51:57 24632] Initializing tags.
[T 07:51:57 24632] ID set to 6403243500001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 19204] Allocated ID F5226CE600004B04_0000000000000001 (parent 59743DE100001768_0000000000000001)
[T 07:51:57 19204] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (2632) ====
[T 07:51:57 19204] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 19204] Lua: Dotnet subcommand detected: exec
[T 07:51:57 19204] Lua: Execute a .NET application usage detected
[T 07:51:57 19204] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:51:57 19204] Executing the following tracer actions:
[T 07:51:57 19204] Tracer actions:
[T 07:51:57 19204] pre_invocations(0)
[T 07:51:57 19204] post_invocations(0)
[T 07:51:57 19204] trace_languages(1): [csharp]
[T 07:51:57 11120] Allocated ID 4D700E3500002B70_0000000000000001 (parent B4FECB9400001768_0000000000000001)
[T 07:51:57 13392] Allocated ID C7EC14E400003450_0000000000000001 (parent 09AAA48100001768_0000000000000001)
[T 07:51:57 11120] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (1644) ====
[T 07:51:57 2632] Initializing tracer.
[T 07:51:57 2632] Initializing tags.
[T 07:51:57 2632] ID set to F5226CE600004B04_0000000000000001 (parent 59743DE100001768_0000000000000001)
[T 07:51:57 11120] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 11120] Lua: Dotnet subcommand detected: exec
[T 07:51:57 11120] Lua: Execute a .NET application usage detected
[T 07:51:57 11120] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:51:57 11120] Executing the following tracer actions:
[T 07:51:57 11120] Tracer actions:
[T 07:51:57 11120] pre_invocations(0)
[T 07:51:57 11120] post_invocations(0)
[T 07:51:57 11120] trace_languages(1): [csharp]
[T 07:51:57 13392] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (23284) ====
[T 07:51:57 13392] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 13392] Lua: Dotnet subcommand detected: exec
[T 07:51:57 13392] Lua: Execute a .NET application usage detected
[T 07:51:57 13392] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:51:57 13392] Executing the following tracer actions:
[T 07:51:57 13392] Tracer actions:
[T 07:51:57 13392] pre_invocations(0)
[T 07:51:57 13392] post_invocations(0)
[T 07:51:57 13392] trace_languages(1): [csharp]
[T 07:51:57 1644] Initializing tracer.
[T 07:51:57 1644] Initializing tags.
[T 07:51:57 1644] ID set to 4D700E3500002B70_0000000000000001 (parent B4FECB9400001768_0000000000000001)
[T 07:51:57 23284] Initializing tracer.
[T 07:51:57 23284] Initializing tags.
[T 07:51:57 23284] ID set to C7EC14E400003450_0000000000000001 (parent 09AAA48100001768_0000000000000001)
[T 07:51:57 24632] Allocated ID 2BB317F000006038_0000000000000001 (parent 6403243500001768_0000000000000001)
[T 07:51:57 5992] Allocated ID A4B74EC700001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 18320] Allocated ID C1F7174600004790_0000000000000001 (parent D1D9B65400001768_0000000000000001)
[T 07:51:57 24632] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (11052) ====
[T 07:51:57 24632] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 24632] Lua: Dotnet subcommand detected: exec
[T 07:51:57 24632] Lua: Execute a .NET application usage detected
[T 07:51:57 24632] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:51:57 24632] Executing the following tracer actions:
[T 07:51:57 24632] Tracer actions:
[T 07:51:57 24632] pre_invocations(0)
[T 07:51:57 24632] post_invocations(0)
[T 07:51:57 24632] trace_languages(1): [csharp]
[T 07:51:57 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (15668) ====
[T 07:51:57 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:57 5992] Executing the following tracer actions:
[T 07:51:57 5992] Tracer actions:
[T 07:51:57 5992] pre_invocations(0)
[T 07:51:57 5992] post_invocations(0)
[T 07:51:57 5992] trace_languages(1): [csharp]
[T 07:51:57 5992] Allocated ID 1612D1D000001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 11052] Initializing tracer.
[T 07:51:57 11052] Initializing tags.
[T 07:51:57 11052] ID set to 2BB317F000006038_0000000000000001 (parent 6403243500001768_0000000000000001)
[T 07:51:57 15668] Initializing tracer.
[T 07:51:57 15668] Initializing tags.
[T 07:51:57 15668] ID set to A4B74EC700001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (1580) ====
[T 07:51:57 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:57 5992] Executing the following tracer actions:
[T 07:51:57 5992] Tracer actions:
[T 07:51:57 5992] pre_invocations(0)
[T 07:51:57 5992] post_invocations(0)
[T 07:51:57 5992] trace_languages(1): [csharp]
[T 07:51:57 18320] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (3712) ====
[T 07:51:57 18320] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 18320] Lua: Dotnet subcommand detected: exec
[T 07:51:57 18320] Lua: Execute a .NET application usage detected
[T 07:51:57 18320] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:51:57 18320] Executing the following tracer actions:
[T 07:51:57 18320] Tracer actions:
[T 07:51:57 18320] pre_invocations(0)
[T 07:51:57 18320] post_invocations(0)
[T 07:51:57 18320] trace_languages(1): [csharp]
[T 07:51:57 5992] Allocated ID 9395935600001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 1580] Initializing tracer.
[T 07:51:57 1580] Initializing tags.
[T 07:51:57 1580] ID set to 1612D1D000001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:57 3712] Initializing tracer.
[T 07:51:57 3712] Initializing tags.
[T 07:51:57 3712] ID set to C1F7174600004790_0000000000000001 (parent D1D9B65400001768_0000000000000001)
[T 07:51:57 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (27048) ====
[T 07:51:57 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:57 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:57 5992] Executing the following tracer actions:
[T 07:51:57 5992] Tracer actions:
[T 07:51:57 5992] pre_invocations(0)
[T 07:51:57 5992] post_invocations(0)
[T 07:51:57 5992] trace_languages(1): [csharp]
[T 07:51:57 5992] Allocated ID B8F9138900001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:58 27048] Initializing tracer.
[T 07:51:58 27048] Initializing tags.
[T 07:51:58 27048] ID set to 9395935600001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:58 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (3056) ====
[T 07:51:58 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:58 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:58 5992] Executing the following tracer actions:
[T 07:51:58 5992] Tracer actions:
[T 07:51:58 5992] pre_invocations(0)
[T 07:51:58 5992] post_invocations(0)
[T 07:51:58 5992] trace_languages(1): [csharp]
[T 07:51:58 5992] Allocated ID CB4AEEC100001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:58 3056] Initializing tracer.
[T 07:51:58 3056] Initializing tags.
[T 07:51:58 3056] ID set to B8F9138900001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:58 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (19612) ====
[T 07:51:58 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:58 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:58 5992] Executing the following tracer actions:
[T 07:51:58 5992] Tracer actions:
[T 07:51:58 5992] pre_invocations(0)
[T 07:51:58 5992] post_invocations(0)
[T 07:51:58 5992] trace_languages(1): [csharp]
[T 07:51:58 5992] Allocated ID 49169DB400001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:58 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (14680) ====
[T 07:51:58 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:58 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:58 5992] Executing the following tracer actions:
[T 07:51:58 5992] Tracer actions:
[T 07:51:58 5992] pre_invocations(0)
[T 07:51:58 5992] post_invocations(0)
[T 07:51:58 5992] trace_languages(1): [csharp]
[T 07:51:58 5992] Allocated ID 57A1FFD300001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:58 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (2152) ====
[T 07:51:58 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:58 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:58 5992] Executing the following tracer actions:
[T 07:51:58 5992] Tracer actions:
[T 07:51:58 5992] pre_invocations(0)
[T 07:51:58 5992] post_invocations(0)
[T 07:51:58 5992] trace_languages(1): [csharp]
[T 07:51:58 5992] Allocated ID 973D5FFB00001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:58 19612] Initializing tracer.
[T 07:51:58 19612] Initializing tags.
[T 07:51:58 19612] ID set to CB4AEEC100001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:58 15668] Allocated ID 47D898FB00003D34_0000000000000001 (parent A4B74EC700001768_0000000000000001)
[T 07:51:58 5992] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (25428) ====
[T 07:51:58 5992] Lua: Execute a .NET SDK command usage detected
[T 07:51:58 5992] Lua: Dotnet subcommand detected: nuget
[T 07:51:58 5992] Executing the following tracer actions:
[T 07:51:58 5992] Tracer actions:
[T 07:51:58 5992] pre_invocations(0)
[T 07:51:58 5992] post_invocations(0)
[T 07:51:58 5992] trace_languages(1): [csharp]
[T 07:51:58 2152] Initializing tracer.
[T 07:51:58 2152] Initializing tags.
[T 07:51:58 2152] ID set to 57A1FFD300001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:58 15668] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (18124) ====
[T 07:51:58 15668] Lua: Execute a .NET SDK command usage detected
[T 07:51:58 15668] Lua: Dotnet subcommand detected: exec
[T 07:51:58 15668] Lua: Execute a .NET application usage detected
[T 07:51:58 15668] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:51:58 15668] Executing the following tracer actions:
[T 07:51:58 15668] Tracer actions:
[T 07:51:58 15668] pre_invocations(0)
[T 07:51:58 15668] post_invocations(0)
[T 07:51:58 15668] trace_languages(1): [csharp]
[T 07:51:58 14680] Initializing tracer.
[T 07:51:58 14680] Initializing tags.
[T 07:51:58 14680] ID set to 49169DB400001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:58 1580] Allocated ID 6648C4730000062C_0000000000000001 (parent 1612D1D000001768_0000000000000001)
[T 07:51:58 25428] Initializing tracer.
[T 07:51:58 25428] Initializing tags.
[T 07:51:58 25428] ID set to 973D5FFB00001768_0000000000000001 (parent 2E3B909C00005274_0000000000000001)
[T 07:51:58 1580] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (13496) ====
[T 07:51:59 3056] Allocated ID 80C05FB600000BF0_0000000000000001 (parent B8F9138900001768_0000000000000001)
[T 07:51:59 1580] Lua: Execute a .NET SDK command usage detected
[T 07:51:59 1580] Lua: Dotnet subcommand detected: exec
[T 07:51:59 1580] Lua: Execute a .NET application usage detected
[T 07:51:59 1580] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:51:59 1580] Executing the following tracer actions:
[T 07:51:59 1580] Tracer actions:
[T 07:51:59 1580] pre_invocations(0)
[T 07:51:59 1580] post_invocations(0)
[T 07:51:59 1580] trace_languages(1): [csharp]
[T 07:51:59 18124] Initializing tracer.
[T 07:51:59 18124] Initializing tags.
[T 07:51:59 18124] ID set to 47D898FB00003D34_0000000000000001 (parent A4B74EC700001768_0000000000000001)
[T 07:51:59 3056] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (14252) ====
[T 07:51:59 27048] Allocated ID FCE5A699000069A8_0000000000000001 (parent 9395935600001768_0000000000000001)
[T 07:51:59 3056] Lua: Execute a .NET SDK command usage detected
[T 07:51:59 3056] Lua: Dotnet subcommand detected: exec
[T 07:51:59 3056] Lua: Execute a .NET application usage detected
[T 07:51:59 3056] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:51:59 3056] Executing the following tracer actions:
[T 07:51:59 3056] Tracer actions:
[T 07:51:59 3056] pre_invocations(0)
[T 07:51:59 3056] post_invocations(0)
[T 07:51:59 3056] trace_languages(1): [csharp]
[T 07:51:59 27048] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (16872) ====
[T 07:51:59 27048] Lua: Execute a .NET SDK command usage detected
[T 07:51:59 27048] Lua: Dotnet subcommand detected: exec
[T 07:51:59 27048] Lua: Execute a .NET application usage detected
[T 07:51:59 27048] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:51:59 27048] Executing the following tracer actions:
[T 07:51:59 27048] Tracer actions:
[T 07:51:59 27048] pre_invocations(0)
[T 07:51:59 27048] post_invocations(0)
[T 07:51:59 27048] trace_languages(1): [csharp]
[T 07:51:59 14252] Initializing tracer.
[T 07:51:59 16872] Initializing tags.
[T 07:51:59 13496] ID set to 6648C4730000062C_0000000000000001 (parent 1612D1D000001768_0000000000000001)
[T 07:51:59 16872] ID set to FCE5A699000069A8_0000000000000001 (parent 9395935600001768_0000000000000001)
[T 07:51:59 19612] Allocated ID 9C435EBE00004C9C_0000000000000001 (parent CB4AEEC100001768_0000000000000001)
[T 07:51:59 2152] Allocated ID DCE13FB400000868_0000000000000001 (parent 57A1FFD300001768_0000000000000001)
[T 07:51:59 25428] Allocated ID 648C534100006354_0000000000000001 (parent 973D5FFB00001768_0000000000000001)
[T 07:51:59 14680] Allocated ID C90697B400003958_0000000000000001 (parent 49169DB400001768_0000000000000001)
[T 07:52:00 19612] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (18140) ====
[T 07:52:00 19612] Lua: Execute a .NET SDK command usage detected
[T 07:52:00 19612] Lua: Dotnet subcommand detected: exec
[T 07:52:00 19612] Lua: Execute a .NET application usage detected
[T 07:52:00 19612] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:52:00 19612] Executing the following tracer actions:
[T 07:52:00 19612] Tracer actions:
[T 07:52:00 19612] pre_invocations(0)
[T 07:52:00 19612] post_invocations(0)
[T 07:52:00 19612] trace_languages(1): [csharp]
[T 07:52:00 2152] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (22612) ====
[T 07:52:00 2152] Lua: Execute a .NET SDK command usage detected
[T 07:52:00 2152] Lua: Dotnet subcommand detected: exec
[T 07:52:00 2152] Lua: Execute a .NET application usage detected
[T 07:52:00 2152] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:52:00 2152] Executing the following tracer actions:
[T 07:52:00 2152] Tracer actions:
[T 07:52:00 2152] pre_invocations(0)
[T 07:52:00 2152] post_invocations(0)
[T 07:52:00 2152] trace_languages(1): [csharp]
[T 07:52:00 25428] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (18976) ====
[T 07:52:00 25428] Lua: Execute a .NET SDK command usage detected
[T 07:52:00 25428] Lua: Dotnet subcommand detected: exec
[T 07:52:00 25428] Lua: Execute a .NET application usage detected
[T 07:52:00 25428] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:52:00 25428] Executing the following tracer actions:
[T 07:52:00 25428] Tracer actions:
[T 07:52:00 25428] pre_invocations(0)
[T 07:52:00 25428] post_invocations(0)
[T 07:52:00 25428] trace_languages(1): [csharp]
[T 07:52:00 14680] ==== Candidate to intercept: C:\Program Files\dotnet\dotnet.exe (19752) ====
[T 07:52:00 14680] Lua: Execute a .NET SDK command usage detected
[T 07:52:00 14680] Lua: Dotnet subcommand detected: exec
[T 07:52:00 14680] Lua: Execute a .NET application usage detected
[T 07:52:00 14680] Lua: Dotnet path-to-application detected: c:\program files\dotnet\sdk\8.0.410\NuGet.CommandLine.XPlat.dll
[T 07:52:00 14680] Executing the following tracer actions:
[T 07:52:00 14680] Tracer actions:
[T 07:52:00 14680] pre_invocations(0)
[T 07:52:00 14680] post_invocations(0)
[T 07:52:00 14680] trace_languages(1): [csharp]
[T 07:52:00 22612] Initializing tracer.
[T 07:52:00 22612] Initializing tags.
[T 07:52:00 22612] ID set to DCE13FB400000868_0000000000000001 (parent 57A1FFD300001768_0000000000000001)
[T 07:52:00 18976] Initializing tracer.
[T 07:52:00 18976] Initializing tags.
[T 07:52:00 18976] ID set to 648C534100006354_0000000000000001 (parent 973D5FFB00001768_0000000000000001)
[T 07:52:00 19752] Initializing tracer.
[T 07:52:00 19752] Initializing tags.
[T 07:52:00 19752] ID set to C90697B400003958_0000000000000001 (parent 49169DB400001768_0000000000000001)
[T 07:52:00 18140] Initializing tracer.
[T 07:52:00 18140] Initializing tags.
[T 07:52:00 18140] ID set to 9C435EBE00004C9C_0000000000000001 (parent CB4AEEC100001768_0000000000000001)
