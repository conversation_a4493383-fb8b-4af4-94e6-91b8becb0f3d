/**
 * @name Custom Hacker Paradise Detection
 * @description Detects the specific vulnerability pattern "I am hacker's paradise" in JavaScript/Node.js code
 * @kind problem
 * @problem.severity error
 * @id js/custom-hacker-paradise
 */

import javascript

from StringLiteral str
where
  str.getValue() = "I am hacker's paradise"
select str, "Detected custom vulnerability pattern: 'I am hacker's paradise' - this indicates a security backdoor or test vulnerability."
