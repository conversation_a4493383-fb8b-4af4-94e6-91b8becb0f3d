[2025-06-03 12:46:10] This is codeql database create csharp-db --language=csharp --source-root=CSharpTest --command=dotnet build CSharpTest --overwrite
[2025-06-03 12:46:10] Log file was started late.
[2025-06-03 12:46:10] [PROGRESS] database create> Initializing database at E:\advance_javascript\codeQL\7\csharp-queries\csharp-db.
[2025-06-03 12:46:10] Running plumbing command: codeql database init --overwrite --language=csharp --extractor-options-verbosity=1 --qlconfig-file=E:\advance_javascript\codeQL\7\csharp-queries\qlconfig.yml --source-root=E:\advance_javascript\codeQL\7\csharp-queries\CSharpTest --allow-missing-source-root=false --allow-already-existing -- E:\advance_javascript\codeQL\7\csharp-queries\csharp-db
[2025-06-03 12:46:10] Exception caught at top level: Invalid source root: E:\advance_javascript\codeQL\7\csharp-queries\CSharpTest.
                      com.semmle.cli2.database.InitCommand.executeSubcommand(InitCommand.java:170)
                      com.semmle.cli2.picocli.SubcommandCommon.lambda$executeSubcommandWithMessages$5(SubcommandCommon.java:892)
                      com.semmle.cli2.picocli.SubcommandCommon.withCompilationMessages(SubcommandCommon.java:444)
                      com.semmle.cli2.picocli.SubcommandCommon.executeSubcommandWithMessages(SubcommandCommon.java:890)
                      com.semmle.cli2.picocli.PlumbingRunner.run(PlumbingRunner.java:119)
                      com.semmle.cli2.picocli.SubcommandCommon.runPlumbingInProcess(SubcommandCommon.java:201)
                      com.semmle.cli2.database.CreateCommand.executeSubcommand(CreateCommand.java:113)
                      com.semmle.cli2.picocli.SubcommandCommon.lambda$executeSubcommandWithMessages$5(SubcommandCommon.java:892)
                      com.semmle.cli2.picocli.SubcommandCommon.withCompilationMessages(SubcommandCommon.java:444)
                      com.semmle.cli2.picocli.SubcommandCommon.executeSubcommandWithMessages(SubcommandCommon.java:890)
                      com.semmle.cli2.picocli.SubcommandCommon.toplevelMain(SubcommandCommon.java:777)
                      com.semmle.cli2.picocli.SubcommandCommon.call(SubcommandCommon.java:757)
                      com.semmle.cli2.picocli.SubcommandMaker.runMain(SubcommandMaker.java:238)
                      com.semmle.cli2.picocli.SubcommandMaker.runMain(SubcommandMaker.java:259)
                      com.semmle.cli2.CodeQL.main(CodeQL.java:115)
