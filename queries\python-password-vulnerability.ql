/**
 * @name Hardcoded passwords and sensitive credentials v2
 * @description Finds hardcoded passwords, API keys, and other sensitive credentials in Python code.
 * @kind problem
 * @problem.severity error
 * @id py/hardcoded-credentials
 */

import python

from Assign assign, StringLiteral value, string varName
where
  assign.getTarget(0).(Name).getId() = varName and
  assign.getValue() = value and
  (
    varName.toLowerCase().matches("%password%") or
    varName.toLowerCase().matches("%passwd%") or
    varName.toLowerCase().matches("%pwd%") or
    varName.toLowerCase().matches("%secret%") or
    varName.toLowerCase().matches("%key%") or
    varName.toLowerCase().matches("%token%") or
    varName.toLowerCase().matches("%api_key%") or
    varName.toLowerCase().matches("%apikey%")
  ) and
  value.getText().length() > 3 and
  not value.getText() = "" and
  not value.getText().matches("%<%") and  // Exclude template placeholders like <password>
  not value.getText().matches("%{%") and  // Exclude template placeholders like {password}
  not value.getText().matches("your_%") and // Exclude placeholder text
  not value.getText().matches("enter_%") and // Exclude placeholder text
  not value.getText().matches("example_%") and   // Exclude example text
  not value.getText().matches("%:%") and         // Exclude prompts ending with colon
  not value.getText() = "" and                   // Exclude empty strings
  not (value.getText().matches("%enter%") and value.getText().matches("%:%")) // Exclude "Enter your password:" type prompts
select assign, "Hardcoded credential found in variable '" + varName + "' - this poses a security risk."
